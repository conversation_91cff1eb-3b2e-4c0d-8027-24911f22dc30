<?php

namespace frontend\controllers;

use frontend\controllers\MyController;
use frontend\interfaces\CenterInterface;
use frontend\services\InstrumentServer;
use yii\helpers\ArrayHelper;

class InstrumentBookingController extends MyController
{
    public function actionGetInstrumentsData()
    {
        $postData = \Yii::$app->getRequest()->post();

        // 用于查询我加入的鹰群或部门
        $currentUserGroupIds = array_column($this->userinfo->groups, 'id');
        $currentUserDepartmentIds = array_column($this->userinfo->department, 'id');
        $where['groups'] = $currentUserGroupIds;
        $where['departments'] = $currentUserDepartmentIds;

        // 获取查看权限
        // $userAuth = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id);
        if (\Yii::$app->view->params['instruments_manage_read']) {
            $where['viewAuth'] = '1'; // 能查看所有仪器
        } else {
            $where['viewAuth'] = '2'; // 能查看可见鹰群和部门仪器
        }
        if (!\Yii::$app->view->params['instruments_manage_read'] && !\Yii::$app->view->params['instruments_manage_read_my']) {
            return $this->fail(\Yii::t('base', 'no_power')); // 没有查看仪器的权限
        }
        // 获取查询条件
        $curPage = isset($postData['curPage']) ? $postData['curPage'] : 1;
        $limit = isset($postData['pageSize']) ? $postData['pageSize'] : \Yii::$app->params['default_page_size'];
        $where['instrumentName'] = isset($postData['instrumentName']) ? $postData['instrumentName'] : '';
        $where['bookTime'] = isset($postData['bookTime']) ? $postData['bookTime'] : '';
        $where['onlyBooked'] = isset($postData['onlyBooked']) ? $postData['onlyBooked'] : false;
        $where['lang'] = isset($postData['lang']) ? $postData['onlyBooked'] : 'cn';
        // 获取查询的数据
        $data = (new InstrumentServer())->queryInstrumentsWithBooking($where, $limit, $curPage);
        return $this->success($data);
    }
}