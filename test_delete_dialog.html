<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除确认对话框</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 50px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* 删除确认对话框样式 */
        .delete-booking-dialog {
            border-radius: 12px !important;
            padding: 0 !important;
        }

        .delete-booking-dialog .el-dialog__header {
            padding: 24px 24px 0 24px !important;
            margin-right: 0 !important;
        }

        .delete-booking-dialog .el-dialog__title {
            font-size: 16px !important;
            font-weight: 500 !important;
            color: #303133 !important;
            line-height: 24px !important;
        }

        .delete-booking-dialog .el-dialog__body {
            padding: 16px 24px 0 24px !important;
        }

        .delete-booking-dialog .el-dialog__footer {
            padding: 24px 24px 24px 24px !important;
        }

        .delete-dialog-content {
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .delete-dialog-icon {
            flex-shrink: 0;
            margin-top: 2px;
        }

        .warning-icon {
            font-size: 20px;
            color: #FF6B47;
        }

        .delete-dialog-text {
            flex: 1;
            font-size: 14px;
            line-height: 20px;
            color: #606266;
        }

        .delete-dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .delete-dialog-footer .el-button {
            padding: 8px 16px !important;
            border-radius: 6px !important;
            font-size: 14px !important;
        }

        .delete-dialog-footer .el-button--default {
            color: #606266 !important;
            border-color: #DCDFE6 !important;
            background-color: #FFFFFF !important;
        }

        .delete-dialog-footer .el-button--primary {
            background-color: #FF6B47 !important;
            border-color: #FF6B47 !important;
            color: #FFFFFF !important;
        }

        .delete-dialog-footer .el-button--primary:hover {
            background-color: #FF5722 !important;
            border-color: #FF5722 !important;
        }

        .delete-booking-dialog .el-dialog__close {
            color: #C0C4CC !important;
            font-size: 16px !important;
        }

        .delete-booking-dialog .el-dialog__close:hover {
            color: #909399 !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h2>删除确认对话框测试</h2>
            <p>点击下面的按钮测试删除确认对话框的样式和功能：</p>
            
            <el-button type="danger" @click="showDeleteDialog">
                测试删除对话框
            </el-button>
            
            <div style="margin-top: 20px;">
                <h3>对话框信息：</h3>
                <p><strong>实验者：</strong>天平</p>
                <p><strong>时间：</strong>2025年1月13日 9:00 - 12:00</p>
                <p><strong>操作：</strong>撤销预约</p>
            </div>
        </div>

        <!-- 删除确认对话框 -->
        <el-dialog
            v-model="deleteDialogVisible"
            title="确定要撤销此预约吗"
            width="400px"
            :show-close="true"
            :close-on-click-modal="false"
            :close-on-press-escape="true"
            custom-class="delete-booking-dialog"
            align-center
        >
            <div class="delete-dialog-content">
                <div class="delete-dialog-icon">
                    <el-icon class="warning-icon">
                        <WarningFilled />
                    </el-icon>
                </div>
                <div class="delete-dialog-text">
                    撤销对 天平 于 2025年1月13日 9:00 - 12:00 的预约
                </div>
            </div>
            
            <template #footer>
                <div class="delete-dialog-footer">
                    <el-button @click="deleteDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmDelete">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref } = Vue;
        const { ElButton, ElDialog, ElIcon, ElMessage } = ElementPlus;
        const { WarningFilled } = ElementPlusIconsVue;

        createApp({
            components: {
                ElButton,
                ElDialog,
                ElIcon,
                WarningFilled
            },
            setup() {
                const deleteDialogVisible = ref(false);

                const showDeleteDialog = () => {
                    deleteDialogVisible.value = true;
                };

                const confirmDelete = () => {
                    // 模拟删除操作
                    ElMessage({
                        message: '预约已成功撤销',
                        type: 'success'
                    });
                    deleteDialogVisible.value = false;
                };

                return {
                    deleteDialogVisible,
                    showDeleteDialog,
                    confirmDelete
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
