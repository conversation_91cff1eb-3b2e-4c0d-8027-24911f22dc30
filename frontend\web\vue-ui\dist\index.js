import { ad as d, r as f, ae as E, af as L, d as V, ag as A, ah as F, ai as G, aj as K, e as _, ak as w, al as y, M as R, c as W, am as p } from "./el-input.js";
import { watch as q, unref as N } from "vue";
var h = d(f, "WeakMap");
function H(t) {
  return t != null && E(t.length) && !L(t);
}
var Y = Object.prototype;
function Z(t) {
  var r = t && t.constructor, e = typeof r == "function" && r.prototype || Y;
  return t === e;
}
function J(t, r) {
  for (var e = -1, n = Array(t); ++e < t; )
    n[e] = r(e);
  return n;
}
function Q() {
  return !1;
}
var z = typeof exports == "object" && exports && !exports.nodeType && exports, x = z && typeof module == "object" && module && !module.nodeType && module, X = x && x.exports === z, S = X ? f.Buffer : void 0, tt = S ? S.isBuffer : void 0, et = tt || Q, rt = "[object Arguments]", at = "[object Array]", nt = "[object Boolean]", ot = "[object Date]", st = "[object Error]", it = "[object Function]", ut = "[object Map]", ct = "[object Number]", ft = "[object Object]", pt = "[object RegExp]", gt = "[object Set]", bt = "[object String]", lt = "[object WeakMap]", yt = "[object ArrayBuffer]", dt = "[object DataView]", vt = "[object Float32Array]", ht = "[object Float64Array]", jt = "[object Int8Array]", Tt = "[object Int16Array]", mt = "[object Int32Array]", At = "[object Uint8Array]", _t = "[object Uint8ClampedArray]", wt = "[object Uint16Array]", xt = "[object Uint32Array]", a = {};
a[vt] = a[ht] = a[jt] = a[Tt] = a[mt] = a[At] = a[_t] = a[wt] = a[xt] = !0;
a[rt] = a[at] = a[yt] = a[nt] = a[dt] = a[ot] = a[st] = a[it] = a[ut] = a[ct] = a[ft] = a[pt] = a[gt] = a[bt] = a[lt] = !1;
function St(t) {
  return V(t) && E(t.length) && !!a[A(t)];
}
function Ot(t) {
  return function(r) {
    return t(r);
  };
}
var U = typeof exports == "object" && exports && !exports.nodeType && exports, g = U && typeof module == "object" && module && !module.nodeType && module, Pt = g && g.exports === U, v = Pt && F.process, O = function() {
  try {
    var t = g && g.require && g.require("util").types;
    return t || v && v.binding && v.binding("util");
  } catch {
  }
}(), P = O && O.isTypedArray, Mt = P ? Ot(P) : St, It = Object.prototype, $t = It.hasOwnProperty;
function Ct(t, r) {
  var e = _(t), n = !e && G(t), i = !e && !n && et(t), u = !e && !n && !i && Mt(t), s = e || n || i || u, l = s ? J(t.length, String) : [], D = l.length;
  for (var o in t)
    (r || $t.call(t, o)) && !(s && // Safari 9 has enumerable `arguments.length` in strict mode.
    (o == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
    i && (o == "offset" || o == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
    u && (o == "buffer" || o == "byteLength" || o == "byteOffset") || // Skip index properties.
    K(o, D))) && l.push(o);
  return l;
}
function kt(t, r) {
  return function(e) {
    return t(r(e));
  };
}
var Bt = kt(Object.keys, Object), Et = Object.prototype, zt = Et.hasOwnProperty;
function Ut(t) {
  if (!Z(t))
    return Bt(t);
  var r = [];
  for (var e in Object(t))
    zt.call(t, e) && e != "constructor" && r.push(e);
  return r;
}
function Dt(t) {
  return H(t) ? Ct(t) : Ut(t);
}
function oe() {
  if (!arguments.length)
    return [];
  var t = arguments[0];
  return _(t) ? t : [t];
}
function Lt() {
  this.__data__ = new w(), this.size = 0;
}
function Vt(t) {
  var r = this.__data__, e = r.delete(t);
  return this.size = r.size, e;
}
function Ft(t) {
  return this.__data__.get(t);
}
function Gt(t) {
  return this.__data__.has(t);
}
var Kt = 200;
function Rt(t, r) {
  var e = this.__data__;
  if (e instanceof w) {
    var n = e.__data__;
    if (!y || n.length < Kt - 1)
      return n.push([t, r]), this.size = ++e.size, this;
    e = this.__data__ = new R(n);
  }
  return e.set(t, r), this.size = e.size, this;
}
function b(t) {
  var r = this.__data__ = new w(t);
  this.size = r.size;
}
b.prototype.clear = Lt;
b.prototype.delete = Vt;
b.prototype.get = Ft;
b.prototype.has = Gt;
b.prototype.set = Rt;
function Wt(t, r) {
  for (var e = -1, n = t == null ? 0 : t.length, i = 0, u = []; ++e < n; ) {
    var s = t[e];
    r(s, e, t) && (u[i++] = s);
  }
  return u;
}
function qt() {
  return [];
}
var Nt = Object.prototype, Ht = Nt.propertyIsEnumerable, M = Object.getOwnPropertySymbols, Yt = M ? function(t) {
  return t == null ? [] : (t = Object(t), Wt(M(t), function(r) {
    return Ht.call(t, r);
  }));
} : qt;
function Zt(t, r, e) {
  var n = r(t);
  return _(t) ? n : W(n, e(t));
}
function se(t) {
  return Zt(t, Dt, Yt);
}
var j = d(f, "DataView"), T = d(f, "Promise"), m = d(f, "Set"), I = "[object Map]", Jt = "[object Object]", $ = "[object Promise]", C = "[object Set]", k = "[object WeakMap]", B = "[object DataView]", Qt = p(j), Xt = p(y), te = p(T), ee = p(m), re = p(h), c = A;
(j && c(new j(new ArrayBuffer(1))) != B || y && c(new y()) != I || T && c(T.resolve()) != $ || m && c(new m()) != C || h && c(new h()) != k) && (c = function(t) {
  var r = A(t), e = r == Jt ? t.constructor : void 0, n = e ? p(e) : "";
  if (n)
    switch (n) {
      case Qt:
        return B;
      case Xt:
        return I;
      case te:
        return $;
      case ee:
        return C;
      case re:
        return k;
    }
  return r;
});
var ie = f.Uint8Array;
const ue = ({ from: t, replacement: r, scope: e, version: n, ref: i, type: u = "API" }, s) => {
  q(() => N(s), (l) => {
  }, {
    immediate: !0
  });
};
export {
  b as S,
  ie as U,
  Ct as a,
  H as b,
  c,
  Ot as d,
  et as e,
  se as f,
  Yt as g,
  oe as h,
  Z as i,
  Mt as j,
  Dt as k,
  O as n,
  kt as o,
  qt as s,
  ue as u
};
