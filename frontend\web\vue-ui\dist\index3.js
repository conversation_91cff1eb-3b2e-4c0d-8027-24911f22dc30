import { aS as Jn, i as ct, r as Qn, h as U, _ as X, n as ce, A as eo, aT as ft, O as ue, P as le, T as gt, j as L, F as Wt, B as to, Y as mn, w as nt, ay as dt, aU as gn, X as he, aR as ht, q as no, aO as Ut, aH as oo, z as Kt, aV as ro, aW as hn, y as Ve, s as bn, p as ao, aX as so, aY as io, aZ as lo, az as uo, C as co } from "./el-input.js";
import { defineComponent as B, inject as se, ref as A, computed as P, onBeforeUnmount as ie, toRef as ge, createBlock as ee, openBlock as H, Transition as yn, unref as v, withCtx as Y, withDirectives as bt, createElementVNode as pt, withModifiers as fo, normalizeClass as be, normalizeStyle as Qe, vShow as wn, createElementBlock as Re, Fragment as En, createVNode as we, watch as M, nextTick as et, provide as Ee, reactive as po, onActivated as vo, onMounted as fe, onUpdated as mo, createCommentVNode as De, resolveDynamicComponent as go, renderSlot as G, cloneVNode as ho, Text as bo, Comment as yo, mergeProps as yt, shallowRef as wo, getCurrentInstance as Eo, Teleport as To, onBeforeMount as Oo, readonly as So, onDeactivated as Co, toDisplayString as Ro } from "vue";
var Po = /\s/;
function Ao(e) {
  for (var t = e.length; t-- && Po.test(e.charAt(t)); )
    ;
  return t;
}
var _o = /^\s+/;
function xo(e) {
  return e && e.slice(0, Ao(e) + 1).replace(_o, "");
}
var qt = NaN, Fo = /^[-+]0x[0-9a-f]+$/i, Lo = /^0b[01]+$/i, ko = /^0o[0-7]+$/i, Io = parseInt;
function Vt(e) {
  if (typeof e == "number")
    return e;
  if (Jn(e))
    return qt;
  if (ct(e)) {
    var t = typeof e.valueOf == "function" ? e.valueOf() : e;
    e = ct(t) ? t + "" : t;
  }
  if (typeof e != "string")
    return e === 0 ? e : +e;
  e = xo(e);
  var n = Lo.test(e);
  return n || ko.test(e) ? Io(e.slice(2), n ? 2 : 8) : Fo.test(e) ? qt : +e;
}
var it = function() {
  return Qn.Date.now();
}, Mo = "Expected a function", Bo = Math.max, No = Math.min;
function ys(e, t, n) {
  var o, r, a, i, s, l, u = 0, d = !1, f = !1, g = !0;
  if (typeof e != "function")
    throw new TypeError(Mo);
  t = Vt(t) || 0, ct(n) && (d = !!n.leading, f = "maxWait" in n, a = f ? Bo(Vt(n.maxWait) || 0, t) : a, g = "trailing" in n ? !!n.trailing : g);
  function h(w) {
    var O = o, C = r;
    return o = r = void 0, u = w, i = e.apply(C, O), i;
  }
  function c(w) {
    return u = w, s = setTimeout(E, t), d ? h(w) : i;
  }
  function p(w) {
    var O = w - l, C = w - u, x = t - O;
    return f ? No(x, a - C) : x;
  }
  function T(w) {
    var O = w - l, C = w - u;
    return l === void 0 || O >= t || O < 0 || f && C >= a;
  }
  function E() {
    var w = it();
    if (T(w))
      return S(w);
    s = setTimeout(E, p(w));
  }
  function S(w) {
    return s = void 0, g && o ? h(w) : (o = r = void 0, i);
  }
  function m() {
    s !== void 0 && clearTimeout(s), u = 0, o = l = r = s = void 0;
  }
  function y() {
    return s === void 0 ? i : S(it());
  }
  function b() {
    var w = it(), O = T(w);
    if (o = arguments, r = this, l = w, O) {
      if (s === void 0)
        return c(l);
      if (f)
        return clearTimeout(s), s = setTimeout(E, t), h(l);
    }
    return s === void 0 && (s = setTimeout(E, t)), i;
  }
  return b.cancel = m, b.flush = y, b;
}
function jo(e) {
  return e === void 0;
}
const Oe = 4, Do = {
  vertical: {
    offset: "offsetHeight",
    scroll: "scrollTop",
    scrollSize: "scrollHeight",
    size: "height",
    key: "vertical",
    axis: "Y",
    client: "clientY",
    direction: "top"
  },
  horizontal: {
    offset: "offsetWidth",
    scroll: "scrollLeft",
    scrollSize: "scrollWidth",
    size: "width",
    key: "horizontal",
    axis: "X",
    client: "clientX",
    direction: "left"
  }
}, $o = ({
  move: e,
  size: t,
  bar: n
}) => ({
  [n.size]: t,
  transform: `translate${n.axis}(${e}%)`
}), wt = Symbol("scrollbarContextKey"), Ho = U({
  vertical: Boolean,
  size: String,
  move: Number,
  ratio: {
    type: Number,
    required: !0
  },
  always: Boolean
}), zo = "Thumb", Wo = /* @__PURE__ */ B({
  __name: "thumb",
  props: Ho,
  setup(e) {
    const t = e, n = se(wt), o = ce("scrollbar");
    n || eo(zo, "can not inject scrollbar context");
    const r = A(), a = A(), i = A({}), s = A(!1);
    let l = !1, u = !1, d = ue ? document.onselectstart : null;
    const f = P(() => Do[t.vertical ? "vertical" : "horizontal"]), g = P(() => $o({
      size: t.size,
      move: t.move,
      bar: f.value
    })), h = P(() => r.value[f.value.offset] ** 2 / n.wrapElement[f.value.scrollSize] / t.ratio / a.value[f.value.offset]), c = (w) => {
      var O;
      if (w.stopPropagation(), w.ctrlKey || [1, 2].includes(w.button))
        return;
      (O = window.getSelection()) == null || O.removeAllRanges(), T(w);
      const C = w.currentTarget;
      C && (i.value[f.value.axis] = C[f.value.offset] - (w[f.value.client] - C.getBoundingClientRect()[f.value.direction]));
    }, p = (w) => {
      if (!a.value || !r.value || !n.wrapElement)
        return;
      const O = Math.abs(w.target.getBoundingClientRect()[f.value.direction] - w[f.value.client]), C = a.value[f.value.offset] / 2, x = (O - C) * 100 * h.value / r.value[f.value.offset];
      n.wrapElement[f.value.scroll] = x * n.wrapElement[f.value.scrollSize] / 100;
    }, T = (w) => {
      w.stopImmediatePropagation(), l = !0, document.addEventListener("mousemove", E), document.addEventListener("mouseup", S), d = document.onselectstart, document.onselectstart = () => !1;
    }, E = (w) => {
      if (!r.value || !a.value || l === !1)
        return;
      const O = i.value[f.value.axis];
      if (!O)
        return;
      const C = (r.value.getBoundingClientRect()[f.value.direction] - w[f.value.client]) * -1, x = a.value[f.value.offset] - O, _ = (C - x) * 100 * h.value / r.value[f.value.offset];
      n.wrapElement[f.value.scroll] = _ * n.wrapElement[f.value.scrollSize] / 100;
    }, S = () => {
      l = !1, i.value[f.value.axis] = 0, document.removeEventListener("mousemove", E), document.removeEventListener("mouseup", S), b(), u && (s.value = !1);
    }, m = () => {
      u = !1, s.value = !!t.size;
    }, y = () => {
      u = !0, s.value = l;
    };
    ie(() => {
      b(), document.removeEventListener("mouseup", S);
    });
    const b = () => {
      document.onselectstart !== d && (document.onselectstart = d);
    };
    return ft(ge(n, "scrollbarElement"), "mousemove", m), ft(ge(n, "scrollbarElement"), "mouseleave", y), (w, O) => (H(), ee(yn, {
      name: v(o).b("fade"),
      persisted: ""
    }, {
      default: Y(() => [
        bt(pt("div", {
          ref_key: "instance",
          ref: r,
          class: be([v(o).e("bar"), v(o).is(v(f).key)]),
          onMousedown: p,
          onClick: fo(() => {
          }, ["stop"])
        }, [
          pt("div", {
            ref_key: "thumb",
            ref: a,
            class: be(v(o).e("thumb")),
            style: Qe(v(g)),
            onMousedown: c
          }, null, 38)
        ], 42, ["onClick"]), [
          [wn, w.always || s.value]
        ])
      ]),
      _: 1
    }, 8, ["name"]));
  }
});
var Xt = /* @__PURE__ */ X(Wo, [["__file", "thumb.vue"]]);
const Uo = U({
  always: {
    type: Boolean,
    default: !0
  },
  minSize: {
    type: Number,
    required: !0
  }
}), Ko = /* @__PURE__ */ B({
  __name: "bar",
  props: Uo,
  setup(e, { expose: t }) {
    const n = e, o = se(wt), r = A(0), a = A(0), i = A(""), s = A(""), l = A(1), u = A(1);
    return t({
      handleScroll: (g) => {
        if (g) {
          const h = g.offsetHeight - Oe, c = g.offsetWidth - Oe;
          a.value = g.scrollTop * 100 / h * l.value, r.value = g.scrollLeft * 100 / c * u.value;
        }
      },
      update: () => {
        const g = o == null ? void 0 : o.wrapElement;
        if (!g)
          return;
        const h = g.offsetHeight - Oe, c = g.offsetWidth - Oe, p = h ** 2 / g.scrollHeight, T = c ** 2 / g.scrollWidth, E = Math.max(p, n.minSize), S = Math.max(T, n.minSize);
        l.value = p / (h - p) / (E / (h - E)), u.value = T / (c - T) / (S / (c - S)), s.value = E + Oe < h ? `${E}px` : "", i.value = S + Oe < c ? `${S}px` : "";
      }
    }), (g, h) => (H(), Re(En, null, [
      we(Xt, {
        move: r.value,
        ratio: u.value,
        size: i.value,
        always: g.always
      }, null, 8, ["move", "ratio", "size", "always"]),
      we(Xt, {
        move: a.value,
        ratio: l.value,
        size: s.value,
        vertical: "",
        always: g.always
      }, null, 8, ["move", "ratio", "size", "always"])
    ], 64));
  }
});
var qo = /* @__PURE__ */ X(Ko, [["__file", "bar.vue"]]);
const Vo = U({
  height: {
    type: [String, Number],
    default: ""
  },
  maxHeight: {
    type: [String, Number],
    default: ""
  },
  native: {
    type: Boolean,
    default: !1
  },
  wrapStyle: {
    type: L([String, Object, Array]),
    default: ""
  },
  wrapClass: {
    type: [String, Array],
    default: ""
  },
  viewClass: {
    type: [String, Array],
    default: ""
  },
  viewStyle: {
    type: [String, Array, Object],
    default: ""
  },
  noresize: Boolean,
  tag: {
    type: String,
    default: "div"
  },
  always: Boolean,
  minSize: {
    type: Number,
    default: 20
  },
  tabindex: {
    type: [String, Number],
    default: void 0
  },
  id: String,
  role: String,
  ...gt(["ariaLabel", "ariaOrientation"])
}), Xo = {
  scroll: ({
    scrollTop: e,
    scrollLeft: t
  }) => [e, t].every(le)
}, Yo = "ElScrollbar", Go = B({
  name: Yo
}), Zo = /* @__PURE__ */ B({
  ...Go,
  props: Vo,
  emits: Xo,
  setup(e, { expose: t, emit: n }) {
    const o = e, r = ce("scrollbar");
    let a, i, s = 0, l = 0;
    const u = A(), d = A(), f = A(), g = A(), h = P(() => {
      const b = {};
      return o.height && (b.height = Wt(o.height)), o.maxHeight && (b.maxHeight = Wt(o.maxHeight)), [o.wrapStyle, b];
    }), c = P(() => [
      o.wrapClass,
      r.e("wrap"),
      { [r.em("wrap", "hidden-default")]: !o.native }
    ]), p = P(() => [r.e("view"), o.viewClass]), T = () => {
      var b;
      d.value && ((b = g.value) == null || b.handleScroll(d.value), s = d.value.scrollTop, l = d.value.scrollLeft, n("scroll", {
        scrollTop: d.value.scrollTop,
        scrollLeft: d.value.scrollLeft
      }));
    };
    function E(b, w) {
      mn(b) ? d.value.scrollTo(b) : le(b) && le(w) && d.value.scrollTo(b, w);
    }
    const S = (b) => {
      le(b) && (d.value.scrollTop = b);
    }, m = (b) => {
      le(b) && (d.value.scrollLeft = b);
    }, y = () => {
      var b;
      (b = g.value) == null || b.update();
    };
    return M(() => o.noresize, (b) => {
      b ? (a == null || a(), i == null || i()) : ({ stop: a } = to(f, y), i = ft("resize", y));
    }, { immediate: !0 }), M(() => [o.maxHeight, o.height], () => {
      o.native || et(() => {
        var b;
        y(), d.value && ((b = g.value) == null || b.handleScroll(d.value));
      });
    }), Ee(wt, po({
      scrollbarElement: u,
      wrapElement: d
    })), vo(() => {
      d.value && (d.value.scrollTop = s, d.value.scrollLeft = l);
    }), fe(() => {
      o.native || et(() => {
        y();
      });
    }), mo(() => y()), t({
      wrapRef: d,
      update: y,
      scrollTo: E,
      setScrollTop: S,
      setScrollLeft: m,
      handleScroll: T
    }), (b, w) => (H(), Re("div", {
      ref_key: "scrollbarRef",
      ref: u,
      class: be(v(r).b())
    }, [
      pt("div", {
        ref_key: "wrapRef",
        ref: d,
        class: be(v(c)),
        style: Qe(v(h)),
        tabindex: b.tabindex,
        onScroll: T
      }, [
        (H(), ee(go(b.tag), {
          id: b.id,
          ref_key: "resizeRef",
          ref: f,
          class: be(v(p)),
          style: Qe(b.viewStyle),
          role: b.role,
          "aria-label": b.ariaLabel,
          "aria-orientation": b.ariaOrientation
        }, {
          default: Y(() => [
            G(b.$slots, "default")
          ]),
          _: 3
        }, 8, ["id", "class", "style", "role", "aria-label", "aria-orientation"]))
      ], 46, ["tabindex"]),
      b.native ? De("v-if", !0) : (H(), ee(qo, {
        key: 0,
        ref_key: "barRef",
        ref: g,
        always: b.always,
        "min-size": b.minSize
      }, null, 8, ["always", "min-size"]))
    ], 2));
  }
});
var Jo = /* @__PURE__ */ X(Zo, [["__file", "scrollbar.vue"]]);
const ws = nt(Jo), Et = Symbol("popper"), Tn = Symbol("popperContent"), Qo = [
  "dialog",
  "grid",
  "group",
  "listbox",
  "menu",
  "navigation",
  "tooltip",
  "tree"
], On = U({
  role: {
    type: String,
    values: Qo,
    default: "tooltip"
  }
}), er = B({
  name: "ElPopper",
  inheritAttrs: !1
}), tr = /* @__PURE__ */ B({
  ...er,
  props: On,
  setup(e, { expose: t }) {
    const n = e, o = A(), r = A(), a = A(), i = A(), s = P(() => n.role), l = {
      triggerRef: o,
      popperInstanceRef: r,
      contentRef: a,
      referenceRef: i,
      role: s
    };
    return t(l), Ee(Et, l), (u, d) => G(u.$slots, "default");
  }
});
var nr = /* @__PURE__ */ X(tr, [["__file", "popper.vue"]]);
const Sn = U({
  arrowOffset: {
    type: Number,
    default: 5
  }
}), or = B({
  name: "ElPopperArrow",
  inheritAttrs: !1
}), rr = /* @__PURE__ */ B({
  ...or,
  props: Sn,
  setup(e, { expose: t }) {
    const n = e, o = ce("popper"), { arrowOffset: r, arrowRef: a, arrowStyle: i } = se(Tn, void 0);
    return M(() => n.arrowOffset, (s) => {
      r.value = s;
    }), ie(() => {
      a.value = void 0;
    }), t({
      arrowRef: a
    }), (s, l) => (H(), Re("span", {
      ref_key: "arrowRef",
      ref: a,
      class: be(v(o).e("arrow")),
      style: Qe(v(i)),
      "data-popper-arrow": ""
    }, null, 6));
  }
});
var ar = /* @__PURE__ */ X(rr, [["__file", "arrow.vue"]]);
const Cn = U({
  virtualRef: {
    type: L(Object)
  },
  virtualTriggering: Boolean,
  onMouseenter: {
    type: L(Function)
  },
  onMouseleave: {
    type: L(Function)
  },
  onClick: {
    type: L(Function)
  },
  onKeydown: {
    type: L(Function)
  },
  onFocus: {
    type: L(Function)
  },
  onBlur: {
    type: L(Function)
  },
  onContextmenu: {
    type: L(Function)
  },
  id: String,
  open: Boolean
}), Rn = Symbol("elForwardRef"), sr = (e) => {
  Ee(Rn, {
    setForwardRef: (n) => {
      e.value = n;
    }
  });
}, ir = (e) => ({
  mounted(t) {
    e(t);
  },
  updated(t) {
    e(t);
  },
  unmounted() {
    e(null);
  }
}), vt = (e) => {
  if (e.tabIndex > 0 || e.tabIndex === 0 && e.getAttribute("tabIndex") !== null)
    return !0;
  if (e.tabIndex < 0 || e.hasAttribute("disabled") || e.getAttribute("aria-disabled") === "true")
    return !1;
  switch (e.nodeName) {
    case "A":
      return !!e.href && e.rel !== "ignore";
    case "INPUT":
      return !(e.type === "hidden" || e.type === "file");
    case "BUTTON":
    case "SELECT":
    case "TEXTAREA":
      return !0;
    default:
      return !1;
  }
}, lr = "ElOnlyChild", ur = B({
  name: lr,
  setup(e, {
    slots: t,
    attrs: n
  }) {
    var o;
    const r = se(Rn), a = ir((o = r == null ? void 0 : r.setForwardRef) != null ? o : dt);
    return () => {
      var i;
      const s = (i = t.default) == null ? void 0 : i.call(t, n);
      if (!s || s.length > 1)
        return null;
      const l = Pn(s);
      return l ? bt(ho(l, n), [[a]]) : null;
    };
  }
});
function Pn(e) {
  if (!e)
    return null;
  const t = e;
  for (const n of t) {
    if (mn(n))
      switch (n.type) {
        case yo:
          continue;
        case bo:
        case "svg":
          return Yt(n);
        case En:
          return Pn(n.children);
        default:
          return n;
      }
    return Yt(n);
  }
  return null;
}
function Yt(e) {
  const t = ce("only-child");
  return we("span", {
    class: t.e("content")
  }, [e]);
}
const cr = B({
  name: "ElPopperTrigger",
  inheritAttrs: !1
}), fr = /* @__PURE__ */ B({
  ...cr,
  props: Cn,
  setup(e, { expose: t }) {
    const n = e, { role: o, triggerRef: r } = se(Et, void 0);
    sr(r);
    const a = P(() => s.value ? n.id : void 0), i = P(() => {
      if (o && o.value === "tooltip")
        return n.open && n.id ? n.id : void 0;
    }), s = P(() => {
      if (o && o.value !== "tooltip")
        return o.value;
    }), l = P(() => s.value ? `${n.open}` : void 0);
    let u;
    const d = [
      "onMouseenter",
      "onMouseleave",
      "onClick",
      "onKeydown",
      "onFocus",
      "onBlur",
      "onContextmenu"
    ];
    return fe(() => {
      M(() => n.virtualRef, (f) => {
        f && (r.value = gn(f));
      }, {
        immediate: !0
      }), M(r, (f, g) => {
        u == null || u(), u = void 0, he(f) && (d.forEach((h) => {
          var c;
          const p = n[h];
          p && (f.addEventListener(h.slice(2).toLowerCase(), p), (c = g == null ? void 0 : g.removeEventListener) == null || c.call(g, h.slice(2).toLowerCase(), p));
        }), vt(f) && (u = M([a, i, s, l], (h) => {
          [
            "aria-controls",
            "aria-describedby",
            "aria-haspopup",
            "aria-expanded"
          ].forEach((c, p) => {
            ht(h[p]) ? f.removeAttribute(c) : f.setAttribute(c, h[p]);
          });
        }, { immediate: !0 }))), he(g) && vt(g) && [
          "aria-controls",
          "aria-describedby",
          "aria-haspopup",
          "aria-expanded"
        ].forEach((h) => g.removeAttribute(h));
      }, {
        immediate: !0
      });
    }), ie(() => {
      if (u == null || u(), u = void 0, r.value && he(r.value)) {
        const f = r.value;
        d.forEach((g) => {
          const h = n[g];
          h && f.removeEventListener(g.slice(2).toLowerCase(), h);
        }), r.value = void 0;
      }
    }), t({
      triggerRef: r
    }), (f, g) => f.virtualTriggering ? De("v-if", !0) : (H(), ee(v(ur), yt({ key: 0 }, f.$attrs, {
      "aria-controls": v(a),
      "aria-describedby": v(i),
      "aria-expanded": v(l),
      "aria-haspopup": v(s)
    }), {
      default: Y(() => [
        G(f.$slots, "default")
      ]),
      _: 3
    }, 16, ["aria-controls", "aria-describedby", "aria-expanded", "aria-haspopup"]));
  }
});
var dr = /* @__PURE__ */ X(fr, [["__file", "trigger.vue"]]);
const lt = "focus-trap.focus-after-trapped", ut = "focus-trap.focus-after-released", pr = "focus-trap.focusout-prevented", Gt = {
  cancelable: !0,
  bubbles: !1
}, vr = {
  cancelable: !0,
  bubbles: !1
}, Zt = "focusAfterTrapped", Jt = "focusAfterReleased", mr = Symbol("elFocusTrap"), Tt = A(), ot = A(0), Ot = A(0);
let Xe = 0;
const An = (e) => {
  const t = [], n = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
    acceptNode: (o) => {
      const r = o.tagName === "INPUT" && o.type === "hidden";
      return o.disabled || o.hidden || r ? NodeFilter.FILTER_SKIP : o.tabIndex >= 0 || o === document.activeElement ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
    }
  });
  for (; n.nextNode(); )
    t.push(n.currentNode);
  return t;
}, Qt = (e, t) => {
  for (const n of e)
    if (!gr(n, t))
      return n;
}, gr = (e, t) => {
  if (getComputedStyle(e).visibility === "hidden")
    return !0;
  for (; e; ) {
    if (t && e === t)
      return !1;
    if (getComputedStyle(e).display === "none")
      return !0;
    e = e.parentElement;
  }
  return !1;
}, hr = (e) => {
  const t = An(e), n = Qt(t, e), o = Qt(t.reverse(), e);
  return [n, o];
}, br = (e) => e instanceof HTMLInputElement && "select" in e, oe = (e, t) => {
  if (e && e.focus) {
    const n = document.activeElement;
    let o = !1;
    he(e) && !vt(e) && !e.getAttribute("tabindex") && (e.setAttribute("tabindex", "-1"), o = !0), e.focus({ preventScroll: !0 }), Ot.value = window.performance.now(), e !== n && br(e) && t && e.select(), he(e) && o && e.removeAttribute("tabindex");
  }
};
function en(e, t) {
  const n = [...e], o = e.indexOf(t);
  return o !== -1 && n.splice(o, 1), n;
}
const yr = () => {
  let e = [];
  return {
    push: (o) => {
      const r = e[0];
      r && o !== r && r.pause(), e = en(e, o), e.unshift(o);
    },
    remove: (o) => {
      var r, a;
      e = en(e, o), (a = (r = e[0]) == null ? void 0 : r.resume) == null || a.call(r);
    }
  };
}, wr = (e, t = !1) => {
  const n = document.activeElement;
  for (const o of e)
    if (oe(o, t), document.activeElement !== n)
      return;
}, tn = yr(), Er = () => ot.value > Ot.value, Ye = () => {
  Tt.value = "pointer", ot.value = window.performance.now();
}, nn = () => {
  Tt.value = "keyboard", ot.value = window.performance.now();
}, Tr = () => (fe(() => {
  Xe === 0 && (document.addEventListener("mousedown", Ye), document.addEventListener("touchstart", Ye), document.addEventListener("keydown", nn)), Xe++;
}), ie(() => {
  Xe--, Xe <= 0 && (document.removeEventListener("mousedown", Ye), document.removeEventListener("touchstart", Ye), document.removeEventListener("keydown", nn));
}), {
  focusReason: Tt,
  lastUserFocusTimestamp: ot,
  lastAutomatedFocusTimestamp: Ot
}), Ge = (e) => new CustomEvent(pr, {
  ...vr,
  detail: e
}), Be = {
  tab: "Tab",
  enter: "Enter",
  space: "Space",
  esc: "Escape",
  delete: "Delete",
  numpadEnter: "NumpadEnter"
};
let Ce = [];
const on = (e) => {
  e.code === Be.esc && Ce.forEach((t) => t(e));
}, Or = (e) => {
  fe(() => {
    Ce.length === 0 && document.addEventListener("keydown", on), ue && Ce.push(e);
  }), ie(() => {
    Ce = Ce.filter((t) => t !== e), Ce.length === 0 && ue && document.removeEventListener("keydown", on);
  });
}, Sr = B({
  name: "ElFocusTrap",
  inheritAttrs: !1,
  props: {
    loop: Boolean,
    trapped: Boolean,
    focusTrapEl: Object,
    focusStartEl: {
      type: [Object, String],
      default: "first"
    }
  },
  emits: [
    Zt,
    Jt,
    "focusin",
    "focusout",
    "focusout-prevented",
    "release-requested"
  ],
  setup(e, { emit: t }) {
    const n = A();
    let o, r;
    const { focusReason: a } = Tr();
    Or((c) => {
      e.trapped && !i.paused && t("release-requested", c);
    });
    const i = {
      paused: !1,
      pause() {
        this.paused = !0;
      },
      resume() {
        this.paused = !1;
      }
    }, s = (c) => {
      if (!e.loop && !e.trapped || i.paused)
        return;
      const { code: p, altKey: T, ctrlKey: E, metaKey: S, currentTarget: m, shiftKey: y } = c, { loop: b } = e, w = p === Be.tab && !T && !E && !S, O = document.activeElement;
      if (w && O) {
        const C = m, [x, _] = hr(C);
        if (x && _) {
          if (!y && O === _) {
            const F = Ge({
              focusReason: a.value
            });
            t("focusout-prevented", F), F.defaultPrevented || (c.preventDefault(), b && oe(x, !0));
          } else if (y && [x, C].includes(O)) {
            const F = Ge({
              focusReason: a.value
            });
            t("focusout-prevented", F), F.defaultPrevented || (c.preventDefault(), b && oe(_, !0));
          }
        } else if (O === C) {
          const F = Ge({
            focusReason: a.value
          });
          t("focusout-prevented", F), F.defaultPrevented || c.preventDefault();
        }
      }
    };
    Ee(mr, {
      focusTrapRef: n,
      onKeydown: s
    }), M(() => e.focusTrapEl, (c) => {
      c && (n.value = c);
    }, { immediate: !0 }), M([n], ([c], [p]) => {
      c && (c.addEventListener("keydown", s), c.addEventListener("focusin", d), c.addEventListener("focusout", f)), p && (p.removeEventListener("keydown", s), p.removeEventListener("focusin", d), p.removeEventListener("focusout", f));
    });
    const l = (c) => {
      t(Zt, c);
    }, u = (c) => t(Jt, c), d = (c) => {
      const p = v(n);
      if (!p)
        return;
      const T = c.target, E = c.relatedTarget, S = T && p.contains(T);
      e.trapped || E && p.contains(E) || (o = E), S && t("focusin", c), !i.paused && e.trapped && (S ? r = T : oe(r, !0));
    }, f = (c) => {
      const p = v(n);
      if (!(i.paused || !p))
        if (e.trapped) {
          const T = c.relatedTarget;
          !ht(T) && !p.contains(T) && setTimeout(() => {
            if (!i.paused && e.trapped) {
              const E = Ge({
                focusReason: a.value
              });
              t("focusout-prevented", E), E.defaultPrevented || oe(r, !0);
            }
          }, 0);
        } else {
          const T = c.target;
          T && p.contains(T) || t("focusout", c);
        }
    };
    async function g() {
      await et();
      const c = v(n);
      if (c) {
        tn.push(i);
        const p = c.contains(document.activeElement) ? o : document.activeElement;
        if (o = p, !c.contains(p)) {
          const E = new Event(lt, Gt);
          c.addEventListener(lt, l), c.dispatchEvent(E), E.defaultPrevented || et(() => {
            let S = e.focusStartEl;
            no(S) || (oe(S), document.activeElement !== S && (S = "first")), S === "first" && wr(An(c), !0), (document.activeElement === p || S === "container") && oe(c);
          });
        }
      }
    }
    function h() {
      const c = v(n);
      if (c) {
        c.removeEventListener(lt, l);
        const p = new CustomEvent(ut, {
          ...Gt,
          detail: {
            focusReason: a.value
          }
        });
        c.addEventListener(ut, u), c.dispatchEvent(p), !p.defaultPrevented && (a.value == "keyboard" || !Er() || c.contains(document.activeElement)) && oe(o ?? document.body), c.removeEventListener(ut, u), tn.remove(i);
      }
    }
    return fe(() => {
      e.trapped && g(), M(() => e.trapped, (c) => {
        c ? g() : h();
      });
    }), ie(() => {
      e.trapped && h(), n.value && (n.value.removeEventListener("keydown", s), n.value.removeEventListener("focusin", d), n.value.removeEventListener("focusout", f), n.value = void 0);
    }), {
      onKeydown: s
    };
  }
});
function Cr(e, t, n, o, r, a) {
  return G(e.$slots, "default", { handleKeydown: e.onKeydown });
}
var Rr = /* @__PURE__ */ X(Sr, [["render", Cr], ["__file", "focus-trap.vue"]]), z = "top", q = "bottom", V = "right", W = "left", St = "auto", ze = [z, q, V, W], Pe = "start", $e = "end", Pr = "clippingParents", _n = "viewport", Me = "popper", Ar = "reference", rn = ze.reduce(function(e, t) {
  return e.concat([t + "-" + Pe, t + "-" + $e]);
}, []), Ct = [].concat(ze, [St]).reduce(function(e, t) {
  return e.concat([t, t + "-" + Pe, t + "-" + $e]);
}, []), _r = "beforeRead", xr = "read", Fr = "afterRead", Lr = "beforeMain", kr = "main", Ir = "afterMain", Mr = "beforeWrite", Br = "write", Nr = "afterWrite", jr = [_r, xr, Fr, Lr, kr, Ir, Mr, Br, Nr];
function te(e) {
  return e ? (e.nodeName || "").toLowerCase() : null;
}
function Z(e) {
  if (e == null) return window;
  if (e.toString() !== "[object Window]") {
    var t = e.ownerDocument;
    return t && t.defaultView || window;
  }
  return e;
}
function Ae(e) {
  var t = Z(e).Element;
  return e instanceof t || e instanceof Element;
}
function K(e) {
  var t = Z(e).HTMLElement;
  return e instanceof t || e instanceof HTMLElement;
}
function Rt(e) {
  if (typeof ShadowRoot > "u") return !1;
  var t = Z(e).ShadowRoot;
  return e instanceof t || e instanceof ShadowRoot;
}
function Dr(e) {
  var t = e.state;
  Object.keys(t.elements).forEach(function(n) {
    var o = t.styles[n] || {}, r = t.attributes[n] || {}, a = t.elements[n];
    !K(a) || !te(a) || (Object.assign(a.style, o), Object.keys(r).forEach(function(i) {
      var s = r[i];
      s === !1 ? a.removeAttribute(i) : a.setAttribute(i, s === !0 ? "" : s);
    }));
  });
}
function $r(e) {
  var t = e.state, n = { popper: { position: t.options.strategy, left: "0", top: "0", margin: "0" }, arrow: { position: "absolute" }, reference: {} };
  return Object.assign(t.elements.popper.style, n.popper), t.styles = n, t.elements.arrow && Object.assign(t.elements.arrow.style, n.arrow), function() {
    Object.keys(t.elements).forEach(function(o) {
      var r = t.elements[o], a = t.attributes[o] || {}, i = Object.keys(t.styles.hasOwnProperty(o) ? t.styles[o] : n[o]), s = i.reduce(function(l, u) {
        return l[u] = "", l;
      }, {});
      !K(r) || !te(r) || (Object.assign(r.style, s), Object.keys(a).forEach(function(l) {
        r.removeAttribute(l);
      }));
    });
  };
}
var xn = { name: "applyStyles", enabled: !0, phase: "write", fn: Dr, effect: $r, requires: ["computeStyles"] };
function Q(e) {
  return e.split("-")[0];
}
var ye = Math.max, tt = Math.min, _e = Math.round;
function xe(e, t) {
  t === void 0 && (t = !1);
  var n = e.getBoundingClientRect(), o = 1, r = 1;
  if (K(e) && t) {
    var a = e.offsetHeight, i = e.offsetWidth;
    i > 0 && (o = _e(n.width) / i || 1), a > 0 && (r = _e(n.height) / a || 1);
  }
  return { width: n.width / o, height: n.height / r, top: n.top / r, right: n.right / o, bottom: n.bottom / r, left: n.left / o, x: n.left / o, y: n.top / r };
}
function Pt(e) {
  var t = xe(e), n = e.offsetWidth, o = e.offsetHeight;
  return Math.abs(t.width - n) <= 1 && (n = t.width), Math.abs(t.height - o) <= 1 && (o = t.height), { x: e.offsetLeft, y: e.offsetTop, width: n, height: o };
}
function Fn(e, t) {
  var n = t.getRootNode && t.getRootNode();
  if (e.contains(t)) return !0;
  if (n && Rt(n)) {
    var o = t;
    do {
      if (o && e.isSameNode(o)) return !0;
      o = o.parentNode || o.host;
    } while (o);
  }
  return !1;
}
function ae(e) {
  return Z(e).getComputedStyle(e);
}
function Hr(e) {
  return ["table", "td", "th"].indexOf(te(e)) >= 0;
}
function de(e) {
  return ((Ae(e) ? e.ownerDocument : e.document) || window.document).documentElement;
}
function rt(e) {
  return te(e) === "html" ? e : e.assignedSlot || e.parentNode || (Rt(e) ? e.host : null) || de(e);
}
function an(e) {
  return !K(e) || ae(e).position === "fixed" ? null : e.offsetParent;
}
function zr(e) {
  var t = navigator.userAgent.toLowerCase().indexOf("firefox") !== -1, n = navigator.userAgent.indexOf("Trident") !== -1;
  if (n && K(e)) {
    var o = ae(e);
    if (o.position === "fixed") return null;
  }
  var r = rt(e);
  for (Rt(r) && (r = r.host); K(r) && ["html", "body"].indexOf(te(r)) < 0; ) {
    var a = ae(r);
    if (a.transform !== "none" || a.perspective !== "none" || a.contain === "paint" || ["transform", "perspective"].indexOf(a.willChange) !== -1 || t && a.willChange === "filter" || t && a.filter && a.filter !== "none") return r;
    r = r.parentNode;
  }
  return null;
}
function We(e) {
  for (var t = Z(e), n = an(e); n && Hr(n) && ae(n).position === "static"; ) n = an(n);
  return n && (te(n) === "html" || te(n) === "body" && ae(n).position === "static") ? t : n || zr(e) || t;
}
function At(e) {
  return ["top", "bottom"].indexOf(e) >= 0 ? "x" : "y";
}
function Ne(e, t, n) {
  return ye(e, tt(t, n));
}
function Wr(e, t, n) {
  var o = Ne(e, t, n);
  return o > n ? n : o;
}
function Ln() {
  return { top: 0, right: 0, bottom: 0, left: 0 };
}
function kn(e) {
  return Object.assign({}, Ln(), e);
}
function In(e, t) {
  return t.reduce(function(n, o) {
    return n[o] = e, n;
  }, {});
}
var Ur = function(e, t) {
  return e = typeof e == "function" ? e(Object.assign({}, t.rects, { placement: t.placement })) : e, kn(typeof e != "number" ? e : In(e, ze));
};
function Kr(e) {
  var t, n = e.state, o = e.name, r = e.options, a = n.elements.arrow, i = n.modifiersData.popperOffsets, s = Q(n.placement), l = At(s), u = [W, V].indexOf(s) >= 0, d = u ? "height" : "width";
  if (!(!a || !i)) {
    var f = Ur(r.padding, n), g = Pt(a), h = l === "y" ? z : W, c = l === "y" ? q : V, p = n.rects.reference[d] + n.rects.reference[l] - i[l] - n.rects.popper[d], T = i[l] - n.rects.reference[l], E = We(a), S = E ? l === "y" ? E.clientHeight || 0 : E.clientWidth || 0 : 0, m = p / 2 - T / 2, y = f[h], b = S - g[d] - f[c], w = S / 2 - g[d] / 2 + m, O = Ne(y, w, b), C = l;
    n.modifiersData[o] = (t = {}, t[C] = O, t.centerOffset = O - w, t);
  }
}
function qr(e) {
  var t = e.state, n = e.options, o = n.element, r = o === void 0 ? "[data-popper-arrow]" : o;
  r != null && (typeof r == "string" && (r = t.elements.popper.querySelector(r), !r) || !Fn(t.elements.popper, r) || (t.elements.arrow = r));
}
var Vr = { name: "arrow", enabled: !0, phase: "main", fn: Kr, effect: qr, requires: ["popperOffsets"], requiresIfExists: ["preventOverflow"] };
function Fe(e) {
  return e.split("-")[1];
}
var Xr = { top: "auto", right: "auto", bottom: "auto", left: "auto" };
function Yr(e) {
  var t = e.x, n = e.y, o = window, r = o.devicePixelRatio || 1;
  return { x: _e(t * r) / r || 0, y: _e(n * r) / r || 0 };
}
function sn(e) {
  var t, n = e.popper, o = e.popperRect, r = e.placement, a = e.variation, i = e.offsets, s = e.position, l = e.gpuAcceleration, u = e.adaptive, d = e.roundOffsets, f = e.isFixed, g = i.x, h = g === void 0 ? 0 : g, c = i.y, p = c === void 0 ? 0 : c, T = typeof d == "function" ? d({ x: h, y: p }) : { x: h, y: p };
  h = T.x, p = T.y;
  var E = i.hasOwnProperty("x"), S = i.hasOwnProperty("y"), m = W, y = z, b = window;
  if (u) {
    var w = We(n), O = "clientHeight", C = "clientWidth";
    if (w === Z(n) && (w = de(n), ae(w).position !== "static" && s === "absolute" && (O = "scrollHeight", C = "scrollWidth")), w = w, r === z || (r === W || r === V) && a === $e) {
      y = q;
      var x = f && w === b && b.visualViewport ? b.visualViewport.height : w[O];
      p -= x - o.height, p *= l ? 1 : -1;
    }
    if (r === W || (r === z || r === q) && a === $e) {
      m = V;
      var _ = f && w === b && b.visualViewport ? b.visualViewport.width : w[C];
      h -= _ - o.width, h *= l ? 1 : -1;
    }
  }
  var k = Object.assign({ position: s }, u && Xr), F = d === !0 ? Yr({ x: h, y: p }) : { x: h, y: p };
  if (h = F.x, p = F.y, l) {
    var I;
    return Object.assign({}, k, (I = {}, I[y] = S ? "0" : "", I[m] = E ? "0" : "", I.transform = (b.devicePixelRatio || 1) <= 1 ? "translate(" + h + "px, " + p + "px)" : "translate3d(" + h + "px, " + p + "px, 0)", I));
  }
  return Object.assign({}, k, (t = {}, t[y] = S ? p + "px" : "", t[m] = E ? h + "px" : "", t.transform = "", t));
}
function Gr(e) {
  var t = e.state, n = e.options, o = n.gpuAcceleration, r = o === void 0 ? !0 : o, a = n.adaptive, i = a === void 0 ? !0 : a, s = n.roundOffsets, l = s === void 0 ? !0 : s, u = { placement: Q(t.placement), variation: Fe(t.placement), popper: t.elements.popper, popperRect: t.rects.popper, gpuAcceleration: r, isFixed: t.options.strategy === "fixed" };
  t.modifiersData.popperOffsets != null && (t.styles.popper = Object.assign({}, t.styles.popper, sn(Object.assign({}, u, { offsets: t.modifiersData.popperOffsets, position: t.options.strategy, adaptive: i, roundOffsets: l })))), t.modifiersData.arrow != null && (t.styles.arrow = Object.assign({}, t.styles.arrow, sn(Object.assign({}, u, { offsets: t.modifiersData.arrow, position: "absolute", adaptive: !1, roundOffsets: l })))), t.attributes.popper = Object.assign({}, t.attributes.popper, { "data-popper-placement": t.placement });
}
var Mn = { name: "computeStyles", enabled: !0, phase: "beforeWrite", fn: Gr, data: {} }, Ze = { passive: !0 };
function Zr(e) {
  var t = e.state, n = e.instance, o = e.options, r = o.scroll, a = r === void 0 ? !0 : r, i = o.resize, s = i === void 0 ? !0 : i, l = Z(t.elements.popper), u = [].concat(t.scrollParents.reference, t.scrollParents.popper);
  return a && u.forEach(function(d) {
    d.addEventListener("scroll", n.update, Ze);
  }), s && l.addEventListener("resize", n.update, Ze), function() {
    a && u.forEach(function(d) {
      d.removeEventListener("scroll", n.update, Ze);
    }), s && l.removeEventListener("resize", n.update, Ze);
  };
}
var Bn = { name: "eventListeners", enabled: !0, phase: "write", fn: function() {
}, effect: Zr, data: {} }, Jr = { left: "right", right: "left", bottom: "top", top: "bottom" };
function Je(e) {
  return e.replace(/left|right|bottom|top/g, function(t) {
    return Jr[t];
  });
}
var Qr = { start: "end", end: "start" };
function ln(e) {
  return e.replace(/start|end/g, function(t) {
    return Qr[t];
  });
}
function _t(e) {
  var t = Z(e), n = t.pageXOffset, o = t.pageYOffset;
  return { scrollLeft: n, scrollTop: o };
}
function xt(e) {
  return xe(de(e)).left + _t(e).scrollLeft;
}
function ea(e) {
  var t = Z(e), n = de(e), o = t.visualViewport, r = n.clientWidth, a = n.clientHeight, i = 0, s = 0;
  return o && (r = o.width, a = o.height, /^((?!chrome|android).)*safari/i.test(navigator.userAgent) || (i = o.offsetLeft, s = o.offsetTop)), { width: r, height: a, x: i + xt(e), y: s };
}
function ta(e) {
  var t, n = de(e), o = _t(e), r = (t = e.ownerDocument) == null ? void 0 : t.body, a = ye(n.scrollWidth, n.clientWidth, r ? r.scrollWidth : 0, r ? r.clientWidth : 0), i = ye(n.scrollHeight, n.clientHeight, r ? r.scrollHeight : 0, r ? r.clientHeight : 0), s = -o.scrollLeft + xt(e), l = -o.scrollTop;
  return ae(r || n).direction === "rtl" && (s += ye(n.clientWidth, r ? r.clientWidth : 0) - a), { width: a, height: i, x: s, y: l };
}
function Ft(e) {
  var t = ae(e), n = t.overflow, o = t.overflowX, r = t.overflowY;
  return /auto|scroll|overlay|hidden/.test(n + r + o);
}
function Nn(e) {
  return ["html", "body", "#document"].indexOf(te(e)) >= 0 ? e.ownerDocument.body : K(e) && Ft(e) ? e : Nn(rt(e));
}
function je(e, t) {
  var n;
  t === void 0 && (t = []);
  var o = Nn(e), r = o === ((n = e.ownerDocument) == null ? void 0 : n.body), a = Z(o), i = r ? [a].concat(a.visualViewport || [], Ft(o) ? o : []) : o, s = t.concat(i);
  return r ? s : s.concat(je(rt(i)));
}
function mt(e) {
  return Object.assign({}, e, { left: e.x, top: e.y, right: e.x + e.width, bottom: e.y + e.height });
}
function na(e) {
  var t = xe(e);
  return t.top = t.top + e.clientTop, t.left = t.left + e.clientLeft, t.bottom = t.top + e.clientHeight, t.right = t.left + e.clientWidth, t.width = e.clientWidth, t.height = e.clientHeight, t.x = t.left, t.y = t.top, t;
}
function un(e, t) {
  return t === _n ? mt(ea(e)) : Ae(t) ? na(t) : mt(ta(de(e)));
}
function oa(e) {
  var t = je(rt(e)), n = ["absolute", "fixed"].indexOf(ae(e).position) >= 0, o = n && K(e) ? We(e) : e;
  return Ae(o) ? t.filter(function(r) {
    return Ae(r) && Fn(r, o) && te(r) !== "body";
  }) : [];
}
function ra(e, t, n) {
  var o = t === "clippingParents" ? oa(e) : [].concat(t), r = [].concat(o, [n]), a = r[0], i = r.reduce(function(s, l) {
    var u = un(e, l);
    return s.top = ye(u.top, s.top), s.right = tt(u.right, s.right), s.bottom = tt(u.bottom, s.bottom), s.left = ye(u.left, s.left), s;
  }, un(e, a));
  return i.width = i.right - i.left, i.height = i.bottom - i.top, i.x = i.left, i.y = i.top, i;
}
function jn(e) {
  var t = e.reference, n = e.element, o = e.placement, r = o ? Q(o) : null, a = o ? Fe(o) : null, i = t.x + t.width / 2 - n.width / 2, s = t.y + t.height / 2 - n.height / 2, l;
  switch (r) {
    case z:
      l = { x: i, y: t.y - n.height };
      break;
    case q:
      l = { x: i, y: t.y + t.height };
      break;
    case V:
      l = { x: t.x + t.width, y: s };
      break;
    case W:
      l = { x: t.x - n.width, y: s };
      break;
    default:
      l = { x: t.x, y: t.y };
  }
  var u = r ? At(r) : null;
  if (u != null) {
    var d = u === "y" ? "height" : "width";
    switch (a) {
      case Pe:
        l[u] = l[u] - (t[d] / 2 - n[d] / 2);
        break;
      case $e:
        l[u] = l[u] + (t[d] / 2 - n[d] / 2);
        break;
    }
  }
  return l;
}
function He(e, t) {
  t === void 0 && (t = {});
  var n = t, o = n.placement, r = o === void 0 ? e.placement : o, a = n.boundary, i = a === void 0 ? Pr : a, s = n.rootBoundary, l = s === void 0 ? _n : s, u = n.elementContext, d = u === void 0 ? Me : u, f = n.altBoundary, g = f === void 0 ? !1 : f, h = n.padding, c = h === void 0 ? 0 : h, p = kn(typeof c != "number" ? c : In(c, ze)), T = d === Me ? Ar : Me, E = e.rects.popper, S = e.elements[g ? T : d], m = ra(Ae(S) ? S : S.contextElement || de(e.elements.popper), i, l), y = xe(e.elements.reference), b = jn({ reference: y, element: E, placement: r }), w = mt(Object.assign({}, E, b)), O = d === Me ? w : y, C = { top: m.top - O.top + p.top, bottom: O.bottom - m.bottom + p.bottom, left: m.left - O.left + p.left, right: O.right - m.right + p.right }, x = e.modifiersData.offset;
  if (d === Me && x) {
    var _ = x[r];
    Object.keys(C).forEach(function(k) {
      var F = [V, q].indexOf(k) >= 0 ? 1 : -1, I = [z, q].indexOf(k) >= 0 ? "y" : "x";
      C[k] += _[I] * F;
    });
  }
  return C;
}
function aa(e, t) {
  t === void 0 && (t = {});
  var n = t, o = n.placement, r = n.boundary, a = n.rootBoundary, i = n.padding, s = n.flipVariations, l = n.allowedAutoPlacements, u = l === void 0 ? Ct : l, d = Fe(o), f = d ? s ? rn : rn.filter(function(c) {
    return Fe(c) === d;
  }) : ze, g = f.filter(function(c) {
    return u.indexOf(c) >= 0;
  });
  g.length === 0 && (g = f);
  var h = g.reduce(function(c, p) {
    return c[p] = He(e, { placement: p, boundary: r, rootBoundary: a, padding: i })[Q(p)], c;
  }, {});
  return Object.keys(h).sort(function(c, p) {
    return h[c] - h[p];
  });
}
function sa(e) {
  if (Q(e) === St) return [];
  var t = Je(e);
  return [ln(e), t, ln(t)];
}
function ia(e) {
  var t = e.state, n = e.options, o = e.name;
  if (!t.modifiersData[o]._skip) {
    for (var r = n.mainAxis, a = r === void 0 ? !0 : r, i = n.altAxis, s = i === void 0 ? !0 : i, l = n.fallbackPlacements, u = n.padding, d = n.boundary, f = n.rootBoundary, g = n.altBoundary, h = n.flipVariations, c = h === void 0 ? !0 : h, p = n.allowedAutoPlacements, T = t.options.placement, E = Q(T), S = E === T, m = l || (S || !c ? [Je(T)] : sa(T)), y = [T].concat(m).reduce(function(ve, ne) {
      return ve.concat(Q(ne) === St ? aa(t, { placement: ne, boundary: d, rootBoundary: f, padding: u, flipVariations: c, allowedAutoPlacements: p }) : ne);
    }, []), b = t.rects.reference, w = t.rects.popper, O = /* @__PURE__ */ new Map(), C = !0, x = y[0], _ = 0; _ < y.length; _++) {
      var k = y[_], F = Q(k), I = Fe(k) === Pe, J = [z, q].indexOf(F) >= 0, $ = J ? "width" : "height", j = He(t, { placement: k, boundary: d, rootBoundary: f, altBoundary: g, padding: u }), D = J ? I ? V : W : I ? q : z;
      b[$] > w[$] && (D = Je(D));
      var R = Je(D), N = [];
      if (a && N.push(j[F] <= 0), s && N.push(j[D] <= 0, j[R] <= 0), N.every(function(ve) {
        return ve;
      })) {
        x = k, C = !1;
        break;
      }
      O.set(k, N);
    }
    if (C) for (var pe = c ? 3 : 1, Le = function(ve) {
      var ne = y.find(function(Ke) {
        var Ie = O.get(Ke);
        if (Ie) return Ie.slice(0, ve).every(function(Te) {
          return Te;
        });
      });
      if (ne) return x = ne, "break";
    }, ke = pe; ke > 0; ke--) {
      var Ue = Le(ke);
      if (Ue === "break") break;
    }
    t.placement !== x && (t.modifiersData[o]._skip = !0, t.placement = x, t.reset = !0);
  }
}
var la = { name: "flip", enabled: !0, phase: "main", fn: ia, requiresIfExists: ["offset"], data: { _skip: !1 } };
function cn(e, t, n) {
  return n === void 0 && (n = { x: 0, y: 0 }), { top: e.top - t.height - n.y, right: e.right - t.width + n.x, bottom: e.bottom - t.height + n.y, left: e.left - t.width - n.x };
}
function fn(e) {
  return [z, V, q, W].some(function(t) {
    return e[t] >= 0;
  });
}
function ua(e) {
  var t = e.state, n = e.name, o = t.rects.reference, r = t.rects.popper, a = t.modifiersData.preventOverflow, i = He(t, { elementContext: "reference" }), s = He(t, { altBoundary: !0 }), l = cn(i, o), u = cn(s, r, a), d = fn(l), f = fn(u);
  t.modifiersData[n] = { referenceClippingOffsets: l, popperEscapeOffsets: u, isReferenceHidden: d, hasPopperEscaped: f }, t.attributes.popper = Object.assign({}, t.attributes.popper, { "data-popper-reference-hidden": d, "data-popper-escaped": f });
}
var ca = { name: "hide", enabled: !0, phase: "main", requiresIfExists: ["preventOverflow"], fn: ua };
function fa(e, t, n) {
  var o = Q(e), r = [W, z].indexOf(o) >= 0 ? -1 : 1, a = typeof n == "function" ? n(Object.assign({}, t, { placement: e })) : n, i = a[0], s = a[1];
  return i = i || 0, s = (s || 0) * r, [W, V].indexOf(o) >= 0 ? { x: s, y: i } : { x: i, y: s };
}
function da(e) {
  var t = e.state, n = e.options, o = e.name, r = n.offset, a = r === void 0 ? [0, 0] : r, i = Ct.reduce(function(d, f) {
    return d[f] = fa(f, t.rects, a), d;
  }, {}), s = i[t.placement], l = s.x, u = s.y;
  t.modifiersData.popperOffsets != null && (t.modifiersData.popperOffsets.x += l, t.modifiersData.popperOffsets.y += u), t.modifiersData[o] = i;
}
var pa = { name: "offset", enabled: !0, phase: "main", requires: ["popperOffsets"], fn: da };
function va(e) {
  var t = e.state, n = e.name;
  t.modifiersData[n] = jn({ reference: t.rects.reference, element: t.rects.popper, placement: t.placement });
}
var Dn = { name: "popperOffsets", enabled: !0, phase: "read", fn: va, data: {} };
function ma(e) {
  return e === "x" ? "y" : "x";
}
function ga(e) {
  var t = e.state, n = e.options, o = e.name, r = n.mainAxis, a = r === void 0 ? !0 : r, i = n.altAxis, s = i === void 0 ? !1 : i, l = n.boundary, u = n.rootBoundary, d = n.altBoundary, f = n.padding, g = n.tether, h = g === void 0 ? !0 : g, c = n.tetherOffset, p = c === void 0 ? 0 : c, T = He(t, { boundary: l, rootBoundary: u, padding: f, altBoundary: d }), E = Q(t.placement), S = Fe(t.placement), m = !S, y = At(E), b = ma(y), w = t.modifiersData.popperOffsets, O = t.rects.reference, C = t.rects.popper, x = typeof p == "function" ? p(Object.assign({}, t.rects, { placement: t.placement })) : p, _ = typeof x == "number" ? { mainAxis: x, altAxis: x } : Object.assign({ mainAxis: 0, altAxis: 0 }, x), k = t.modifiersData.offset ? t.modifiersData.offset[t.placement] : null, F = { x: 0, y: 0 };
  if (w) {
    if (a) {
      var I, J = y === "y" ? z : W, $ = y === "y" ? q : V, j = y === "y" ? "height" : "width", D = w[y], R = D + T[J], N = D - T[$], pe = h ? -C[j] / 2 : 0, Le = S === Pe ? O[j] : C[j], ke = S === Pe ? -C[j] : -O[j], Ue = t.elements.arrow, ve = h && Ue ? Pt(Ue) : { width: 0, height: 0 }, ne = t.modifiersData["arrow#persistent"] ? t.modifiersData["arrow#persistent"].padding : Ln(), Ke = ne[J], Ie = ne[$], Te = Ne(0, O[j], ve[j]), Kn = m ? O[j] / 2 - pe - Te - Ke - _.mainAxis : Le - Te - Ke - _.mainAxis, qn = m ? -O[j] / 2 + pe + Te + Ie + _.mainAxis : ke + Te + Ie + _.mainAxis, at = t.elements.arrow && We(t.elements.arrow), Vn = at ? y === "y" ? at.clientTop || 0 : at.clientLeft || 0 : 0, It = (I = k == null ? void 0 : k[y]) != null ? I : 0, Xn = D + Kn - It - Vn, Yn = D + qn - It, Mt = Ne(h ? tt(R, Xn) : R, D, h ? ye(N, Yn) : N);
      w[y] = Mt, F[y] = Mt - D;
    }
    if (s) {
      var Bt, Gn = y === "x" ? z : W, Zn = y === "x" ? q : V, me = w[b], qe = b === "y" ? "height" : "width", Nt = me + T[Gn], jt = me - T[Zn], st = [z, W].indexOf(E) !== -1, Dt = (Bt = k == null ? void 0 : k[b]) != null ? Bt : 0, $t = st ? Nt : me - O[qe] - C[qe] - Dt + _.altAxis, Ht = st ? me + O[qe] + C[qe] - Dt - _.altAxis : jt, zt = h && st ? Wr($t, me, Ht) : Ne(h ? $t : Nt, me, h ? Ht : jt);
      w[b] = zt, F[b] = zt - me;
    }
    t.modifiersData[o] = F;
  }
}
var ha = { name: "preventOverflow", enabled: !0, phase: "main", fn: ga, requiresIfExists: ["offset"] };
function ba(e) {
  return { scrollLeft: e.scrollLeft, scrollTop: e.scrollTop };
}
function ya(e) {
  return e === Z(e) || !K(e) ? _t(e) : ba(e);
}
function wa(e) {
  var t = e.getBoundingClientRect(), n = _e(t.width) / e.offsetWidth || 1, o = _e(t.height) / e.offsetHeight || 1;
  return n !== 1 || o !== 1;
}
function Ea(e, t, n) {
  n === void 0 && (n = !1);
  var o = K(t), r = K(t) && wa(t), a = de(t), i = xe(e, r), s = { scrollLeft: 0, scrollTop: 0 }, l = { x: 0, y: 0 };
  return (o || !o && !n) && ((te(t) !== "body" || Ft(a)) && (s = ya(t)), K(t) ? (l = xe(t, !0), l.x += t.clientLeft, l.y += t.clientTop) : a && (l.x = xt(a))), { x: i.left + s.scrollLeft - l.x, y: i.top + s.scrollTop - l.y, width: i.width, height: i.height };
}
function Ta(e) {
  var t = /* @__PURE__ */ new Map(), n = /* @__PURE__ */ new Set(), o = [];
  e.forEach(function(a) {
    t.set(a.name, a);
  });
  function r(a) {
    n.add(a.name);
    var i = [].concat(a.requires || [], a.requiresIfExists || []);
    i.forEach(function(s) {
      if (!n.has(s)) {
        var l = t.get(s);
        l && r(l);
      }
    }), o.push(a);
  }
  return e.forEach(function(a) {
    n.has(a.name) || r(a);
  }), o;
}
function Oa(e) {
  var t = Ta(e);
  return jr.reduce(function(n, o) {
    return n.concat(t.filter(function(r) {
      return r.phase === o;
    }));
  }, []);
}
function Sa(e) {
  var t;
  return function() {
    return t || (t = new Promise(function(n) {
      Promise.resolve().then(function() {
        t = void 0, n(e());
      });
    })), t;
  };
}
function Ca(e) {
  var t = e.reduce(function(n, o) {
    var r = n[o.name];
    return n[o.name] = r ? Object.assign({}, r, o, { options: Object.assign({}, r.options, o.options), data: Object.assign({}, r.data, o.data) }) : o, n;
  }, {});
  return Object.keys(t).map(function(n) {
    return t[n];
  });
}
var dn = { placement: "bottom", modifiers: [], strategy: "absolute" };
function pn() {
  for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
  return !t.some(function(o) {
    return !(o && typeof o.getBoundingClientRect == "function");
  });
}
function Lt(e) {
  e === void 0 && (e = {});
  var t = e, n = t.defaultModifiers, o = n === void 0 ? [] : n, r = t.defaultOptions, a = r === void 0 ? dn : r;
  return function(i, s, l) {
    l === void 0 && (l = a);
    var u = { placement: "bottom", orderedModifiers: [], options: Object.assign({}, dn, a), modifiersData: {}, elements: { reference: i, popper: s }, attributes: {}, styles: {} }, d = [], f = !1, g = { state: u, setOptions: function(p) {
      var T = typeof p == "function" ? p(u.options) : p;
      c(), u.options = Object.assign({}, a, u.options, T), u.scrollParents = { reference: Ae(i) ? je(i) : i.contextElement ? je(i.contextElement) : [], popper: je(s) };
      var E = Oa(Ca([].concat(o, u.options.modifiers)));
      return u.orderedModifiers = E.filter(function(S) {
        return S.enabled;
      }), h(), g.update();
    }, forceUpdate: function() {
      if (!f) {
        var p = u.elements, T = p.reference, E = p.popper;
        if (pn(T, E)) {
          u.rects = { reference: Ea(T, We(E), u.options.strategy === "fixed"), popper: Pt(E) }, u.reset = !1, u.placement = u.options.placement, u.orderedModifiers.forEach(function(C) {
            return u.modifiersData[C.name] = Object.assign({}, C.data);
          });
          for (var S = 0; S < u.orderedModifiers.length; S++) {
            if (u.reset === !0) {
              u.reset = !1, S = -1;
              continue;
            }
            var m = u.orderedModifiers[S], y = m.fn, b = m.options, w = b === void 0 ? {} : b, O = m.name;
            typeof y == "function" && (u = y({ state: u, options: w, name: O, instance: g }) || u);
          }
        }
      }
    }, update: Sa(function() {
      return new Promise(function(p) {
        g.forceUpdate(), p(u);
      });
    }), destroy: function() {
      c(), f = !0;
    } };
    if (!pn(i, s)) return g;
    g.setOptions(l).then(function(p) {
      !f && l.onFirstUpdate && l.onFirstUpdate(p);
    });
    function h() {
      u.orderedModifiers.forEach(function(p) {
        var T = p.name, E = p.options, S = E === void 0 ? {} : E, m = p.effect;
        if (typeof m == "function") {
          var y = m({ state: u, name: T, instance: g, options: S }), b = function() {
          };
          d.push(y || b);
        }
      });
    }
    function c() {
      d.forEach(function(p) {
        return p();
      }), d = [];
    }
    return g;
  };
}
Lt();
var Ra = [Bn, Dn, Mn, xn];
Lt({ defaultModifiers: Ra });
var Pa = [Bn, Dn, Mn, xn, pa, la, ha, Vr, ca], Aa = Lt({ defaultModifiers: Pa });
const _a = ["fixed", "absolute"], xa = U({
  boundariesPadding: {
    type: Number,
    default: 0
  },
  fallbackPlacements: {
    type: L(Array),
    default: void 0
  },
  gpuAcceleration: {
    type: Boolean,
    default: !0
  },
  offset: {
    type: Number,
    default: 12
  },
  placement: {
    type: String,
    values: Ct,
    default: "bottom"
  },
  popperOptions: {
    type: L(Object),
    default: () => ({})
  },
  strategy: {
    type: String,
    values: _a,
    default: "absolute"
  }
}), $n = U({
  ...xa,
  id: String,
  style: {
    type: L([String, Array, Object])
  },
  className: {
    type: L([String, Array, Object])
  },
  effect: {
    type: L(String),
    default: "dark"
  },
  visible: Boolean,
  enterable: {
    type: Boolean,
    default: !0
  },
  pure: Boolean,
  focusOnShow: {
    type: Boolean,
    default: !1
  },
  trapping: {
    type: Boolean,
    default: !1
  },
  popperClass: {
    type: L([String, Array, Object])
  },
  popperStyle: {
    type: L([String, Array, Object])
  },
  referenceEl: {
    type: L(Object)
  },
  triggerTargetEl: {
    type: L(Object)
  },
  stopPopperMouseEvent: {
    type: Boolean,
    default: !0
  },
  virtualTriggering: Boolean,
  zIndex: Number,
  ...gt(["ariaLabel"])
}), Fa = {
  mouseenter: (e) => e instanceof MouseEvent,
  mouseleave: (e) => e instanceof MouseEvent,
  focus: () => !0,
  blur: () => !0,
  close: () => !0
}, La = (e, t) => {
  const n = A(!1), o = A();
  return {
    focusStartRef: o,
    trapped: n,
    onFocusAfterReleased: (u) => {
      var d;
      ((d = u.detail) == null ? void 0 : d.focusReason) !== "pointer" && (o.value = "first", t("blur"));
    },
    onFocusAfterTrapped: () => {
      t("focus");
    },
    onFocusInTrap: (u) => {
      e.visible && !n.value && (u.target && (o.value = u.target), n.value = !0);
    },
    onFocusoutPrevented: (u) => {
      e.trapping || (u.detail.focusReason === "pointer" && u.preventDefault(), n.value = !1);
    },
    onReleaseRequested: () => {
      n.value = !1, t("close");
    }
  };
}, ka = (e, t = []) => {
  const { placement: n, strategy: o, popperOptions: r } = e, a = {
    placement: n,
    strategy: o,
    ...r,
    modifiers: [...Ma(e), ...t]
  };
  return Ba(a, r == null ? void 0 : r.modifiers), a;
}, Ia = (e) => {
  if (ue)
    return gn(e);
};
function Ma(e) {
  const { offset: t, gpuAcceleration: n, fallbackPlacements: o } = e;
  return [
    {
      name: "offset",
      options: {
        offset: [0, t ?? 12]
      }
    },
    {
      name: "preventOverflow",
      options: {
        padding: {
          top: 2,
          bottom: 2,
          left: 5,
          right: 5
        }
      }
    },
    {
      name: "flip",
      options: {
        padding: 5,
        fallbackPlacements: o
      }
    },
    {
      name: "computeStyles",
      options: {
        gpuAcceleration: n
      }
    }
  ];
}
function Ba(e, t) {
  t && (e.modifiers = [...e.modifiers, ...t ?? []]);
}
const Na = (e, t, n = {}) => {
  const o = {
    name: "updateState",
    enabled: !0,
    phase: "write",
    fn: ({ state: l }) => {
      const u = ja(l);
      Object.assign(i.value, u);
    },
    requires: ["computeStyles"]
  }, r = P(() => {
    const { onFirstUpdate: l, placement: u, strategy: d, modifiers: f } = v(n);
    return {
      onFirstUpdate: l,
      placement: u || "bottom",
      strategy: d || "absolute",
      modifiers: [
        ...f || [],
        o,
        { name: "applyStyles", enabled: !1 }
      ]
    };
  }), a = wo(), i = A({
    styles: {
      popper: {
        position: v(r).strategy,
        left: "0",
        top: "0"
      },
      arrow: {
        position: "absolute"
      }
    },
    attributes: {}
  }), s = () => {
    a.value && (a.value.destroy(), a.value = void 0);
  };
  return M(r, (l) => {
    const u = v(a);
    u && u.setOptions(l);
  }, {
    deep: !0
  }), M([e, t], ([l, u]) => {
    s(), !(!l || !u) && (a.value = Aa(l, u, v(r)));
  }), ie(() => {
    s();
  }), {
    state: P(() => {
      var l;
      return { ...((l = v(a)) == null ? void 0 : l.state) || {} };
    }),
    styles: P(() => v(i).styles),
    attributes: P(() => v(i).attributes),
    update: () => {
      var l;
      return (l = v(a)) == null ? void 0 : l.update();
    },
    forceUpdate: () => {
      var l;
      return (l = v(a)) == null ? void 0 : l.forceUpdate();
    },
    instanceRef: P(() => v(a))
  };
};
function ja(e) {
  const t = Object.keys(e.elements), n = Ut(t.map((r) => [r, e.styles[r] || {}])), o = Ut(t.map((r) => [r, e.attributes[r]]));
  return {
    styles: n,
    attributes: o
  };
}
const Da = 0, $a = (e) => {
  const { popperInstanceRef: t, contentRef: n, triggerRef: o, role: r } = se(Et, void 0), a = A(), i = A(), s = P(() => ({
    name: "eventListeners",
    enabled: !!e.visible
  })), l = P(() => {
    var E;
    const S = v(a), m = (E = v(i)) != null ? E : Da;
    return {
      name: "arrow",
      enabled: !jo(S),
      options: {
        element: S,
        padding: m
      }
    };
  }), u = P(() => ({
    onFirstUpdate: () => {
      c();
    },
    ...ka(e, [
      v(l),
      v(s)
    ])
  })), d = P(() => Ia(e.referenceEl) || v(o)), { attributes: f, state: g, styles: h, update: c, forceUpdate: p, instanceRef: T } = Na(d, n, u);
  return M(T, (E) => t.value = E, {
    flush: "sync"
  }), fe(() => {
    M(() => {
      var E;
      return (E = v(d)) == null ? void 0 : E.getBoundingClientRect();
    }, () => {
      c();
    });
  }), {
    attributes: f,
    arrowRef: a,
    contentRef: n,
    instanceRef: T,
    state: g,
    styles: h,
    role: r,
    forceUpdate: p,
    update: c
  };
}, Ha = (e, {
  attributes: t,
  styles: n,
  role: o
}) => {
  const { nextZIndex: r } = oo(), a = ce("popper"), i = P(() => v(t).popper), s = A(le(e.zIndex) ? e.zIndex : r()), l = P(() => [
    a.b(),
    a.is("pure", e.pure),
    a.is(e.effect),
    e.popperClass
  ]), u = P(() => [
    { zIndex: v(s) },
    v(n).popper,
    e.popperStyle || {}
  ]), d = P(() => o.value === "dialog" ? "false" : void 0), f = P(() => v(n).arrow || {});
  return {
    ariaModal: d,
    arrowStyle: f,
    contentAttrs: i,
    contentClass: l,
    contentStyle: u,
    contentZIndex: s,
    updateZIndex: () => {
      s.value = le(e.zIndex) ? e.zIndex : r();
    }
  };
}, za = B({
  name: "ElPopperContent"
}), Wa = /* @__PURE__ */ B({
  ...za,
  props: $n,
  emits: Fa,
  setup(e, { expose: t, emit: n }) {
    const o = e, {
      focusStartRef: r,
      trapped: a,
      onFocusAfterReleased: i,
      onFocusAfterTrapped: s,
      onFocusInTrap: l,
      onFocusoutPrevented: u,
      onReleaseRequested: d
    } = La(o, n), { attributes: f, arrowRef: g, contentRef: h, styles: c, instanceRef: p, role: T, update: E } = $a(o), {
      ariaModal: S,
      arrowStyle: m,
      contentAttrs: y,
      contentClass: b,
      contentStyle: w,
      updateZIndex: O
    } = Ha(o, {
      styles: c,
      attributes: f,
      role: T
    }), C = se(Kt, void 0), x = A();
    Ee(Tn, {
      arrowStyle: m,
      arrowRef: g,
      arrowOffset: x
    }), C && Ee(Kt, {
      ...C,
      addInputId: dt,
      removeInputId: dt
    });
    let _;
    const k = (I = !0) => {
      E(), I && O();
    }, F = () => {
      k(!1), o.visible && o.focusOnShow ? a.value = !0 : o.visible === !1 && (a.value = !1);
    };
    return fe(() => {
      M(() => o.triggerTargetEl, (I, J) => {
        _ == null || _(), _ = void 0;
        const $ = v(I || h.value), j = v(J || h.value);
        he($) && (_ = M([T, () => o.ariaLabel, S, () => o.id], (D) => {
          ["role", "aria-label", "aria-modal", "id"].forEach((R, N) => {
            ht(D[N]) ? $.removeAttribute(R) : $.setAttribute(R, D[N]);
          });
        }, { immediate: !0 })), j !== $ && he(j) && ["role", "aria-label", "aria-modal", "id"].forEach((D) => {
          j.removeAttribute(D);
        });
      }, { immediate: !0 }), M(() => o.visible, F, { immediate: !0 });
    }), ie(() => {
      _ == null || _(), _ = void 0;
    }), t({
      popperContentRef: h,
      popperInstanceRef: p,
      updatePopper: k,
      contentStyle: w
    }), (I, J) => (H(), Re("div", yt({
      ref_key: "contentRef",
      ref: h
    }, v(y), {
      style: v(w),
      class: v(b),
      tabindex: "-1",
      onMouseenter: ($) => I.$emit("mouseenter", $),
      onMouseleave: ($) => I.$emit("mouseleave", $)
    }), [
      we(v(Rr), {
        trapped: v(a),
        "trap-on-focus-in": !0,
        "focus-trap-el": v(h),
        "focus-start-el": v(r),
        onFocusAfterTrapped: v(s),
        onFocusAfterReleased: v(i),
        onFocusin: v(l),
        onFocusoutPrevented: v(u),
        onReleaseRequested: v(d)
      }, {
        default: Y(() => [
          G(I.$slots, "default")
        ]),
        _: 3
      }, 8, ["trapped", "focus-trap-el", "focus-start-el", "onFocusAfterTrapped", "onFocusAfterReleased", "onFocusin", "onFocusoutPrevented", "onReleaseRequested"])
    ], 16, ["onMouseenter", "onMouseleave"]));
  }
});
var Ua = /* @__PURE__ */ X(Wa, [["__file", "content.vue"]]);
const Ka = nt(nr), kt = Symbol("elTooltip");
function vn() {
  let e;
  const t = (o, r) => {
    n(), e = window.setTimeout(o, r);
  }, n = () => window.clearTimeout(e);
  return ro(() => n()), {
    registerTimeout: t,
    cancelTimeout: n
  };
}
const qa = U({
  showAfter: {
    type: Number,
    default: 0
  },
  hideAfter: {
    type: Number,
    default: 200
  },
  autoClose: {
    type: Number,
    default: 0
  }
}), Va = ({
  showAfter: e,
  hideAfter: t,
  autoClose: n,
  open: o,
  close: r
}) => {
  const { registerTimeout: a } = vn(), {
    registerTimeout: i,
    cancelTimeout: s
  } = vn();
  return {
    onOpen: (d) => {
      a(() => {
        o(d);
        const f = v(n);
        le(f) && f > 0 && i(() => {
          r(d);
        }, f);
      }, v(e));
    },
    onClose: (d) => {
      s(), a(() => {
        r(d);
      }, v(t));
    }
  };
}, Hn = U({
  to: {
    type: L([String, Object]),
    required: !0
  },
  disabled: Boolean
}), zn = U({
  ...qa,
  ...$n,
  appendTo: {
    type: Hn.to.type
  },
  content: {
    type: String,
    default: ""
  },
  rawContent: Boolean,
  persistent: Boolean,
  visible: {
    type: L(Boolean),
    default: null
  },
  transition: String,
  teleported: {
    type: Boolean,
    default: !0
  },
  disabled: Boolean,
  ...gt(["ariaLabel"])
}), Wn = U({
  ...Cn,
  disabled: Boolean,
  trigger: {
    type: L([String, Array]),
    default: "hover"
  },
  triggerKeys: {
    type: L(Array),
    default: () => [Be.enter, Be.numpadEnter, Be.space]
  }
}), Xa = hn({
  type: L(Boolean),
  default: null
}), Ya = hn({
  type: L(Function)
}), Ga = (e) => {
  const t = `update:${e}`, n = `onUpdate:${e}`, o = [t], r = {
    [e]: Xa,
    [n]: Ya
  };
  return {
    useModelToggle: ({
      indicator: i,
      toggleReason: s,
      shouldHideWhenRouteChanges: l,
      shouldProceed: u,
      onShow: d,
      onHide: f
    }) => {
      const g = Eo(), { emit: h } = g, c = g.props, p = P(() => Ve(c[n])), T = P(() => c[e] === null), E = (O) => {
        i.value !== !0 && (i.value = !0, s && (s.value = O), Ve(d) && d(O));
      }, S = (O) => {
        i.value !== !1 && (i.value = !1, s && (s.value = O), Ve(f) && f(O));
      }, m = (O) => {
        if (c.disabled === !0 || Ve(u) && !u())
          return;
        const C = p.value && ue;
        C && h(t, !0), (T.value || !C) && E(O);
      }, y = (O) => {
        if (c.disabled === !0 || !ue)
          return;
        const C = p.value && ue;
        C && h(t, !1), (T.value || !C) && S(O);
      }, b = (O) => {
        bn(O) && (c.disabled && O ? p.value && h(t, !1) : i.value !== O && (O ? E() : S()));
      }, w = () => {
        i.value ? y() : m();
      };
      return M(() => c[e], b), l && g.appContext.config.globalProperties.$route !== void 0 && M(() => ({
        ...g.proxy.$route
      }), () => {
        l.value && i.value && y();
      }), fe(() => {
        b(c[e]);
      }), {
        hide: y,
        show: m,
        toggle: w,
        hasUpdateHandler: p
      };
    },
    useModelToggleProps: r,
    useModelToggleEmits: o
  };
}, {
  useModelToggleProps: Za,
  useModelToggleEmits: Ja,
  useModelToggle: Qa
} = Ga("visible"), es = U({
  ...On,
  ...Za,
  ...zn,
  ...Wn,
  ...Sn,
  showArrow: {
    type: Boolean,
    default: !0
  }
}), ts = [
  ...Ja,
  "before-show",
  "before-hide",
  "show",
  "hide",
  "open",
  "close"
], ns = (e, t) => ao(e) ? e.includes(t) : e === t, Se = (e, t, n) => (o) => {
  ns(v(e), t) && n(o);
}, re = (e, t, { checkForDefaultPrevented: n = !0 } = {}) => (r) => {
  const a = e == null ? void 0 : e(r);
  if (n === !1 || !a)
    return t == null ? void 0 : t(r);
}, os = B({
  name: "ElTooltipTrigger"
}), rs = /* @__PURE__ */ B({
  ...os,
  props: Wn,
  setup(e, { expose: t }) {
    const n = e, o = ce("tooltip"), { controlled: r, id: a, open: i, onOpen: s, onClose: l, onToggle: u } = se(kt, void 0), d = A(null), f = () => {
      if (v(r) || n.disabled)
        return !0;
    }, g = ge(n, "trigger"), h = re(f, Se(g, "hover", s)), c = re(f, Se(g, "hover", l)), p = re(f, Se(g, "click", (y) => {
      y.button === 0 && u(y);
    })), T = re(f, Se(g, "focus", s)), E = re(f, Se(g, "focus", l)), S = re(f, Se(g, "contextmenu", (y) => {
      y.preventDefault(), u(y);
    })), m = re(f, (y) => {
      const { code: b } = y;
      n.triggerKeys.includes(b) && (y.preventDefault(), u(y));
    });
    return t({
      triggerRef: d
    }), (y, b) => (H(), ee(v(dr), {
      id: v(a),
      "virtual-ref": y.virtualRef,
      open: v(i),
      "virtual-triggering": y.virtualTriggering,
      class: be(v(o).e("trigger")),
      onBlur: v(E),
      onClick: v(p),
      onContextmenu: v(S),
      onFocus: v(T),
      onMouseenter: v(h),
      onMouseleave: v(c),
      onKeydown: v(m)
    }, {
      default: Y(() => [
        G(y.$slots, "default")
      ]),
      _: 3
    }, 8, ["id", "virtual-ref", "open", "virtual-triggering", "class", "onBlur", "onClick", "onContextmenu", "onFocus", "onMouseenter", "onMouseleave", "onKeydown"]));
  }
});
var as = /* @__PURE__ */ X(rs, [["__file", "trigger.vue"]]);
const ss = /* @__PURE__ */ B({
  __name: "teleport",
  props: Hn,
  setup(e) {
    return (t, n) => t.disabled ? G(t.$slots, "default", { key: 0 }) : (H(), ee(To, {
      key: 1,
      to: t.to
    }, [
      G(t.$slots, "default")
    ], 8, ["to"]));
  }
});
var is = /* @__PURE__ */ X(ss, [["__file", "teleport.vue"]]);
const ls = nt(is), Un = () => {
  const e = so(), t = io(), n = P(() => `${e.value}-popper-container-${t.prefix}`), o = P(() => `#${n.value}`);
  return {
    id: n,
    selector: o
  };
}, us = (e) => {
  const t = document.createElement("div");
  return t.id = e, document.body.appendChild(t), t;
}, cs = () => {
  const { id: e, selector: t } = Un();
  return Oo(() => {
    ue && (document.body.querySelector(t.value) || us(e.value));
  }), {
    id: e,
    selector: t
  };
}, fs = B({
  name: "ElTooltipContent",
  inheritAttrs: !1
}), ds = /* @__PURE__ */ B({
  ...fs,
  props: zn,
  setup(e, { expose: t }) {
    const n = e, { selector: o } = Un(), r = ce("tooltip"), a = A(), i = lo(() => {
      var R;
      return (R = a.value) == null ? void 0 : R.popperContentRef;
    });
    let s;
    const {
      controlled: l,
      id: u,
      open: d,
      trigger: f,
      onClose: g,
      onOpen: h,
      onShow: c,
      onHide: p,
      onBeforeShow: T,
      onBeforeHide: E
    } = se(kt, void 0), S = P(() => n.transition || `${r.namespace.value}-fade-in-linear`), m = P(() => n.persistent);
    ie(() => {
      s == null || s();
    });
    const y = P(() => v(m) ? !0 : v(d)), b = P(() => n.disabled ? !1 : v(d)), w = P(() => n.appendTo || o.value), O = P(() => {
      var R;
      return (R = n.style) != null ? R : {};
    }), C = A(!0), x = () => {
      p(), D() && oe(document.body), C.value = !0;
    }, _ = () => {
      if (v(l))
        return !0;
    }, k = re(_, () => {
      n.enterable && v(f) === "hover" && h();
    }), F = re(_, () => {
      v(f) === "hover" && g();
    }), I = () => {
      var R, N;
      (N = (R = a.value) == null ? void 0 : R.updatePopper) == null || N.call(R), T == null || T();
    }, J = () => {
      E == null || E();
    }, $ = () => {
      c(), s = uo(i, () => {
        if (v(l))
          return;
        v(f) !== "hover" && g();
      });
    }, j = () => {
      n.virtualTriggering || g();
    }, D = (R) => {
      var N;
      const pe = (N = a.value) == null ? void 0 : N.popperContentRef, Le = (R == null ? void 0 : R.relatedTarget) || document.activeElement;
      return pe == null ? void 0 : pe.contains(Le);
    };
    return M(() => v(d), (R) => {
      R ? C.value = !1 : s == null || s();
    }, {
      flush: "post"
    }), M(() => n.content, () => {
      var R, N;
      (N = (R = a.value) == null ? void 0 : R.updatePopper) == null || N.call(R);
    }), t({
      contentRef: a,
      isFocusInsideContent: D
    }), (R, N) => (H(), ee(v(ls), {
      disabled: !R.teleported,
      to: v(w)
    }, {
      default: Y(() => [
        we(yn, {
          name: v(S),
          onAfterLeave: x,
          onBeforeEnter: I,
          onAfterEnter: $,
          onBeforeLeave: J
        }, {
          default: Y(() => [
            v(y) ? bt((H(), ee(v(Ua), yt({
              key: 0,
              id: v(u),
              ref_key: "contentRef",
              ref: a
            }, R.$attrs, {
              "aria-label": R.ariaLabel,
              "aria-hidden": C.value,
              "boundaries-padding": R.boundariesPadding,
              "fallback-placements": R.fallbackPlacements,
              "gpu-acceleration": R.gpuAcceleration,
              offset: R.offset,
              placement: R.placement,
              "popper-options": R.popperOptions,
              strategy: R.strategy,
              effect: R.effect,
              enterable: R.enterable,
              pure: R.pure,
              "popper-class": R.popperClass,
              "popper-style": [R.popperStyle, v(O)],
              "reference-el": R.referenceEl,
              "trigger-target-el": R.triggerTargetEl,
              visible: v(b),
              "z-index": R.zIndex,
              onMouseenter: v(k),
              onMouseleave: v(F),
              onBlur: j,
              onClose: v(g)
            }), {
              default: Y(() => [
                G(R.$slots, "default")
              ]),
              _: 3
            }, 16, ["id", "aria-label", "aria-hidden", "boundaries-padding", "fallback-placements", "gpu-acceleration", "offset", "placement", "popper-options", "strategy", "effect", "enterable", "pure", "popper-class", "popper-style", "reference-el", "trigger-target-el", "visible", "z-index", "onMouseenter", "onMouseleave", "onClose"])), [
              [wn, v(b)]
            ]) : De("v-if", !0)
          ]),
          _: 3
        }, 8, ["name"])
      ]),
      _: 3
    }, 8, ["disabled", "to"]));
  }
});
var ps = /* @__PURE__ */ X(ds, [["__file", "content.vue"]]);
const vs = B({
  name: "ElTooltip"
}), ms = /* @__PURE__ */ B({
  ...vs,
  props: es,
  emits: ts,
  setup(e, { expose: t, emit: n }) {
    const o = e;
    cs();
    const r = ce("tooltip"), a = co(), i = A(), s = A(), l = () => {
      var m;
      const y = v(i);
      y && ((m = y.popperInstanceRef) == null || m.update());
    }, u = A(!1), d = A(), { show: f, hide: g, hasUpdateHandler: h } = Qa({
      indicator: u,
      toggleReason: d
    }), { onOpen: c, onClose: p } = Va({
      showAfter: ge(o, "showAfter"),
      hideAfter: ge(o, "hideAfter"),
      autoClose: ge(o, "autoClose"),
      open: f,
      close: g
    }), T = P(() => bn(o.visible) && !h.value), E = P(() => [r.b(), o.popperClass]);
    Ee(kt, {
      controlled: T,
      id: a,
      open: So(u),
      trigger: ge(o, "trigger"),
      onOpen: (m) => {
        c(m);
      },
      onClose: (m) => {
        p(m);
      },
      onToggle: (m) => {
        v(u) ? p(m) : c(m);
      },
      onShow: () => {
        n("show", d.value);
      },
      onHide: () => {
        n("hide", d.value);
      },
      onBeforeShow: () => {
        n("before-show", d.value);
      },
      onBeforeHide: () => {
        n("before-hide", d.value);
      },
      updatePopper: l
    }), M(() => o.disabled, (m) => {
      m && u.value && (u.value = !1);
    });
    const S = (m) => {
      var y;
      return (y = s.value) == null ? void 0 : y.isFocusInsideContent(m);
    };
    return Co(() => u.value && g()), t({
      popperRef: i,
      contentRef: s,
      isFocusInsideContent: S,
      updatePopper: l,
      onOpen: c,
      onClose: p,
      hide: g
    }), (m, y) => (H(), ee(v(Ka), {
      ref_key: "popperRef",
      ref: i,
      role: m.role
    }, {
      default: Y(() => [
        we(as, {
          disabled: m.disabled,
          trigger: m.trigger,
          "trigger-keys": m.triggerKeys,
          "virtual-ref": m.virtualRef,
          "virtual-triggering": m.virtualTriggering
        }, {
          default: Y(() => [
            m.$slots.default ? G(m.$slots, "default", { key: 0 }) : De("v-if", !0)
          ]),
          _: 3
        }, 8, ["disabled", "trigger", "trigger-keys", "virtual-ref", "virtual-triggering"]),
        we(ps, {
          ref_key: "contentRef",
          ref: s,
          "aria-label": m.ariaLabel,
          "boundaries-padding": m.boundariesPadding,
          content: m.content,
          disabled: m.disabled,
          effect: m.effect,
          enterable: m.enterable,
          "fallback-placements": m.fallbackPlacements,
          "hide-after": m.hideAfter,
          "gpu-acceleration": m.gpuAcceleration,
          offset: m.offset,
          persistent: m.persistent,
          "popper-class": v(E),
          "popper-style": m.popperStyle,
          placement: m.placement,
          "popper-options": m.popperOptions,
          pure: m.pure,
          "raw-content": m.rawContent,
          "reference-el": m.referenceEl,
          "trigger-target-el": m.triggerTargetEl,
          "show-after": m.showAfter,
          strategy: m.strategy,
          teleported: m.teleported,
          transition: m.transition,
          "virtual-triggering": m.virtualTriggering,
          "z-index": m.zIndex,
          "append-to": m.appendTo
        }, {
          default: Y(() => [
            G(m.$slots, "content", {}, () => [
              m.rawContent ? (H(), Re("span", {
                key: 0,
                innerHTML: m.content
              }, null, 8, ["innerHTML"])) : (H(), Re("span", { key: 1 }, Ro(m.content), 1))
            ]),
            m.showArrow ? (H(), ee(v(ar), {
              key: 0,
              "arrow-offset": m.arrowOffset
            }, null, 8, ["arrow-offset"])) : De("v-if", !0)
          ]),
          _: 3
        }, 8, ["aria-label", "boundaries-padding", "content", "disabled", "effect", "enterable", "fallback-placements", "hide-after", "gpu-acceleration", "offset", "persistent", "popper-class", "popper-style", "placement", "popper-options", "pure", "raw-content", "reference-el", "trigger-target-el", "show-after", "strategy", "teleported", "transition", "virtual-triggering", "z-index", "append-to"])
      ]),
      _: 3
    }, 8, ["role"]));
  }
});
var gs = /* @__PURE__ */ X(ms, [["__file", "tooltip.vue"]]);
const Es = nt(gs);
var Ts = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function Os(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
export {
  Be as E,
  Ct as a,
  Es as b,
  ws as c,
  ys as d,
  Ts as e,
  Os as g,
  Xo as s,
  zn as u
};
