<?php

namespace frontend\services;

use common\components\Encryption;
use common\components\fileServer\ElnFileServer;
use common\components\Upload;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\models\DefineTableValueModel;
use frontend\models\ExperimentModel;
use frontend\models\InstrumentBatchUploadInfo;
use frontend\models\InstrumentBindingModel;
use frontend\models\InstrumentCheckModel;
use frontend\models\InstrumentCheckRecordExtendFieldModel;
use frontend\models\InstrumentCheckRecordModel;
use frontend\models\InstrumentDataFileModel;
use frontend\models\InstrumentDataHistoryModel;
use frontend\models\InstrumentDataNumericalModel;
use frontend\models\InstrumentDefineFields;
use frontend\models\InstrumentDefineTableData;
use frontend\models\InstrumentDefineTableKey;
use frontend\models\InstrumentReminderSettingModel;
use frontend\models\InstrumentRepairRecordExtendFieldModel;
use frontend\models\InstrumentRepairRecordModel;
use frontend\models\InstrumentRunningRecordExtendFieldModel;
use frontend\models\InstrumentRunningRecordModel;
use frontend\models\InstrumentsActionRecordModel;
use frontend\models\InstrumentsBatchEditFile;
use frontend\models\InstrumentsBookModel;
use frontend\models\InstrumentsModel;
use common\components\Picture;
use frontend\models\UseStatic;
use yii\db\Exception;
use yii\db\Expression;
use yii\helpers\ArrayHelper;


/**
 * Class InstrumentServer
 * @package frontend\services
 */
class InstrumentServer extends CommonServer
{

    const ACTION_ADD = 1; //仪器操作：1->创建，2->编辑,3->报修，4->删除,5->预约，6->取消预约,7->修改预约
    const ACTION_EDIT = 2;
    const ACTION_REPAIR = 3;
    const ACTION_DELETE = 4;
    const ACTION_BOOK = 5;
    const ACTION_DELETE_BOOK = 6;
    const ACTION_EDIT_BOOK = 7;
    const ACTION_DELETE_FILE = 8; // 删除仪器文件
    const ACTION_UPLOAD_FILE = 9; // 上传仪器文件
    const ACTION_EDIT_OPERATE_RECORD = 10; // 编辑运行记录
    const ACTION_EDIT_REPAIR_RECORD = 11; // 编辑维修记录
    const ACTION_EDIT_CHECK_RECORD = 12; // 编辑校验记录
    const ACTION_UPLOAD_PICTURE = 13; // 上传仪器图片
    const ACTION_DELETE_PICTURE = 14; // 删除仪器图片
    const ACTION_APPROVAL = 15; // 状态审核
    const Action_SETTING = 16; //仪器时间和审核设置
    const Action_REMINDER = 17;//仪器提醒
    const Action_ADJUST_COLUMN = 18; //仪器列调整
    const Action_CANCEL_RECORD = 19; //仪器记录报废
    const Action_RECOVER_RECORD = 20; //仪器记录恢复


    /**
     * Notes:添加仪器
     * Author: hkk
     * Date: 2019/10/31 14:09
     * @param $instrumentData
     * @return array
     */
    public function addInstrument($instrumentData)
    {

        // 如果有对接类型
        if (isset($instrumentData['data_type'])) {
            $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
            $elnPaidItems = $companyData['data']['elnPaidItems'];
            $inscadaNumAll = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);
            $instrumentNum = InstrumentsModel::find()->where(['data_type' => 1])->asArray()->all();
            $instrumentFile = InstrumentsModel::find()->where(['data_type' => 2])->asArray()->all();

            $newDataType = $instrumentData['data_type'] == 0 ? 0 : 1;
            $inscadaNum = count(array_merge($instrumentNum, $instrumentFile)) + $newDataType;

            // 超过对接类型最大数量限制
            if ($inscadaNum > $inscadaNumAll) {
                return $this->fail('match_instrument_failed');
            }
        } else {
            // 没有对接类型，默认为无对接类型
            $instrumentData['data_type'] = 0;
        }

        $transaction = \Yii::$app->integle_ineln->beginTransaction();

        // add by hkk 2022/8/8 标记不限仪器，所有人可查看
        if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
            $instrumentData['groupIds'] = 'all';
        }

        $ins = new InstrumentsModel(['scenario' => 'editInstrument']);
        $ins->setAttributes($instrumentData);

        // if ($ins->validate()) {
        //     $ins->setAttributes($instrumentData);
        // } else {
        //     $err = 'batch_number_conflict';
        // }
        // 如果鹰群和部门全空那么就添加日志
        // bug 6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,添加bug日志
        if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
            \Yii::info('BUG #6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,操作：新增仪器,仪器数据: ' . json_encode($instrumentData), 'BugInvestigation');
        }

        if (!$ins->save()) {
            $transaction->rollBack();
            //! 获取model校验的错误,并对外返回第一条错误
            $errMses = array_values($ins->getFirstErrors());
            // bug 6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,添加bug日志
            if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
                \Yii::info('BUG #6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,操作：新增仪器,报错信息: ' . $errMses[0], 'BugInvestigation');
            }
            return $this->fail($errMses[0]);//只取第一条错误信息
            // return $this->fail('fail to add instrument');
        }
        $res = $this->addInstrumentHistory($ins['id'], self::ACTION_ADD, null); // 添加历史痕迹
        if ($res['status'] != 1) {
            $transaction->rollBack();
            return $this->fail('fail to add instrument');
        }
        $transaction->commit();
        return $this->success([]);
    }

    /**
     * Notes:编辑仪器
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function editInstrument($instrumentData)
    {

        // 根据check_status 和原本的check_situation确定新的check_situation
        $instrumentInfo = $this->getInsInfoById($instrumentData['id']);

        $originCheckSituation = $instrumentInfo['data']['check_situation'];

        if (isset($instrumentData['check_status']) && $instrumentData['check_status'] === '0') {
            $instrumentData['check_situation'] = 0;
            unset($instrumentData['check_status']);
        } else if (isset($instrumentData['check_status']) && $instrumentData['check_status'] === '1') {
            if ($originCheckSituation === 1) {
                $instrumentData['check_situation'] = 1; // 原来已校验就还是已校验
            } else {
                $instrumentData['check_situation'] = 2; // 原来未校验或无需校验要改为 未校验
            }
        }
        unset($instrumentData['check_status']);

        // 记录痕迹改变
        $action_details = '';
        if (isset($instrumentData['name']) && $instrumentInfo['data']['name'] !== $instrumentData['name']) {
            $action_details .= (\Yii::t('base', 'name') . ":" . $instrumentInfo['data']['name'] . "=>" . $instrumentData['name'] . '</br>');
        }
        if (isset($instrumentData['batch_number']) && $instrumentInfo['data']['batch_number'] !== $instrumentData['batch_number']) {
            $action_details .= (\Yii::t('base', 'batch_number') . ":" . $instrumentInfo['data']['batch_number'] . "=>" . $instrumentData['batch_number'] . '</br>');
        }
        if (isset($instrumentData['specification']) && $instrumentInfo['data']['specification'] !== $instrumentData['specification']) {
            $action_details .= (\Yii::t('base', 'specification') . ":" . $instrumentInfo['data']['specification'] . "=>" . $instrumentData['specification'] . '</br>');
        }
        if (isset($instrumentData['instrument_type']) && $instrumentInfo['data']['instrument_type'] !== $instrumentData['instrument_type']) { // add by hkk 2021/4/28  分类的痕迹改变
            //判断老数据。老数据是''，新数据是0，上面判断''!=0，但是实际类型一样，应该不显示历史。
            if ($instrumentInfo['data']['instrument_type'] != '' || $instrumentData['instrument_type'] != 0) {
                $action_details .= (\Yii::t('base', 'instrument_type') . ":" . $instrumentInfo['data']['instrument_type'] . "=>" . $instrumentData['instrument_type'] . '</br>');
            }
        }
        if (isset($instrumentData['data_type']) && $instrumentInfo['data']['data_type'] != $instrumentData['data_type']) { // add by hkk 2021/4/28  分类的痕迹改变
            //判断老数据。老数据是''，新数据是0，上面判断''!=0，但是实际类型一样，应该不显示历史。
            if ($instrumentInfo['data']['data_type'] != '' || $instrumentData['data_type'] != 0) {
                // 仪器状态：0-> 无，1-> 数值 ，2-> 文件
                $dataTypeMap = [
                    0 => \Yii::t('base', 'none'),
                    1 => \Yii::t('base', 'value'),
                    2 => \Yii::t('base', 'file'),
                ];

                $dataTypeBeforeKey = $instrumentInfo['data']['data_type'];
                $dataTypeAfterKey = $instrumentData['data_type'];
                $action_details .= (\Yii::t('base', 'data_type') . ":" . @getVar($dataTypeMap[$dataTypeBeforeKey]) . "=>" . @getVar($dataTypeMap[$dataTypeAfterKey]) . '</br>');
            }
        }
        if (isset($instrumentData['model']) && $instrumentInfo['data']['model'] !== $instrumentData['model']) {
            $action_details .= (\Yii::t('base', 'model') . ":" . $instrumentInfo['data']['model'] . "=>" . $instrumentData['model'] . '</br>');
        }
        if (isset($instrumentData['manufacturer']) && $instrumentInfo['data']['manufacturer'] !== $instrumentData['manufacturer']) {
            $action_details .= (\Yii::t('base', 'manufacturer') . ":" . $instrumentInfo['data']['manufacturer'] . "=>" . $instrumentData['manufacturer'] . '</br>');
        }
        if (isset($instrumentData['status']) && $instrumentInfo['data']['status'] != $instrumentData['status']) {
            // 仪器状态：0-> 已删除，1->正常，2->停用，3>维修中，4->报废
            $statusMap = [
                0 => \Yii::t('base', 'already_deleted'),
                1 => \Yii::t('base', 'normal'),
                2 => \Yii::t('base', 'suspend_use'),
                3 => \Yii::t('base', 'repairing'),
                4 => \Yii::t('base', 'scrap'),
            ];

            $action_details .= (\Yii::t('base', 'status') . ":" . $statusMap[$instrumentInfo['data']['status']] . "=>" . $statusMap[$instrumentData['status']] . '</br>');
        }
        if (isset($instrumentData['check_situation']) && $instrumentInfo['data']['check_situation'] != $instrumentData['check_situation']) {
            // 仪器状态：0-> 已删除，1->正常，2->停用，3>维修中，4->报废
            $checkMap = [
                0 => \Yii::t('base', 'noNeedCheck'),
                1 => \Yii::t('base', 'needCheck'),
                2 => \Yii::t('base', 'needCheck'),
            ];

            $action_details .= (\Yii::t('base', 'check') . ":" . $checkMap[$instrumentInfo['data']['check_situation']] . "=>" . $checkMap[$instrumentData['check_situation']] . '</br>');
        }
        if (isset($instrumentData['position']) && $instrumentInfo['data']['position'] !== $instrumentData['position']) {
            $action_details .= (\Yii::t('base', 'position') . ":" . $instrumentInfo['data']['position'] . "=>" . $instrumentData['position'] . '</br>');
        }


        if ((isset($instrumentData['responsible_person']) && $instrumentInfo['data']['responsible_person'] !== $instrumentData['responsible_person'])
            || (isset($instrumentData['in_charge_person']) && $instrumentInfo['data']['in_charge_person'] !== $instrumentData['in_charge_person'])
            || (isset($instrumentData['maintenance_person']) && $instrumentInfo['data']['maintenance_person'] !== $instrumentData['maintenance_person'])
        ) {

            $company_id = \Yii::$app->view->params['curr_company_id'];
            $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
            $userListNew = isset($userList['list']) ? $userList['list'] : [];
            $userListInfo = ArrayHelper::index($userListNew, 'id');

            if (isset($instrumentData['responsible_person']) && $instrumentInfo['data']['responsible_person'] !== $instrumentData['responsible_person']) {
                $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['responsible_person']);
                $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['responsible_person']);
                if ($beforeUserNameString !== $afterNameString) {
                    $action_details .= (\Yii::t('base', 'response_person') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                }
            }

            if (isset($instrumentData['in_charge_person']) && $instrumentInfo['data']['in_charge_person'] !== $instrumentData['in_charge_person']) {
                $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['in_charge_person']);
                $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['in_charge_person']);
                if ($beforeUserNameString !== $afterNameString) {
                    $action_details .= (\Yii::t('base', 'person_in_charge') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                }
            }

            if (isset($instrumentData['maintenance_person']) && $instrumentInfo['data']['maintenance_person'] !== $instrumentData['maintenance_person']) {
                $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['maintenance_person']);
                $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['maintenance_person']);
                if ($beforeUserNameString !== $afterNameString) {
                    $action_details .= (\Yii::t('base', 'maintainer') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                }
            }
        }


        if (isset($instrumentData['supplier']) && $instrumentInfo['data']['supplier'] !== $instrumentData['supplier']) {
            $action_details .= (\Yii::t('base', 'supplier') . ":" . $instrumentInfo['data']['supplier'] . "=>" . $instrumentData['supplier'] . '</br>');
        }
        if (isset($instrumentData['remark']) && $instrumentInfo['data']['remark'] !== $instrumentData['remark']) {
            $action_details .= (\Yii::t('base', 'remark') . ":" . $instrumentInfo['data']['remark'] . "=>" . $instrumentData['remark'] . '</br>');
        }
        if (isset($instrumentData['groupIds']) && $instrumentInfo['data']['groupIds'] !== $instrumentData['groupIds']) {
            $groupList = (new CenterInterface())->getGroupsListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所有鹰群信息
            $groupList = array_column($groupList, 'group_name', 'id'); // 鹰群名数组
            // 改了鹰群就加痕迹
            $beforeGroupNameString = $this->getGroupNameStrings($groupList, $instrumentInfo['data']['groupIds']);
            $afterGroupString = $this->getGroupNameStrings($groupList, $instrumentData['groupIds']);
            if ($beforeGroupNameString !== $afterGroupString) {
                $action_details .= (\Yii::t('base', 'group') . ":" . $beforeGroupNameString . "=>" . $afterGroupString . '</br>');
            }
        }
        if (isset($instrumentData['departmentIds']) && $instrumentInfo['data']['departmentIds'] !== $instrumentData['departmentIds']) {

            // 获取部门信息
            $redis = \Yii::$app->redis;
            $allDepartments = $redis->get('department_list');
            if (!empty($allDepartments)) {
                $allDepartments = json_decode($allDepartments, TRUE);
            }
            $allDepartments = array_column($allDepartments, 'department_name', 'id'); // 部门名数组

            $beforeDepartmentNameString = $this->getGroupNameStrings($allDepartments, $instrumentInfo['data']['departmentIds']);
            $afterDepartmentString = $this->getGroupNameStrings($allDepartments, $instrumentData['departmentIds']);
            if ($beforeDepartmentNameString !== $afterDepartmentString) {
                $action_details .= (\Yii::t('base', 'department') . ":" . $beforeDepartmentNameString . "=>" . $afterDepartmentString . '</br>');
            }
        }

        // add by hkk 2022/8/8 兼容不限仪器 鹰群和部门都空白代表不限仪器
        if (isset($instrumentData['groupIds']) && isset($instrumentData['departmentIds']) && empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
            $instrumentData['groupIds'] = 'all';
        }


        // 自定义字段的痕迹改变  add by hkk 2021/4/28
        if ($instrumentInfo['data']['data1']) {
            $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1])->asArray()->one();
            $defineFields = $defineRecord ? $defineRecord : [];
            for ($i = 1; $i <= 20; $i++) {
                if ($instrumentInfo['data']['data' . $i] && isset($instrumentData['data' . $i]) && $instrumentInfo['data']['data' . $i] !== $instrumentData['data' . $i]) {
                    $action_details .= ($defineFields['field' . $i] . ":" . $instrumentInfo['data']['data' . $i] . "=>" . $instrumentData['data' . $i] . '</br>');
                }
            }
        }

//        if (isset($instrumentData['batch_number']) &&  $instrumentInfo['data']['batch_number'] !== $instrumentData['batch_number']) {
//            $model = new InstrumentsModel(['scenario' => 'editInstrument']);
//            $model->attributes = $instrumentData;
//            if ($model->validate()) {
//                $model::updateAll($instrumentData, [
//                    'id' => $instrumentData['id']
//                ]);
//            } else {
//                return ['errMsg' => 'batch_number_conflict'];
//            }
//        } else {
//            InstrumentsModel::updateAll($instrumentData, [
//                'id' => $instrumentData['id']
//            ]);
//        }

        //多个仪器设备id重复，修改其中一个内容时必须修改其设备id
        if (isset($instrumentData['batch_number']) && !empty($instrumentData['batch_number'])) { // bug#32772 没有配置仪器ID字段时需要先校验这个字段，不然走到这里会报错 jiangdm 2023/3/9
            $batch = $this->getRepeatBatchNumber($instrumentData['batch_number'], $instrumentData['id']);
            if ($batch > 0) {
                return ['errMsg' => 'batch_number_conflict'];
            }
        }
        if (isset($instrumentData['data_type'])) {
            $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
            $elnPaidItems = $companyData['data']['elnPaidItems'];
            $inscadaNumAll = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);
            $instrumentNum = InstrumentsModel::find()->where(['data_type' => 1])->asArray()->all();
            $instrumentFile = InstrumentsModel::find()->where(['data_type' => 2])->asArray()->all();

            if ($instrumentData['data_type'] != $instrumentInfo['data']['data_type']) {
                $newDataType = $instrumentData['data_type'] == 0 ? 0 : 1;
                $oldDataType = $instrumentInfo['data']['data_type'] == 0 ? 0 : 1;
                $inscadaNum = count(array_merge($instrumentNum, $instrumentFile)) + $newDataType - $oldDataType;
            } else {
                $inscadaNum = count(array_merge($instrumentNum, $instrumentFile));
            }

            if ($inscadaNum > $inscadaNumAll) {
                return ['errMsg' => 'match_instrument_failed'];
            }
        } else {
            $instrumentData['data_type'] = $instrumentInfo['data']['data_type'];
        }

        $model = new InstrumentsModel(['scenario' => 'editInstrument']);
        $Ins = $model::findOne($instrumentData['id']);
        $Ins->setAttributes($instrumentData);

        // 如果鹰群和部门全空那么就添加日志
        // bug 6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,添加bug日志
        if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
            \Yii::info('BUG #6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,操作：编辑仪器,仪器数据: ' . json_encode($instrumentData) . '之前的仪器数据：' . json_encode($instrumentInfo['data']), 'BugInvestigation');
        }

        if (!$Ins->save()) {
            $errMsg = current($Ins->getFirstErrors());
            // 如果鹰群和部门全空那么就添加日志
            // bug 6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,添加bug日志
            if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
                \Yii::info('BUG #6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,操作：编辑仪器,报错信息: ' . json_encode($errMsg), 'BugInvestigation');
            }
            return $this->fail($errMsg);
        }

        if ($action_details != '') {
            $this->addInstrumentHistory($instrumentData['id'], self::ACTION_EDIT, ['action_details' => $action_details]);
        }

        return $this->success([]);
    }

    /**
     * @Notes: 根据条件精确查找一个仪器
     * @param $where
     * @return array
     * @author: tianyang
     * @Time: 2023/6/8 19:29
     */
    public function getExactInstrument($where)
    {
        $query = InstrumentsModel::find()
            ->where($where)
            ->orderBy('id DESC')
            ->asArray()->one();
        return !empty($query) ? $query : [];
    }

    /**
     * Notes:批量编辑仪器,根据选定字段
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $instrumentData
     * @param $userListInfo
     * @param $defineRecord
     * @param $defineFieldIndex
     * @return array
     * @throws Exception
     */
    public function editBatchInstrument($instrumentData, $userListInfo, $defineRecord, $defineFieldIndex)
    {

        // 查找原仪器信息
        $instrumentInfo = $this->getInsInfoById($instrumentData['id']);
        $instrumentInfo['data']['instrument_type'] = !empty($instrumentInfo['data']['instrument_type']) ? $instrumentInfo['data']['instrument_type'] : '';


        // 校验调整
        if (isset($instrumentData['check_status'])) {
            $originCheckSituation = $instrumentInfo['data']['check_situation'];
            if ($instrumentData['check_status'] == '0') {
                $instrumentData['check_situation'] = 0;
            } else if ($instrumentData['check_status'] == '1') {
                if ($originCheckSituation === 1) {
                    $instrumentData['check_situation'] = 1; // 原来已校验就还是已校验
                } else {
                    $instrumentData['check_situation'] = 2; // 原来未校验或无需校验要改为 未校验
                }
            }
            unset($instrumentData['check_status']);
        }

        // 记录痕迹改变
        $action_details = '';
        if (isset($instrumentData['name']) && $instrumentInfo['data']['name'] != $instrumentData['name']) {
            $action_details .= (\Yii::t('base', 'name') . ":" . $instrumentInfo['data']['name'] . "=>" . $instrumentData['name'] . '</br>');
        }
        if (isset($instrumentData['batch_number']) && $instrumentInfo['data']['batch_number'] != $instrumentData['batch_number']) {
            $action_details .= (\Yii::t('base', 'batch_number') . ":" . $instrumentInfo['data']['batch_number'] . "=>" . $instrumentData['batch_number'] . '</br>');
        }
        if (isset($instrumentData['specification']) && $instrumentInfo['data']['specification'] != $instrumentData['specification']) {
            $action_details .= (\Yii::t('base', 'specification') . ":" . $instrumentInfo['data']['specification'] . "=>" . $instrumentData['specification'] . '</br>');
        }
        if (isset($instrumentData['instrument_type']) && $instrumentInfo['data']['instrument_type'] != $instrumentData['instrument_type']) { // add by hkk 2021/4/28  分类的痕迹改变

            $action_details .= (\Yii::t('base', 'instrument_type') . ":" . $instrumentInfo['data']['instrument_type'] . "=>" . $instrumentData['instrument_type'] . '</br>');
        }
        if (isset($instrumentData['model']) && $instrumentInfo['data']['model'] != $instrumentData['model']) {
            $action_details .= (\Yii::t('base', 'model') . ":" . $instrumentInfo['data']['model'] . "=>" . $instrumentData['model'] . '</br>');
        }
        if (isset($instrumentData['manufacturer']) && $instrumentInfo['data']['manufacturer'] != $instrumentData['manufacturer']) {
            $action_details .= (\Yii::t('base', 'manufacturer') . ":" . $instrumentInfo['data']['manufacturer'] . "=>" . $instrumentData['manufacturer'] . '</br>');
        }
        if (isset($instrumentData['status']) && $instrumentInfo['data']['status'] != $instrumentData['status']) {
            // 仪器状态：0-> 已删除，1->正常，2->停用，3>维修中，4->报废
            $statusMap = [
                0 => \Yii::t('base', 'already_deleted'),
                1 => \Yii::t('base', 'normal'),
                2 => \Yii::t('base', 'suspend_use'),
                3 => \Yii::t('base', 'repairing'),
                4 => \Yii::t('base', 'scrap'),
            ];

            $action_details .= (\Yii::t('base', 'status') . ":" . $statusMap[$instrumentInfo['data']['status']] . "=>" . $statusMap[$instrumentData['status']] . '</br>');
        }
        if (isset($instrumentData['check_situation']) && $instrumentInfo['data']['check_situation'] != $instrumentData['check_situation']) {
            // 仪器状态：0-> 已删除，1->正常，2->停用，3>维修中，4->报废
            $checkMap = [
                0 => \Yii::t('base', 'noNeedCheck'),
                1 => \Yii::t('base', 'needCheck'),
                2 => \Yii::t('base', 'needCheck'),
            ];

            $action_details .= (\Yii::t('base', 'check') . ":" . $checkMap[$instrumentInfo['data']['check_situation']] . "=>" . $checkMap[$instrumentData['check_situation']] . '</br>');
        }
        if (isset($instrumentData['position']) && $instrumentInfo['data']['position'] != $instrumentData['position']) {
            $action_details .= (\Yii::t('base', 'position') . ":" . $instrumentInfo['data']['position'] . "=>" . $instrumentData['position'] . '</br>');
        }
        if (isset($instrumentData['responsible_person']) && $instrumentInfo['data']['responsible_person'] != $instrumentData['responsible_person']) {
            $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['responsible_person']);
            $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['responsible_person']);
            $action_details .= (\Yii::t('base', 'response_person') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
        }
        if (isset($instrumentData['in_charge_person']) && $instrumentInfo['data']['in_charge_person'] != $instrumentData['in_charge_person']) {
            $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['in_charge_person']);
            $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['in_charge_person']);
            $action_details .= (\Yii::t('base', 'person_in_charge') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
        }
        if (isset($instrumentData['maintenance_person']) && $instrumentInfo['data']['maintenance_person'] != $instrumentData['maintenance_person']) {
            $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['maintenance_person']);
            $afterNameString = $this->getUsersNameStrings($userListInfo, $instrumentData['maintenance_person']);
            $action_details .= (\Yii::t('base', 'maintainer') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
        }
        if (isset($instrumentData['supplier']) && $instrumentInfo['data']['supplier'] != $instrumentData['supplier']) {
            $action_details .= (\Yii::t('base', 'supplier') . ":" . $instrumentInfo['data']['supplier'] . "=>" . $instrumentData['supplier'] . '</br>');
        }
        if (isset($instrumentData['remark']) && $instrumentInfo['data']['remark'] != $instrumentData['remark']) {
            $action_details .= (\Yii::t('base', 'remark') . ":" . $instrumentInfo['data']['remark'] . "=>" . $instrumentData['remark'] . '</br>');
        }
        if (!empty($instrumentData['data_type'])) {
            $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
            $elnPaidItems = $companyData['data']['elnPaidItems'];
            $inscadaNumAll = @getVar($elnPaidItems['instrumentsInterfacingNumber'], 0);
            $instrumentNum = InstrumentsModel::find()->where(['data_type' => 1])->asArray()->all();
            $instrumentFile = InstrumentsModel::find()->where(['data_type' => 2])->asArray()->all();

            if ($instrumentData['data_type'] != $instrumentInfo['data']['data_type']) {
                $newDataType = $instrumentData['data_type'] == 0 ? 0 : 1;
                $oldDataType = $instrumentInfo['data']['data_type'] == 0 ? 0 : 1;
                $inscadaNum = count(array_merge($instrumentNum, $instrumentFile)) + $newDataType - $oldDataType;
            } else {
                $inscadaNum = count(array_merge($instrumentNum, $instrumentFile));
            }

            if ($inscadaNum > $inscadaNumAll) {
                return ['errMsg' => 'match_instrument_failed'];
            }
        } else {
            $instrumentData['data_type'] = $instrumentInfo['data']['data_type'];
        }

        // 自定义字段的痕迹改变  add by hkk 2021/4/28
        foreach ($defineFieldIndex as $defineIndex) {
            if (isset($instrumentData['data' . $defineIndex]) && $instrumentInfo['data']['data' . $defineIndex] != $instrumentData['data' . $defineIndex]) {
                $action_details .= ($defineRecord['field' . $defineIndex] . ":" . $instrumentInfo['data']['data' . $defineIndex] . "=>" . $instrumentData['data' . $defineIndex] . '</br>');
            }
        }

        // 如果鹰群和部门全空那么就添加日志
        if (empty($instrumentData['groupIds']) && empty($instrumentData['departmentIds'])) {
            \Yii::info('BUG #6513 人福普克：仪器管理员编辑仪器后，实验员在【我的-仪器库】无法搜索到该仪器,操作：批量编辑仪器,仪器数据: ' . json_encode($instrumentData) . '之前的仪器数据：' . json_encode($instrumentInfo['data']), 'BugInvestigation');
        }

        InstrumentsModel::updateAll($instrumentData, [
            'id' => $instrumentData['id']
        ]);

        if ($action_details != '') {
            $this->addInstrumentHistory($instrumentData['id'], self::ACTION_EDIT, ['action_details' => $action_details]);
        }

        return $this->success([]);
    }

    /**
     * Notes:根据userIds生成姓名字符串
     * Author: hkk
     * Date: 2020/4/29 13:25
     * @param $userListInfo
     * @param $userIds '1,2,3,4'
     * @return string 'jack,liming,Tokyo,Peking'
     */
    public function getUsersNameStrings($userListInfo, $userIds)
    {
        //分别生成责任人，维护人，维修人的用户名字符串
        $UserNames = [];
        if (!empty($userIds)) {
            $userIds = explode(',', $userIds);
            foreach ($userIds as $user) {
                $currentUser = isset($userListInfo[$user]) ? $userListInfo[$user] : '';
                $UserNames[] = $currentUser ? CommentServer::displayUserName($currentUser) : '';
            }
        }
        return join(',', $UserNames);
    }

    /**
     * Notes:根据groupIds生成鹰群字符串
     * Author: hkk
     * Date: 2020/4/29 13:25
     * @param $groupListInfo
     * @param $groupIds '1,2,3,4'
     * @return string 'jack,liming,Tokyo,Peking'
     */
    public function getGroupNameStrings($groupListInfo, $groupIds)
    {
        //分别生成责任人，维护人，维修人的用户名字符串
        $groupNames = [];
        if (!empty($groupIds)) {
            $groupIds = explode(',', $groupIds);
            foreach ($groupIds as $groupId) {
                $groupNames[] = isset($groupListInfo[$groupId]) ? $groupListInfo[$groupId] : '';
            }
        }
        return join(',', $groupNames);
    }

    /**
     * Notes:报修仪器
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function repairInstrument($instrumentData)
    {
        if ($instrumentData['approval_status'] == '1') {
            $this->instrumentStatusCreateApproval($instrumentData['id'], 3, $instrumentData['remark'], 1);
        } else {
            InstrumentsModel::updateAll(['status' => 3, 'update_by' => $instrumentData['update_by'],], ['id' => $instrumentData['id']]);
        }

        $action_details = \Yii::t('base', 'instrument_repair_reason') . ":" . $instrumentData['remark'];
        $data = [
            'email_cc_ids' => $instrumentData['email_cc_ids'],
            'action_details' => $action_details
        ];

        // 发送消息和邮件，默认发给创建人、维护人、维修人，和填写的抄送人
        $instrumentDetails = InstrumentsModel::findOne(['id' => $instrumentData['id']])->toArray();
        $createUserId = array_filter(explode(',', $instrumentDetails['create_by']));
        $inChargeUserIds = array_filter(explode(',', $instrumentDetails['in_charge_person']));
        $maintenanceUserIds = array_filter(explode(',', $instrumentDetails['maintenance_person']));
        $emailccIds = array_filter(explode(',', $instrumentData['email_cc_ids']));
        $emailToIds = array_keys(array_flip($createUserId) + array_flip($inChargeUserIds) + array_flip($maintenanceUserIds));
        $sendById = \Yii::$app->view->params['curr_user_id'];
        $msgToIds = array_keys(array_flip($createUserId) + array_flip($inChargeUserIds) + array_flip($maintenanceUserIds) + array_flip($emailccIds));
        $msgToIds = array_unique($msgToIds);
        $allUserIds = array_merge($msgToIds, [$sendById]);
        $userInfoArr = (new CenterInterface())->userDetailsByUserIds($allUserIds); // 获取用户详情
        $userInfoArr = ArrayHelper::index($userInfoArr, 'id');
        $instrumentLink = '<a href="' . ELN_URL . '?route=my_instruments&name=' . $instrumentDetails['name'] . '" target="_blank">' . $instrumentDetails['name'] . '</a>';
        $msgContentParams = [
            CommentServer::displayUserName($userInfoArr[$sendById]), //发送者名称
            $instrumentLink, //仪器链接
            $instrumentDetails['batch_number'],
            $instrumentData['remark']
//            'http://plugeln.ineln.com/', // 点击无效用的url
        ];


        //发送消息
        $userLang = \Yii::$app->language;
        $elnMsgSer = new ElnMessageServer();
        $msgTitle = \Yii::t('msg', 'instrument_repair_message', [], $userLang);
        $msgContent = \Yii::t('msg', 'instrument_repair_message_content', $msgContentParams, $userLang);
        $elnMsgSer->addMessage($sendById, $msgToIds, $msgTitle, $msgContent, 19, false, \Yii::$app->view->params['curr_company_id']);


        // 发送邮件
        $emailTitle = \Yii::t('msg', 'instrument_repair_message', [], $userLang);
        $emailContent = \Yii::t('msg', 'instrument_repair_email_content', $msgContentParams, $userLang);
        $elnMsgSer->addMail($sendById, $emailToIds, $emailccIds, $emailTitle, $emailContent, 1, \Yii::$app->language);

        $this->addInstrumentHistory($instrumentData['id'], self::ACTION_REPAIR, $data);
        return $this->success([]);
    }

    /**
     * Notes:删除仪器
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $instrumentIds
     * @return array
     * @throws Exception
     */
    public function deleteInstrument($instrumentIds, $approvalReason = '')
    {
        $instrumentInfo = $this->getInsInfoById($instrumentIds);
        $recordSetting = json_decode($instrumentInfo['data']['record_setting'], true);
        if (isset($recordSetting['delete_must_check_users'])) {
            $this->instrumentStatusCreateApproval($instrumentIds, 0, $approvalReason, 1);
        } else {
            InstrumentsModel::updateAll(
                ['status' => 0, 'update_by' => \Yii::$app->view->params['curr_user_id']],
                ['id' => $instrumentIds]
            );

            $this->addInstrumentHistory($instrumentIds, self::ACTION_DELETE, null);

        }

        return $this->success([]);
    }

    /**
     * Notes: 预约仪器
     * Author: hkk
     * Date: 2019/11/4 10:30
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function bookInstrument($instrumentData)
    {

        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $book = new InstrumentsBookModel();
        $book->setAttributes($instrumentData);
        if (!$book->save()) {
            $transaction->rollBack();
            return $this->fail('fail to add instrument');
        }


        // 痕迹记录 预约时间 相关实验 备注
        $action_details = '';
        $action_details .= (\Yii::t('base', 'instrument_book_time') . ":" . $instrumentData['start_time'] . ' -- ' . $instrumentData['end_time'] . '</br>');
        if ($instrumentData['related_experiment'] !== '') {
            $action_details .= (\Yii::t('base', 'instrument_related_experiments') . ":" . $instrumentData['related_experiment'] . '</br>');
        }
        if ($instrumentData['remark'] !== '') {
            $action_details .= (\Yii::t('base', 'remark') . ":" . $instrumentData['remark']);
        }
        $data = [
            'action_details' => $action_details
        ];


        $res = $this->addInstrumentHistory($book['instrument_id'], self::ACTION_BOOK, $data); // 添加历史痕迹
        if ($res['status'] != 1) {
            $transaction->rollBack();
            return $this->fail('fail to add instrument');
        }
        $transaction->commit();
        return $this->success([]);
    }

    /**
     * Notes: 编辑已预约记录
     * Author: hkk
     * Date: 2019/11/5 10:30
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function editBookInstrument($instrumentData)
    {

        // 先查询当前预约信息，用于记录痕迹改变
        $originBookInfo = $this->getBookInfoById($instrumentData['book_id']);

        $updateData = [
            'start_time' => $instrumentData['start_time'],
            'end_time' => $instrumentData['end_time'],
            'remark' => $instrumentData['remark'],
            'related_experiment' => $instrumentData['related_experiment'],
            'update_by' => $instrumentData['update_by'],
        ];

        InstrumentsBookModel::updateAll($updateData, [
            'id' => $instrumentData['book_id']
        ]);


        // 记录痕迹改变 开始时间 结束时间 备注 相关实验
        $action_details = '';
        if ($originBookInfo['data']['start_time'] !== $updateData['start_time']) {
            $action_details .= (\Yii::t('base', 'start_time') . ":" . $originBookInfo['data']['start_time'] . "=>" . $updateData['start_time'] . '</br>');
        }
        if ($originBookInfo['data']['end_time'] !== $updateData['end_time']) {
            $action_details .= (\Yii::t('base', 'end_time') . ":" . $originBookInfo['data']['end_time'] . "=>" . $updateData['end_time'] . '</br>');
        }
        if ($originBookInfo['data']['related_experiment'] !== $updateData['related_experiment']) {
            $action_details .= (\Yii::t('base', 'instrument_related_experiments') . ":" . $originBookInfo['data']['related_experiment'] . "=>" . $updateData['related_experiment'] . '</br>');
        }
        if ($originBookInfo['data']['remark'] !== $updateData['remark']) {
            $action_details .= (\Yii::t('base', 'remark') . ":" . $originBookInfo['data']['remark'] . "=>" . $updateData['remark'] . '</br>');
        }


        $data = [
            'action_details' => $action_details
        ];

        $this->addInstrumentHistory($instrumentData['instrument_id'], self::ACTION_EDIT_BOOK, $data);
        return $this->success([]);
    }

    /**
     * Notes: 删除仪器预约
     * Author: hkk
     * Date: 2019/11/5 13:47
     * @param $instrumentIds
     * @return array
     * @throws Exception
     */
    public function deleteInstrumentBook($bookIds)
    {

        InstrumentsBookModel::updateAll(
            ['status' => 0, 'update_by' => \Yii::$app->view->params['curr_user_id']],
            ['id' => $bookIds]
        );

        // 查询每个预约记录所属的仪器，添加痕迹 预约删除记录
        $results = InstrumentsBookModel::find()
            ->select('instrument_id,start_time,end_time')
            ->where(['id' => $bookIds])
            ->asArray()->all();


        foreach ($results as $item) {
            $action_details = (\Yii::t('base', 'instrument_book_time') . ":" . $item['start_time'] . ' -- ' . $item['end_time'] . '</br>');
            $this->addInstrumentHistory($item['instrument_id'], self::ACTION_DELETE_BOOK, ['action_details' => $action_details]);
        }

        return $this->success([]);
    }

    /**
     * Notes: 判断预约仪器冲突
     * Author: hkk
     * Date: 2019/11/4 10:30
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function bookInstrumentConflict($instrumentData)
    {

        // 查询历史记录，
        $instrumentsBookData = $this->listInstrumentBook($instrumentData['instrument_id']);

        //有预约记录id说明是修改预约判断时，排除当前项
        $excludeCurrent = false;
        if (isset($instrumentData['book_id'])) {
            $excludeCurrent = true;
        }

        foreach ($instrumentsBookData as $key => $item) {

            if ($excludeCurrent && $item['id'] == $instrumentData['book_id']) {
                continue;
            }

            if (strtotime($instrumentData['start_time']) < strtotime($item['end_time']) && strtotime($instrumentData['end_time']) > strtotime($item['start_time'])) {

                //冲突，直接返回冲突时间段
                return [
                    'conflict_start_time' => $item['start_time'],
                    'conflict_end_time' => $item['end_time']
                ];
            }
        }

        return $this->success([]);
    }

    /**
     * Notes:添加仪器痕迹
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $id
     * @param $action
     * @param $data
     * @return array
     */
    public function addInstrumentHistory($id, $action, $data)
    {
        $historyData = [
            'instrument_id' => $id,
            'action' => $action,
            'create_by' => \Yii::$app->view->params['curr_user_id'],
            'status' => 1,
            'action_details' => @getVar($data['action_details']),
            'remark' => @getVar($data['orderNumber'])
        ];

        if ($action == self::ACTION_REPAIR) { // 报修，要保存备注和邮件抄送
            $historyData['email_cc_ids'] = @getVar($data['email_cc_ids']);
        }
        $his = new InstrumentsActionRecordModel();
        $his->setAttributes($historyData);
        if (!$his->save()) {
            return $this->fail('fail to add instrument history');
        }

        return $this->success([]);
    }

    /**
     * Notes:获取所有仪器详情
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listInstrumentsView($where, $limit, $page, $forExport = false)
    {

        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->leftJoin(InstrumentBindingModel::tableName() . ' AS ib', 'ib.batch_number=Ins.batch_number AND ib.status=1'); // 真实仪器 // add by zwm 2022/10/27

        $query->select[] = 'Ins.id';
        $query->select[] = '`Ins`.`name` AS name'; // 增加中文排序
        $query->select[] = '`Ins`.`batch_number` AS batch_number'; // 增加中文排序
        $query->select[] = '`Ins`.`specification` AS specification'; // 规格
        $query->select[] = 'Ins.instrument_type'; // 仪器分类 2021/4/28
        $query->select[] = '`Ins`.`model` AS model';        // 货号/型号
        $query->select[] = '`Ins`.`manufacturer` AS manufacturer';
        $query->select[] = '`Ins`.`position` AS position';
        $query->select[] = 'Ins.responsible_person';   // 负责人
        $query->select[] = 'Ins.in_charge_person';     // 维护人
        $query->select[] = 'Ins.maintenance_person';   // 维修人
        $query->select[] = '`Ins`.`supplier`  AS supplier';
        $query->select[] = '`Ins`.`remark` AS remark';
        $query->select[] = 'Ins.files';
        $query->select[] = 'Ins.pictures';
        $query->select[] = 'Ins.check_situation';
        $query->select[] = 'Ins.start_expiry_time';
        $query->select[] = 'Ins.end_expiry_time';
        $query->select[] = 'Ins.start_check_time';
        $query->select[] = 'Ins.end_check_time';
        $query->select[] = 'Ins.start_running_time';
        $query->select[] = 'Ins.end_running_time';
        $query->select[] = 'Ins.repair_start_time';
        $query->select[] = 'Ins.repair_end_time';
        $query->select[] = 'Ins.create_by';
        $query->select[] = 'Ins.create_time';
        $query->select[] = 'Ins.status';
        $query->select[] = 'Ins.data_type';
        $query->select[] = 'Ins.numerical_instrument_type';
        $query->select[] = 'Ins.groupIds';
        $query->select[] = 'Ins.departmentIds';
        $query->select[] = 'Ins.available_slots';
        $query->select[] = 'Ins.max_advance_day';
        $query->select[] = 'Ins.min_advance';
        $query->select[] = 'Ins.max_booking_duration';
        $query->select[] = '`Ins`.`data1` AS data1';
        $query->select[] = '`Ins`.`data2` AS data2';
        $query->select[] = '`Ins`.`data3` AS data3';
        $query->select[] = '`Ins`.`data4` AS data4';
        $query->select[] = '`Ins`.`data5` AS data5';
        $query->select[] = '`Ins`.`data6` AS data6';
        $query->select[] = '`Ins`.`data7` AS data7';
        $query->select[] = '`Ins`.`data8` AS data8';
        $query->select[] = '`Ins`.`data9` AS data9';
        $query->select[] = '`Ins`.`data10` AS data10';
        $query->select[] = '`Ins`.`data11` AS data11';
        $query->select[] = '`Ins`.`data12` AS data12';
        $query->select[] = '`Ins`.`data13` AS data13';
        $query->select[] = '`Ins`.`data14` AS data14';
        $query->select[] = '`Ins`.`data15` AS data15';
        $query->select[] = '`Ins`.`data16` AS data16';
        $query->select[] = '`Ins`.`data17` AS data17';
        $query->select[] = '`Ins`.`data18` AS data18';
        $query->select[] = '`Ins`.`data19` AS data19';
        $query->select[] = '`Ins`.`data20` AS data20';
        $query->select[] = 'ib.status as instrument_binding_status'; // 是否连接真实仪器 // add by zwm 2022/10/27

        if (!empty($where['chooseIds'])) { // add by hkk 2021/4/28 批量编辑导出
            $query->andWhere(['Ins.id' => $where['chooseIds']]);
        }
        if (!empty($where['name'])) { // modified by hkk 2020/6/10  改成多字段模糊匹配
            $where['name'] = trim($where['name']);
            $query->andFilterWhere(['like', 'Ins.batch_number', $where['name']])
                ->orFilterWhere(['like', 'Ins.specification', $where['name']])
                ->orFilterWhere(['like', 'Ins.model', $where['name']])
                ->orFilterWhere(['like', 'Ins.manufacturer', $where['name']])
                ->orFilterWhere(['like', 'Ins.position', $where['name']])
                ->orFilterWhere(['like', 'Ins.name', $where['name']]);
        }

        if (!empty($where['batch_number'])) {
            $query->andWhere(['Ins.batch_number' => $where['batch_number']]);
        }

        // added by xyx 2023.11.14 bug326 不能使用'name'，会进行模糊查询，新起一个key用来准确查询
        if (!empty($where['reference_name'])) {
            $query->andWhere(['Ins.name' => $where['reference_name']]);
        }

        if (!empty($where['position'])) {
            $query->andWhere(['like', 'Ins.position', $where['position']]);
        }

        if (!empty($where['supplier'])) {
            $query->andWhere(['like', 'Ins.supplier', $where['supplier']]);
        }

        if (!empty($where['manufacturer'])) {
            $query->andWhere(['like', 'Ins.manufacturer', $where['manufacturer']]);
        }

        if (!empty($where['remark'])) {
            $query->andWhere(['like', 'Ins.remark', $where['remark']]);
        }

        if (!empty($where['instrument_type'])) { // add by hkk 2021/4/28
            $query->andWhere(['Ins.instrument_type' => $where['instrument_type']]);
        }

        // 对接类型筛选
        if (!@emptyExclude0($where['data_type'])) {
            if ($where['data_type'] == 0) {
                // 包括使用默认值NULL的情况
                $query->andWhere(['or', ['=', 'Ins.data_type', '0'], ['IS', 'Ins.data_type', new \yii\db\Expression('NULL')]]);
            } else {
                $query->andWhere(['Ins.data_type' => $where['data_type']]);
            }
        }

        if (!@emptyExclude0($where['status'])) {
            $query->andWhere(['Ins.status' => $where['status']]);
        } else {
            $query->andWhere(['Ins.status' => [1, 2, 3, 4]]); // 默认不显示删除的仪器
        }

        $today = date("Y-m-d H:i");
        //校验有效期内的仪器id
        $checkedIds = $this->ifInExpiryTime([], $today);
        //不在校验有效期内的仪器id
        $unCheckedIds = $this->ifInExpiryTime([], $today, null, false);
        if (isset($where['check_situation']) && is_array($where['check_situation'])) { //如果是数组[0,1]有效期内或者无需校验的
            $query->andWhere([
                'or',
                ['Ins.id' => $checkedIds],
                ['=', 'Ins.check_situation', '0']
            ]);
        } elseif (!@emptyExclude0($where['check_situation'])) {
            if ($where['check_situation'] === '1') { // 已校验包括 situation=1且在有效期内
                $query->andWhere(['Ins.id' => $checkedIds]);
                $query->andWhere(['Ins.check_situation' => $where['check_situation']]);
            } else if ($where['check_situation'] === '2') { // 未校验包括 situation=1 且超过有效期 // add by hkk 2020/7/27
                $query->andWhere(
                    ['Ins.id' => $unCheckedIds]
                );
            } else { //无需校验
                $query->andWhere(['Ins.check_situation' => $where['check_situation']]);
            }
        }

        if (!empty($where['create_by'])) {
            $query->andWhere(['Ins.create_by' => $where['create_by']]);
        }

        if (!empty($where['responsible_person'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:responsible_person, Ins.responsible_person)'));
            $query->addParams([':responsible_person' => $where['responsible_person']]);
        }

        if (!empty($where['in_charge_person'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:in_charge_person, Ins.in_charge_person)'));
            $query->addParams([':in_charge_person' => $where['in_charge_person']]);
        }

        if (!empty($where['maintenance_person'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:maintenance_person, Ins.maintenance_person)'));
            $query->addParams([':maintenance_person' => $where['maintenance_person']]);
        }

        if (!empty($where['viewAuth']) && $where['viewAuth'] == '2') { // add by hkk 2022/6/27 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }

            $FindStr .= "FIND_IN_SET('all', Ins.groupIds)"; // add by hkk 2022/8/8  加不限仪器的判断
            // $FindStr = substr($FindStr, 0, -4);
            $query->andWhere(new Expression($FindStr));
        }

        if (!empty($where['belong_group'])) { // add by hkk 2022/6/27 所属鹰群筛选
            $belong_groups = explode(',', $where['belong_group']);
            $FindArr = ['or'];
            foreach ($belong_groups as $belong_group) {
                $FindArr[] = "FIND_IN_SET($belong_group, Ins.groupIds)";
            }
            $query->andWhere($FindArr);
        }

        if (!empty($where['belong_department'])) { // add by hkk 2022/6/27 所属部门筛选
            $belong_departments = explode(',', $where['belong_department']);
            $FindArr = ['or'];
            foreach ($belong_departments as $belong_department) {
                $FindArr[] = "FIND_IN_SET($belong_department, Ins.departmentIds)";
            }
            $query->andWhere($FindArr);
        }

        if (!empty($where['start_time'])) {
            $query->andWhere(['>=', 'Ins.create_time', $where['start_time']]);
        }

        if (!empty($where['end_time'])) {
            $query->andWhere(['<=', 'Ins.create_time', $where['end_time']]);
        }

        // 仪器 id
        if (!empty($where['instrument_id'])) {
            $query->andWhere(['Ins.id' => $where['instrument_id']]);
        }

        $query->groupBy('Ins.id'); // add by hkk 2022/5/26 增加分组用于对管理字段的一对一
        $totalCount = $query->count();
        if (!$forExport) {
            $query->offset(($page - 1) * $limit)->limit($limit);
        }

        $orderString = 'Ins.create_time desc'; // 默认创建时间排序
        if (!empty($where['order_type']) && $where['order_type'] !== 'default') {
            if ($where['order_field_type'] == 'define') {  // 基础自定义字段排序
                $field = 'Ins.data' . substr($where['order_field'], 5); // 去掉'field'
                $orderString = "CONVERT( $field USING gbk)" . $where['order_type'];
            } else if ($where['order_field_type'] == 'manage') {  // 管理字段排序
                $orderString = "CONVERT( Ins." . $where['order_field'] . " USING gbk) " . $where['order_type'];
            } else {   // 基础字段排序
                if ($where['order_field'] == 'check_situation') { //排序校验情况
                    $this->fixCheckSituation();
                }
                $orderString = "CONVERT( Ins." . $where['order_field'] . " USING gbk) " . $where['order_type'];
            }
        }
        $query->orderBy($orderString);
        $result = $query->asArray()->all();

        // 根据创建者ID获取用户姓名
        $user_ids = array_unique(array_column($result, 'create_by'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');

        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');

        if ($forExport) { // add by hkk 2020/5/6
            $groupList = (new CenterInterface())->getGroupsListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所属鹰群
            $groupsInfo = array_column($groupList, NULL, 'id');

            $departmentsInfo = [];
            $departmentList = (new CenterInterface())->getDepartmentListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所属鹰群
            if (isset($departmentList['list']) && !empty($departmentList['list'])) {
                $departmentsInfo = array_column($departmentList['list'], NULL, 'id');
            }
        }

        foreach ($result as $key => $value) {
            // 用户名
            if (!empty($userList[$value['create_by']])) {
                $result[$key]['create_name'] = CommentServer::displayUserName($userList[$value['create_by']]);
            } else {
                $result[$key]['create_name'] = '';
            }

            // 责任人 维护人 维修人
            if (!empty($value['responsible_person'])) {
                $result[$key]['responsible_person_string'] = $this->getUsersNameStrings($userListInfo, $value['responsible_person']);
            } else {
                $result[$key]['responsible_person_string'] = '';
            }
            if (!empty($value['in_charge_person'])) {
                $result[$key]['in_charge_person_string'] = $this->getUsersNameStrings($userListInfo, $value['in_charge_person']);
            } else {
                $result[$key]['in_charge_person_string'] = '';
            }
            if (!empty($value['maintenance_person'])) {
                $result[$key]['maintenance_person_string'] = $this->getUsersNameStrings($userListInfo, $value['maintenance_person']);
            } else {
                $result[$key]['maintenance_person_string'] = '';
            }

            // add by hkk 2020/5/6
            if ($forExport) {
                // 所属鹰群名称
                $groupNames = [];
                $defaultGroupIds = array_filter(explode(',', $value['groupIds'])); // add by hkk 2020/4/23
                foreach ($defaultGroupIds as $groupId) {
                    $groupNames[] = !empty($groupsInfo[$groupId]) ? $groupsInfo[$groupId]['group_name'] : '';
                }
                $result[$key]['group_name_string'] = join(';', $groupNames);

                $departmentNames = [];
                $defaultDepartmentIds = array_filter(explode(',', $value['departmentIds'])); // add by hkk 2020/4/23
                foreach ($defaultDepartmentIds as $departmentId) {
                    $departmentNames[] = !empty($departmentsInfo[$departmentId]) ? $departmentsInfo[$departmentId]['department_name'] : '';
                }
                $result[$key]['department_name_string'] = join(';', $departmentNames);

                // 校验状态
                if ($value['check_situation'] == "0") {
                    $result[$key]['check_situation_string'] = \Yii::t('base', 'noNeedCheck');
                } else if ($value['check_situation'] == "1") { // 加上有效期判断 expiry_date
                    if (in_array($value['id'], $checkedIds)) {
                        $result[$key]['check_situation_string'] = \Yii::t('base', 'checked');
                    } elseif (in_array($value['id'], $unCheckedIds)) {
                        $result[$key]['check_situation_string'] = \Yii::t('base', 'unchecked');
                    } else {
                        $result[$key]['check_situation_string'] = '';
                    }
                } else {
                    $result[$key]['check_situation_string'] = \Yii::t('base', 'unchecked');
                }

                // 仪器情况
                if ($value['status'] == "1") {
                    $result[$key]['status_string'] = \Yii::t('base', 'normal');
                } else if ($value['status'] == "0") {
                    $result[$key]['status_string'] = \Yii::t('base', 'already_deleted');
                } else if ($value['status'] == "2") {
                    $result[$key]['status_string'] = \Yii::t('base', 'suspend_use');
                } else if ($value['status'] == "3") {
                    $result[$key]['status_string'] = \Yii::t('base', 'repairing');
                } else if ($value['status'] == "4") {
                    $result[$key]['status_string'] = \Yii::t('base', 'scrap');
                }

                // 仪器对接类型
                if (isset($result[$key]['data_type'])) {
                    if ($result[$key]['data_type'] == "0") {
                        $result[$key]['data_type_string'] = \Yii::t('base', 'none');
                    } else if ($result[$key]['data_type'] == "1") {
                        if ($result[$key]['numerical_instrument_type'] != 0) {
                            $numericalInstrumentTypeMap = [
                                1 => 'numerical_instrument1',
                                2 => 'numerical_instrument2',
                                3 => 'numerical_instrument3',
                                4 => 'numerical_instrument4',
                            ];
                            $result[$key]['data_type_string'] = \Yii::t('base', $numericalInstrumentTypeMap[$result[$key]['numerical_instrument_type']]);
                        } else {
                            $result[$key]['data_type_string'] = \Yii::t('base', 'numerical_instrument1');
                        }
                    } else if ($result[$key]['data_type'] == "2") {
                        $result[$key]['data_type_string'] = \Yii::t('base', 'file');
                    }
                } else {
                    $result[$key]['data_type'] = 0;
                    $result[$key]['data_type_string'] = \Yii::t('base', 'none');
                }
            }

        }

        $data['checkedIds'] = $checkedIds;
        $data['unCheckedIds'] = $unCheckedIds;
        $data['totalCount'] = $totalCount;
        $data['instruments_list'] = $result;

        // add by hkk 2021/4/27 去仪器自定义字段表查询自定义字段
        $defineRecord = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
        $data['defineFields'] = $defineRecord ? $defineRecord : [];
        //  add by hkk 2021/4/28  获取仪器的分类列表
        $data['type_list'] = (new CollaborationServer())->getTypeList(\Yii::$app->view->params['curr_company_id'], 52);

        return $data;
    }


    /**
     * Notes: 获取当前用户可见的所有仪器，仅获取仪器名称和ID，并按名称升序排序A-Z
     */
    public function listAllInstruments($where)
    {
        //
        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->select[] = 'Ins.id';
        $query->select[] = 'Ins.name AS name'; // 增加中文排序
        $data['dataType'] = '';//判断是我的仪器库还是仪器库管理
        if ($where['viewAuth'] == '2') { // add by hkk 2022/6/27 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }

            $FindStr .= "FIND_IN_SET('all', Ins.groupIds)"; // add by hkk 2022/8/8  加不限仪器的判断
            // $FindStr = substr($FindStr, 0, -4);
            $query->andWhere(new Expression($FindStr));
            $data['dataType'] = 'my-instruments';
        }
        // $query->limit(1000);
        $orderString = 'name asc';
        $query->orderBy($orderString);
        $result = $query->asArray()->all();
        $data['instruments'] = $result;
        return $data;
    }

    /**
     * Notes: 新的仪器痕迹列表，带搜索筛选
     * Author: hkk
     * Date: 2020/4/30 13:19
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listInstrumentTraceView($where, $limit, $page, $forExport = false)
    {

        $query = InstrumentsActionRecordModel::find()->from(InstrumentsActionRecordModel::tableName() . ' IA')
            ->where(['status' => 1]);
        $query->select[] = 'IA.action';
        $query->select[] = 'IA.create_time';
        $query->select[] = 'IA.create_by';
        $query->select[] = 'IA.action_details';
        $query->select[] = 'IA.remark';
        $query->select[] = 'IA.instrument_id';


        if (!empty($where['action'])) { // 类别筛选
            $query->andWhere(['IA.action' => $where['action']]);
        }

        if (!empty($where['instrument_id'])) {
            $query->andWhere(['IA.instrument_id' => $where['instrument_id']]);
        }

        if (!empty($where['action_details'])) { // 详情搜索
            $query->andWhere(['like', 'IA.action_details', $where['action_details']]);
        }

        if (!empty($where['create_by'])) {  // 人员筛选
            $query->andWhere(['IA.create_by' => $where['create_by']]);
        }

        if (!empty($where['start_time'])) { // 操作起始时间
            $query->andWhere(['>=', 'IA.create_time', $where['start_time']]);
        }

        if (!empty($where['end_time'])) { // 操作结束时间
            $query->andWhere(['<=', 'IA.create_time', $where['end_time']]);
        }


        $totalCount = $query->count();
        if (!$forExport) {
            $query->offset(($page - 1) * $limit)->limit($limit); // 分页显示
        }
        $query->orderBy('IA.create_time desc');
        $result = $query->asArray()->all();


        //根据创建者ID获取用户信名
        $user_ids = array_unique(array_column($result, 'create_by'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        foreach ($result as $key => $value) {
            //用户名
            if (!empty($userList[$value['create_by']])) {
                $result[$key]['create_name'] = CommentServer::displayUserName($userList[$value['create_by']]);
            } else {
                $result[$key]['create_name'] = '';
            }

            if ($forExport) {
                switch ($value['action']) {
                    case 1:
                        $result[$key]['action_string'] = \Yii::t('base', 'add_instrument');
                        break;
                    case 2:
                        $result[$key]['action_string'] = \Yii::t('base', 'edit_instrument');
                        break;
                    case 3:
                        $result[$key]['action_string'] = \Yii::t('base', 'repair_instrument');
                        break;
                    case 4:
                        $result[$key]['action_string'] = \Yii::t('base', 'delete_instrument');
                        break;
                    case 5:
                        $result[$key]['action_string'] = \Yii::t('base', 'book_instrument1');
                        break;
                    case 6:
                        $result[$key]['action_string'] = \Yii::t('base', 'delete_book');
                        break;
                    case 7:
                        $result[$key]['action_string'] = \Yii::t('base', 'modify_book1');
                        break;
                    case 8:
                        $result[$key]['action_string'] = \Yii::t('base', 'delete_file');
                        break;
                    case 9:
                        $result[$key]['action_string'] = \Yii::t('base', 'upload_file');
                        break;
                    case 10:
                        $result[$key]['action_string'] = \Yii::t('base', 'edit_operate_record');
                        break;
                    case 11:
                        $result[$key]['action_string'] = \Yii::t('base', 'edit_repair_record');
                        break;
                    case 12:
                        $result[$key]['action_string'] = \Yii::t('base', 'edit_check_record');
                        break;
                    case 13:
                        $result[$key]['action_string'] = \Yii::t('base', 'upload_instrument_picture');
                        break;
                    case 14:
                        $result[$key]['action_string'] = \Yii::t('base', 'delete_instrument_picture');
                        break;
                    case 15:
                        $result[$key]['action_string'] = \Yii::t('base', 'instrument_approval');
                        break;
                    case 16:
                        $result[$key]['action_string'] = \Yii::t('base', 'instrument_set');
                        break;
                    case 17:
                        $result[$key]['action_string'] = \Yii::t('base', 'instrument_remind');
                        break;
                    case 18:
                        $result[$key]['action_string'] = \Yii::t('base', 'instrument_adjust_column');
                        break;
                    default :
                        $result[$key]['action_string'] = \Yii::t('base', 'none');
                }

                $result[$key]['action_details'] = str_replace("</br>", "; ", $value['action_details']);
                $result[$key]['action_details'] = preg_replace("/<a[^>]*>(.*?)<\/a>/is", "$1", $result[$key]['action_details']);
            }
        }

        $data['totalCount'] = $totalCount;
        $data['historyList'] = $result;
        return $data;
    }

    public function getData($relayId, $type = 1)
    {


        $fieldKey = DefineTableKeyModel::find()->where([
            'parent_id' => $relayId,
            'type' => $type,
            'status' => 1
        ])->asArray()->one();

        $fieldValue = DefineTableValueModel::find()->where([
            'define_key_id' => $fieldKey['id']
        ])->asArray()->all();

        if (empty($fieldValue)) {
            $fieldValue = [
                [
                    'data1' => '',
                    'data2' => '',
                    'data3' => '',
                    'data4' => '',
                    'data5' => '',
                    'data6' => '',
                    'data7' => '',
                    'data8' => '',
                    'data9' => '',
                    'data10' => '',
                    'data11' => '',
                    'data12' => '',
                    'data13' => '',
                    'data14' => '',
                    'data15' => '',
                    'data16' => '',
                    'data17' => '',
                    'data18' => '',
                    'data19' => '',
                    'data20' => '',
                    'upload_file_data' => '',
                ]
            ];
        }

        return $this->success([
            'field_key' => $fieldKey,
            'field_value' => $fieldValue
        ]);
    }

    /**
     * Notes: 获取仪器历史操作信息
     * Author: hkk
     * Date: 2019/10/31 17:33
     * @param $instrumentId
     * @return mixed
     */
    public function listInstrumentHistory($instrumentId)
    {

        $result = InstrumentsActionRecordModel::find()
            ->select('action,create_time,create_by,action_details,remark')
            ->orderBy('create_time desc')
            ->where(['instrument_id' => $instrumentId, 'status' => 1])
            ->asArray()->all();

        //根据创建者ID获取用户名
        $user_ids = array_unique(array_column($result, 'create_by'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        foreach ($result as $key => $value) {
            //用户名
            if (!empty($userList[$value['create_by']])) {
                $result[$key]['create_name'] = CommentServer::displayUserName($userList[$value['create_by']]);
            } else {
                $result[$key]['create_name'] = '';
            }
        }

        return $result;
    }

    /**
     * Notes:获取仪器预约历史信息
     * Author: hkk
     * Date: 2019/11/4 11:14
     * @param $instrumentId
     * @return array|\yii\db\ActiveRecord[]
     */
    public function listInstrumentBook($instrumentId)
    {

        $result = InstrumentsBookModel::find()
            ->select('id,create_time,create_by,start_time,end_time,related_experiment,remark,status,instrument_id')
            ->orderBy('end_time desc')
            ->where(['instrument_id' => $instrumentId, 'status' => 1])
            ->andWhere(['>', 'end_time', date("Y-m-d H:i:s", strtotime("-10 day"))]) // 10天前的不显示
            ->asArray()->all();


        //根据创建者ID获取用户名
        $user_ids = array_unique(array_column($result, 'create_by'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        foreach ($result as $key => $value) {
            //用户名
            if (!empty($userList[$value['create_by']])) {
                $result[$key]['create_name'] = CommentServer::displayUserName($userList[$value['create_by']]);
            } else {
                $result[$key]['create_name'] = '';
            }
        }

        return $result;
    }

    /**
     * Notes: 获取相关仪器相关预约，且时间需大于传入的时间
     * Author: zsm
     * Date: 2025/5/16 14:40
     * @param $instrument 仪器id
     * @param $day 查询日期
     * @return mixed
     */
    public function listBookById($instrumentId, $day)
    {
        $query = InstrumentsBookModel::find()
            ->from(InstrumentsBookModel::tableName() . ' AS BOOK')
            ->leftJoin(InstrumentsModel::tableName() . ' AS Ins', 'BOOK.instrument_id = Ins.id')
            ->select([
                'BOOK.instrument_id',
                'BOOK.id',
                'BOOK.start_time',
                'BOOK.end_time',
                'BOOK.related_experiment',
                'BOOK.create_time',
                'BOOK.remark',
                'BOOK.create_by',
                'Ins.name AS instrument_name',  // 规范命名别名
                'Ins.batch_number AS instrument_batch_number',  // 规范命名别名
                'Ins.specification AS instrument_specification',  // 规格
                'Ins.model AS instrument_model'  // 货号/型号
            ]);
        if (!empty($instrumentId)) {
            $query->andWhere(['BOOK.instrument_id' => $instrumentId]);
        }
        if (!empty($day)) {
            // 将 day 转换为标准的 'YYYY-MM-DD' 格式
            $day = strtotime(preg_replace('/\s*\(.*\)$/', '', $day));  // 去掉括号部分，保留日期时间
            $startOfDay = date('Y-m-d', $day) . ' 00:00:00';  // 获取当天的开始时间
            $endOfDay = date('Y-m-d', $day) . ' 23:59:59';    // 获取当天的结束时间

            // 使用 `orWhere` 使查询符合任一条件
            $query->andWhere([
                'or',
                ['<=', 'BOOK.start_time', $endOfDay],
                ['>=', 'BOOK.end_time', $startOfDay]
            ]);
        }
        $query->orderBy('BOOK.create_time desc');
        $result = $query->asArray()->all();


        $totalCount = $query->count();
        $data['totalCount'] = $totalCount;
        $data['book_list'] = $result;
        return $data;
    }
    /**
     * Notes: 获取我的预约页面
     * Author: hkk
     * Date: 2019/11/4 16:27
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listMyBookView($where, $limit, $page)
    {

        $query = InstrumentsBookModel::find()->from(InstrumentsBookModel::tableName() . ' BOOK');
        $query->leftJoin(InstrumentsModel::tableName() . ' AS Ins', 'BOOK.instrument_id=Ins.id');

        $query->select[] = 'BOOK.instrument_id';
        $query->select[] = 'BOOK.id';
        $query->select[] = 'BOOK.start_time';
        $query->select[] = 'BOOK.end_time';
        $query->select[] = 'BOOK.related_experiment';
        $query->select[] = 'BOOK.create_time';
        $query->select[] = 'BOOK.remark';
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ins.batch_number';
        $query->select[] = 'Ins.specification'; // 规格
        $query->select[] = 'Ins.model';        // 货号/型号

        if (!empty($where['create_by'])) {
            $query->andWhere(['BOOK.create_by' => $where['create_by']]);
        }

        if (!empty($where['name'])) {
            $query->andWhere(['like', 'Ins.name', $where['name']]);
        }

        $query->andWhere(['BOOK.status' => 1]);

        $totalCount = $query->count();
        $query->offset(($page - 1) * $limit)->limit($limit);
        $query->orderBy('BOOK.create_time desc');
        $result = $query->asArray()->all();

        $data['totalCount'] = $totalCount;
        $data['my_book_list'] = $result;
        return $data;
    }

    /**
     * Notes: 保存提醒设置
     * Author: hkk
     * Date: 2020/4/24 11:23
     * @param $instrumentData
     * @return array
     */
    public function saveReminderSetting($instrumentData)
    {

        $saveSettingData = [];
        $instrumentModel = InstrumentsModel::findOne([
            'id' => $instrumentData['id'],
            'status' => 1
        ]);
        $userIds = [];
        foreach ($instrumentData['remind_setting'] as $setting) {
            if ($setting['reminder_type'] === 'cancel') { // 取消设置
                continue;
            }
            $saveSettingData[] = $setting;
            //拼userIds数组
            $userIds = array_merge($userIds, $setting['reminder_users']);
            if (!empty($setting['addToSchedule'])) {
                foreach ($setting['reminder_users'] as $userId) {
                    foreach ($setting['time_points'] as $date) {
                        $params['user_id'] = $userId;
                        $params['title'] = \Yii::t('msg', 'instrument_reminder_title') . '[' . $instrumentModel['name'] . ']: ' . $setting['reminder_content'];
                        $params['start'] = $date;
                        $params['url'] = ELN_URL;
                        (new CenterInterface())->sendSchedule($params);
                    }
                }
            }
        }
        $userLists = (new CenterInterface())->userDetailsByUserIds(array_unique($userIds));
        $userLists = ArrayHelper::index($userLists, 'user_id');


        $settingModel = InstrumentReminderSettingModel::findOne([
            'instrument_id' => $instrumentData['id'],
            'status' => 1
        ]);
        if ($settingModel == null && empty($saveSettingData)) { //如果没有设置并且更新的数据为空，没必要存数据库
            return $this->success([]);
        }

        if ($settingModel && empty(json_decode($settingModel['setting'])) && empty($saveSettingData)) { //如果有记录但记录是空且更新的数据为空，没必要存数据库
            return $this->success([]);
        }
        if (!$settingModel) {
            $settingModel = new InstrumentReminderSettingModel();
            $settingModel->setAttributes([
                'instrument_id' => $instrumentData['id']
            ]);
        }
        $settingModel['setting'] = json_encode($saveSettingData);
        $settingModel->save();

        $msgContent = '';
        foreach ($saveSettingData as $index => $setting) {
            $remindUserList = [];
            foreach ($setting['reminder_users'] as $userId) {
                $remindUserList[] = CommentServer::displayUserName($userLists[$userId]);
            }

            if ($setting['reminder_type'] == 'period') { //按周期
                $msgContentParams = [
                    $index + 1,
                    $setting['reminder_period'],
                    $setting['reminder_start_time'],
                    implode(',', $remindUserList),
                    $setting['reminder_content'],

                ];
                $msgContent .= \Yii::t('base', 'ins_remind_setting_content_1', $msgContentParams);
            }

            if ($setting['reminder_type'] == 'timePoints') { //按日期
                $msgContentParams = [
                    $index + 1,
                    implode(',', $setting['time_points']),
                    implode(',', $remindUserList),
                    $setting['reminder_content'],
                ];
                $msgContent .= \Yii::t('base', 'ins_remind_setting_content_2', $msgContentParams);
            }
        }
        if (empty($msgContent)) {
            $msgContent = \Yii::t('base', 'cancel_reminder_settings');
        }
        $this->addInstrumentHistory($instrumentData['id'], self::Action_REMINDER, ['action_details' => $msgContent]);


        return $this->success([]);

    }

    /**
     * @Notes: 保存时间和复核设置并添加日志
     * @param $instrumentData
     * @return bool
     * <AUTHOR>
     * @DateTime: 2023/12/6 15:06
     */
    public function saveRecordSetting($instrumentData)
    {
        $InstrumentInfo = InstrumentsModel::find()->where(['id' => $instrumentData['id']])->asArray()->one();
        $oldRecordSettingArr = json_decode($InstrumentInfo['record_setting'], true);
        $newRecordSettingArr = $instrumentData['setting'];
        //初始化一个新旧比较数组
        $recordSettingArr = [
            'operate_check_users' => [],
            'repair_check_users' => [],
            'check_check_users' => [],
            'scrap_must_check_users' => [],
            'suspend_use_must_check_users' => [],
            'delete_must_check_users' => [],
            'apply_repair_must_check_users' => [],
            'operate_repeat' => [],
            'repair_repeat' => [],
            'check_repeat' => [],
            'time_operate' => [],
        ];
        $diffRecordSettingArr = [];
        $userIds = [];
        //填充值
        foreach ($recordSettingArr as $key => $value) {

            if ($key == 'operate_repeat' || $key == 'repair_repeat' || $key == 'check_repeat' || $key == 'time_operate') { //字符串类型 ture false 或不存在
                $recordSettingArr[$key][0] = isset($oldRecordSettingArr[$key]) ? $oldRecordSettingArr[$key] : 'false'; //不存在初始化禁用
                $recordSettingArr[$key][1] = isset($newRecordSettingArr[$key]) ? $newRecordSettingArr[$key] : 'false';
                if ($recordSettingArr[$key][0] != $recordSettingArr[$key][1]) {
                    $diffRecordSettingArr[$key] = $recordSettingArr[$key];
                }
            } else { //数组类型
                $recordSettingArr[$key][0] = isset($oldRecordSettingArr[$key]) ? $oldRecordSettingArr[$key] : [];
                $recordSettingArr[$key][1] = isset($newRecordSettingArr[$key]) ? $newRecordSettingArr[$key] : [];
                if (!empty(array_diff($recordSettingArr[$key][0], $recordSettingArr[$key][1])) || !empty(array_diff($recordSettingArr[$key][1], $recordSettingArr[$key][0]))) {
                    $diffRecordSettingArr[$key] = $recordSettingArr[$key];
                    $userIds = array_merge($userIds, $recordSettingArr[$key][0], $recordSettingArr[$key][1]);
                }
            }

        }
        $userLists = (new CenterInterface())->userDetailsByUserIds(array_unique($userIds));
        $userLists = ArrayHelper::index($userLists, 'user_id');

        //拼接保存的信息
        if (empty($diffRecordSettingArr)) {
            return true;
        }
        //初始化操作详情
        $actionDetails = '';
        $signUser = \Yii::t('sign', 'sign_user');
        $auditorUser = \Yii::t('temp', 'auditor');
        $disable = \Yii::t('base', 'disable');
        $enable = \Yii::t('base', 'enable');
        foreach ($diffRecordSettingArr as $key => $diffRecordSetting) {
            switch ($key) {
                case 'operate_check_users' :
                    $setting = \Yii::t('base', 'operate_record_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $signUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'repair_check_users' :
                    $setting = \Yii::t('base', 'repair_record_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $signUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'check_check_users' :
                    $setting = \Yii::t('base', 'check_record_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $signUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'scrap_must_check_users' :
                    $setting = \Yii::t('base', 'scrap_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $auditorUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'suspend_use_must_check_users' :
                    $setting = \Yii::t('base', 'suspend_use_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $auditorUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'delete_must_check_users' :
                    $setting = \Yii::t('base', 'delete_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $auditorUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'apply_repair_must_check_users' :
                    $setting = \Yii::t('base', 'apply_repair_must_check');
                    $detailsInfo = $this->initActionDetailsInfo($diffRecordSetting[0], $diffRecordSetting[1], $userLists);
                    $actionDetails .= $setting . ': ' . $detailsInfo['action'] . ' ' . $auditorUser . ': ' . $detailsInfo['user'] . '<br>';
                    break;
                case 'operate_repeat' :
                    $setting = \Yii::t('base', 'operate_time_repeat_not_allow');
                    $actionDetails .= $setting . ': ' . ($diffRecordSetting[0] == 'false' ? $disable : $enable) . '=>' . ($diffRecordSetting[1] == 'false' ? $disable : $enable) . '<br>';
                    break;
                case 'repair_repeat' :
                    $setting = \Yii::t('base', 'repair_time_repeat_not_allow');
                    $actionDetails .= $setting . ': ' . ($diffRecordSetting[0] == 'false' ? $disable : $enable) . '=>' . ($diffRecordSetting[1] == 'false' ? $disable : $enable) . '<br>';
                    break;
                case 'check_repeat' :
                    $setting = \Yii::t('base', 'check_time_repeat_not_allow');
                    $actionDetails .= $setting . ': ' . ($diffRecordSetting[0] == 'false' ? $disable : $enable) . '=>' . ($diffRecordSetting[1] == 'false' ? $disable : $enable) . '<br>';
                    break;
                case 'time_operate' :
                    $setting = \Yii::t('base', 'time_only_after_operate');
                    $actionDetails .= $setting . ': ' . ($diffRecordSetting[0] == 'false' ? $disable : $enable) . '=>' . ($diffRecordSetting[1] == 'false' ? $disable : $enable) . '<br>';
                    break;
                default:
                    break;
            }

        }
        if ($actionDetails != '') {
            $this->addInstrumentHistory($instrumentData['id'], self::Action_SETTING, ['action_details' => $actionDetails]);
        }
        InstrumentsModel::updateAll(['record_setting' => json_encode($instrumentData['setting'])], [
            'id' => $instrumentData['id']
        ]);
        $this->updateInstrumentsTimeRecord($instrumentData['id']);
        return true;

    }

    /**
     * @Notes: 返回 关闭=>开启  user1=>user2类似的信息
     * @param $oldUserListIds
     * @param $newUserListIds
     * @param $userListIds
     * @return array
     * <AUTHOR>
     * @DateTime: 2023/12/6 17:18
     */
    public function initActionDetailsInfo($oldUserListIds, $newUserListIds, $userListIds)
    {
        $actionDetailsInfo = [];
        $oldUser = $newUser = [];
        if (empty($oldUserListIds)) {
            $oldSet = \Yii::t('base', 'disable');
        } else {
            $oldSet = \Yii::t('base', 'enable');
        }
        if (empty($newUserListIds)) {
            $newSet = \Yii::t('base', 'disable');
        } else {
            $newSet = \Yii::t('base', 'enable');
        }
        $actionDetailsInfo['action'] = $oldSet . '=>' . $newSet;
        foreach ($oldUserListIds as $oldUserListId) {
            $oldUser[] = CommentServer::displayUserName($userListIds[$oldUserListId]);
        }
        foreach ($newUserListIds as $newUserListId) {
            $newUser[] = CommentServer::displayUserName($userListIds[$newUserListId]);
        }
        $actionDetailsInfo['user'] = implode(',', $oldUser) . '=>' . implode(',', $newUser);
        return $actionDetailsInfo;
    }

    /**
     * Notes:根据仪器id查询仪器详情
     * Author: hkk
     * Date: 2020/4/24 15:20
     * @param $instrumentId
     * @return array
     */
    public function getInsInfoById($instrumentId)
    {
        $result = InstrumentsModel::findOne([
            'id' => $instrumentId,
        ])->toArray();
        return $this->success($result);
    }

    /**
     * Notes:根据预约id查询预约详情
     * Author: hkk
     * Date: 2020/4/24 15:20
     * @param $bookId
     * @return array
     */
    public function getBookInfoById($bookId)
    {
        $result = InstrumentsBookModel::findOne([
            'id' => $bookId,
        ])->toArray();
        return $this->success($result);
    }

    /**
     * Notes: 获取提醒设置
     * Author: hkk
     * Date: 2020/4/24 11:23
     * @param $instrumentId
     * @param $userId
     * @return array
     */
    public function getReminderSetting($instrumentId)
    {
        $result = InstrumentReminderSettingModel::findOne([
            'instrument_id' => $instrumentId,
            'status' => 1
        ]);
        return $this->success($result);
    }

    /**
     * Notes:发送仪器提醒，包括平台消息和邮件
     * Author: hkk
     * Date: 2020/4/24 14:58
     * @param $instrumentId
     * @param $toUserIds
     * @param $content
     */
    public function sendInstrumentReminder($instrumentId, $setting)
    {

        // 获取仪器名称
        $instrumentInfo = $this->getInsInfoById($instrumentId);
        $instrumentName = $instrumentInfo['data']['name'];
        $instrumentNameLink = '<a href="' . ELN_URL . '?route=my_instruments&name=' . $instrumentName . '' . '" target="_blank">' . $instrumentName . '</a>';


        // 获取提醒人员信息
        $toUserIds = $setting['reminder_users'];
        $userInfoArr = (new CenterInterface())->userDetailsByUserIds($toUserIds);
        $userInfoArr = ArrayHelper::index($userInfoArr, 'id');

        // 替换换行符为</br>
        $content = $setting['reminder_content'];
        $content = preg_replace('/\\n/', '</br>', $content);

        // 发送平台消息
        $elnMsgSer = new ElnMessageServer();
        foreach ($toUserIds as $toUserId) {
            // 获取用户语言
            $userLang = CommonServer::getUserLang($userInfoArr[$toUserId]);
            $msgTitle = \Yii::t('msg', 'instrument_reminder_title', [], $userLang);
            if (@getVar($setting['reminder_period'], '')) {
                $msgContent = \Yii::t(
                    'msg',
                    'instrument_reminder_period_msg_content',
                    [
                        $instrumentNameLink,
                        $instrumentInfo['data']['batch_number'],
                        $setting['reminder_period'],
                        $content
                    ],
                    $userLang
                );
                $elnMsgSer->addMessage(0, $toUserId, $msgTitle, $msgContent, 0, false, 0, '');
            } elseif (@getVar($setting['time_points'], '')) {
                $msgContent = \Yii::t(
                    'msg',
                    'instrument_reminder_time_point_msg_content',
                    [
                        $instrumentNameLink,
                        $instrumentInfo['data']['batch_number'],
                        $setting['time_points'],
                        $content
                    ],
                    $userLang
                );
                $elnMsgSer->addMessage(0, $toUserId, $msgTitle, $msgContent, 0, false, 0, '');
            }
        }

        // 发送邮件
        $userLang = CommonServer::getUserLang($userInfoArr[$toUserIds[0]]); // 获取第一个收件人的用户语言
        $emailTitle = \Yii::t('msg', 'instrument_reminder_title', [], $userLang);
        if (@getVar($setting['reminder_period'], '')) {
            //按周期提醒
            $emailContent = \Yii::t(
                'msg',
                'instrument_reminder_period_msg_content',
                [
                    $instrumentNameLink,
                    $instrumentInfo['data']['batch_number'],
                    $setting['reminder_period'],
                    $content
                ],
                $userLang
            );
            $elnMsgSer->addMail(0, $toUserIds, [], $emailTitle, $emailContent, 1, $userLang);
        } elseif (@getVar($setting['time_points'], '')) {
            //按时间点提醒
            $emailContent = \Yii::t(
                'msg',
                'instrument_reminder_time_point_msg_content',
                [
                    $instrumentNameLink,
                    $instrumentInfo['data']['batch_number'],
                    $setting['time_points'],
                    $content
                ],
                $userLang
            );
            $elnMsgSer->addMail(0, $toUserIds, [], $emailTitle, $emailContent, 1, $userLang);
        }
    }


    /**
     * Notes:发送仪器校验到期提醒
     * Author: hkk
     * Date: 2022/6/1 14:15
     */
    public function sendInstrumentExpiryReminder()
    {

        // 查询所有设置了校验到期提醒的校验仪器
        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->leftJoin(InstrumentCheckRecordExtendFieldModel::tableName() . ' AS Key2', 'Key2.instrument_id=Ins.id'); // 校验 // add by hkk 2022/5/26
        $subQuery2 = InstrumentCheckRecordModel::find()->select('instrument_id,start_check_time,end_check_time,start_expiry_time,end_expiry_time,remind_expiry_time')->orderBy('id desc');
        $query->leftJoin(['Data2' => $subQuery2], 'Data2.instrument_id=Ins.id');
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ins.batch_number';
        $query->select[] = 'Ins.responsible_person';   // 负责人
        $query->select[] = 'Ins.in_charge_person';     // 维护人
        $query->select[] = 'Ins.maintenance_person';   // 维修人
        $query->select[] = 'Data2.start_expiry_time as valid_start_time'; // 校验开始有效期
        $query->select[] = 'Data2.end_expiry_time as valid_end_time'; // 校验结束有效期
        $query->select[] = 'Data2.remind_expiry_time as reminder_days'; // 到期提醒
        $query->andWhere(['Ins.status' => [1, 2, 3, 4]]); // 默认不显示删除的仪器
        $query->andWhere(['<>', 'Data2.remind_expiry_time', '']); // 不等于空
        $query->andWhere(['not', ['Data2.remind_expiry_time' => null]]);
        $query->andWhere('STR_TO_DATE(Data2.end_expiry_time, "%Y-%m-%d %H:%i:%s") >= NOW()'); // bug#12862, 设置已经过期的校验记录不再发送提醒
        $query->groupBy('Ins.id');
        $query->orderBy('Ins.create_time desc');
        $expireResult = $query->asArray()->all();

        $currentDate = date('Y-m-d');
        foreach ($expireResult as $instrumentItem) {

            $expiryTime = new \DateTime(date('Y-m-d', strtotime($instrumentItem['valid_end_time'])));
            $currentTime = new \DateTime($currentDate);
            // \DateInterval::$days只能获取到两个日期之间的绝对天数差值, 不考虑日期先后顺序
            $intervalDays = $expiryTime->diff($currentTime)->days;
            $intervalDays = $intervalDays <= 0 ? 0 : $intervalDays;

            if ($intervalDays <= intval($instrumentItem['reminder_days'])) {

                $toUserIdString = '';
                if (!empty($instrumentItem['responsible_person'])) {
                    $toUserIdString .= $instrumentItem['responsible_person'] . ',';
                }
                if (!empty($instrumentItem['in_charge_person'])) {
                    $toUserIdString .= $instrumentItem['in_charge_person'] . ',';
                }
                if (!empty($instrumentItem['maintenance_person'])) {
                    $toUserIdString .= $instrumentItem['maintenance_person'] . ',';
                }

                $toUserIds = array_filter(array_unique(explode(",", $toUserIdString)));

                if (!empty($toUserIdString) && count($toUserIds) > 0) {
                    // 获取提醒人员信息
                    $userInfoArr = (new CenterInterface())->userDetailsByUserIds($toUserIds);
                    $userInfoArr = ArrayHelper::index($userInfoArr, 'id');

                    // 发送平台消息
                    $elnMsgSer = new ElnMessageServer();
                    foreach ($toUserIds as $toUserId) {
                        // 获取用户语言
                        $userLang = CommonServer::getUserLang($userInfoArr[$toUserId]);
                        $msgTitle = \Yii::t('msg', 'Instrument_expiry_email_title', [], $userLang);
                        $msgContent = \Yii::t('msg', 'Instrument_expiry_email_content', [$instrumentItem['name'], $instrumentItem['batch_number'], $intervalDays], $userLang);
                        $elnMsgSer->addMessage(0, $toUserId, $msgTitle, $msgContent, 0, false, 0, '');
                    }

                    // 发送邮件
                    $userLang = CommonServer::getUserLang($userInfoArr[$toUserIds[0]]); // 获取第一个收件人的用户语言
                    $emailTitle = \Yii::t('msg', 'Instrument_expiry_email_title', [], $userLang);
                    $emailContent = \Yii::t('msg', 'Instrument_expiry_email_content', [$instrumentItem['name'], $instrumentItem['batch_number'], $intervalDays], $userLang);

                    $elnMsgSer->addMail(0, $toUserIds, [], $emailTitle, $emailContent, 1, $userLang);
                }
            }
        }
    }

    /**
     * Notes:保存校验信息,只会新增，不会覆盖
     * Author: hkk
     * Date: 2020/4/28 9:06
     * @param $instrumentData
     * @return array
     * @throws Exception
     */
    public function saveCheckInfo($instrumentData)
    {

        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $ins = new InstrumentCheckModel();
        $ins->setAttributes($instrumentData);
        if (!$ins->save()) {
            $transaction->rollBack();
            return $this->fail('fail to add instrument');
        }
        $transaction->commit();

        // 点击新增校验确定  更改仪器库校验状态为1代表了进行校验操作，同时添加仪器库校验的有效期
        InstrumentsModel::updateAll([
            'check_situation' => 1,
            'start_expiry_time' => $instrumentData['start_expiry_time'],
            'end_expiry_time' => $instrumentData['end_expiry_time'],
        ], [
            'id' => $instrumentData['instrument_id']
        ]);

        return $this->success([]);
    }

    /**
     * Notes:获取仪器校验表信息
     * Author: hkk
     * Date: 2020/4/27 18:21
     * @param $instrumentId
     * @return array|\yii\db\ActiveRecord[]
     */
    public function listInstrumentCheck($instrumentId)
    {

        $result = InstrumentCheckModel::find()
            //->select('id,create_time,create_by,status,instrument_id')
            ->orderBy('create_time desc')
            ->where(['instrument_id' => $instrumentId, 'status' => 1])
            ->asArray()->all();

        return $result;
    }

    /**
     * Notes: 删除仪器文件
     * Author: hkk
     * Date: 2020/4/29 9:34
     * @param $postData
     * @return array
     * @throws Exception
     */
    public function deleteInstrumentFile($data)
    {
        // 更新数据库文件栏
        InstrumentsModel::updateAll(
            ['files' => $data['currentFiles']],
            ['id' => $data['instrumentId']]
        );

        // 构造链接
        $deleteFileString = '<a target="_blank" href=?r=download/file&path=' . $data['deleteFile']['dep_path'] . '&name=' . $data['deleteFile']['save_name'] . '&file_name=' . rawurlencode($data['deleteFile']['real_name']) . '>' . $data['deleteFile']['real_name'] . ' </a>';

        $action_details = \Yii::t('base', 'delete_reason') . ":" . $data['deleteReason'] . "</br>";
        $action_details .= (\Yii::t('base', 'delete_file') . ":" . $deleteFileString);


        $hisData = [
            'action_details' => $action_details,
        ];


        $this->addInstrumentHistory($data['instrumentId'], self::ACTION_DELETE_FILE, $hisData);

        return $this->success([]);
    }

    /**
     * Notes: 删除仪器图片
     * Author: zwm
     * Date: 2023/4/23
     * @param $data
     * @return array
     */
    public function deleteInstrumentPicture($data)
    {
        // 更新数据库文件栏
        InstrumentsModel::updateAll(
            ['pictures' => $data['currentFiles']],
            ['id' => $data['instrumentId']]
        );

        // 构造链接
        $deleteFileString = '<a target="_blank" href=?r=download/file&type=1&path=' . $data['deleteFile']['dep_path'] . '&name=' .
            $data['deleteFile']['save_name'] . '&file_name=' .
            rawurlencode($data['deleteFile']['real_name']) . '>' .
            $data['deleteFile']['real_name'] . ' </a>';

        $action_details = \Yii::t('base', 'delete_reason') . ":" . $data['deleteReason'] . "</br>";
        $action_details .= (\Yii::t('base', 'delete_file') . ":" . $deleteFileString);

        $hisData = [
            'action_details' => $action_details,
        ];

        $this->addInstrumentHistory($data['instrumentId'], self::ACTION_DELETE_PICTURE, $hisData);

        return $this->success([]);
    }

    /**
     * Notes: 上传仪器文件
     * Author: hkk
     * Date: 2020/4/29 11:00
     * @param $data
     * @return array
     * @throws Exception
     */
    public function uploadInstrumentFile($data)
    {
        // 更新数据库文件栏
        InstrumentsModel::updateAll(
            ['files' => $data['currentFiles']],
            ['id' => $data['instrumentId']]
        );

        $action_details = (\Yii::t('base', 'upload_file') . ":" . $data['uploadFiles']);
        $hisData = [
            'action_details' => $action_details,
        ];


        $this->addInstrumentHistory($data['instrumentId'], self::ACTION_UPLOAD_FILE, $hisData);

        return $this->success([]);
    }

    /**
     * Notes: inscada汇总页面的数据
     */
    public function listInscadaSummary($where, $limit, $page, $forExport = false)
    {
        //
        $model = new InstrumentDataNumericalModel();
        if ($where['dataType'] == 2) $model = new InstrumentDataFileModel();
        $query = $model::find()->from($model::tableName() . ' Ind');
        $query->leftJoin(InstrumentsModel::tableName() . ' AS Ins', 'Ins.batch_number=Ind.batch_number');
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ind.batch_number';
        $query->select[] = 'Ins.position';
        $query->select[] = 'Ins.id AS instrument_id';
        $query->select[] = 'Ind.timestamp';

        $query->select[] = 'Ind.operate_users';
        $query->select[] = 'Ind.exp_pages';
        $query->select[] = 'Ind.remark';
        $query->select[] = 'Ind.status';
        $query->select[] = 'Ind.id';
        $query->select[] = 'Ins.groupIds'; // add by zwm 2022/10/28 群组id
        $query->select[] = 'Ins.departmentIds'; // add by zwm 2022/10/28 部门id

        if ($where['dataType'] == "1") {
            $query->select[] = 'Ind.raw_data';
            $query->select[] = 'Ind.numerical_value';
            $query->select[] = 'Ind.unit';
            if (!empty($where['keyword'])) { // 数值类多字段模糊匹配
                $where['keyword'] = trim($where['keyword']);
                $query->andFilterWhere(['like', 'Ind.numerical_value', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.unit', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.remark', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.exp_pages', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.name', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.batch_number', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.position', $where['keyword']]);
            }
            if (!empty($where['numerical_instrument_type'])) {
                $query->andWhere(['Ind.numerical_instrument_type' => $where['numerical_instrument_type']]);
            }
        } else {
            $query->select[] = 'Ind.filename';
            $query->select[] = 'Ind.filepath';
            $query->select[] = 'Ind.save_name';
            $query->select[] = 'Ind.file_host';
            if (!empty($where['keyword'])) { // 文件类多字段模糊匹配
                $where['keyword'] = trim($where['keyword']);
                $query->andFilterWhere(['like', 'Ind.remark', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.exp_pages', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.name', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.batch_number', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.position', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.filename', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.filepath', $where['keyword']]);
            }
        }
        if ($where['status'] != '') {
            $query->andWhere(['Ind.status' => $where['status']]);
        }
        if (!empty($where['start_time'])) {
            $query->andWhere(['>=', 'Ind.timestamp', $where['start_time']]);
        }
        if (!empty($where['end_time'])) {
            $query->andWhere(['<=', 'Ind.timestamp', $where['end_time']]);
        }

        if (!empty($where['operate_users'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, Ind.operate_users)'));
            $query->addParams([':operate_users' => $where['operate_users']]);
        }
        if (!empty($where['instrument'])) {
            $query->andWhere(['Ins.id' => $where['instrument']]);
        }

        if ($where['viewAuth'] == '2') { // add by zwm 2022/10/28 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }

            $FindStr .= "FIND_IN_SET('all', Ins.groupIds)"; // add by zwm 2022/10/28  加不限仪器的判断
            // $FindStr = substr($FindStr, 0, -4);
            $query->andWhere(new Expression($FindStr));
        }

        if (!empty($where['belong_group'])) { // add by zwm 2022/10/28 所属鹰群筛选
            $belong_groups = explode(',', $where['belong_group']);
            $FindArr = ['or'];
            foreach ($belong_groups as $belong_group) {
                $FindArr[] = "FIND_IN_SET($belong_group, Ins.groupIds)";
            }

            $query->andWhere($FindArr);
        }

        if (!empty($where['belong_department'])) { // add by zwm 2022/10/28 所属部门筛选
            $belong_departments = explode(',', $where['belong_department']);
            $FindArr = ['or'];
            foreach ($belong_departments as $belong_department) {
                $FindArr[] = "FIND_IN_SET($belong_department, Ins.departmentIds)";
            }
            $query->andWhere($FindArr);
        }

        // 允许查看他人数据
        $userAuth = CompanyAuthServer::getCompanyAuthByUserId(\Yii::$app->view->params['curr_user_id']);
        if (empty($userAuth['company_feature']['view_other_inscada_data'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, Ind.operate_users) OR Ind.operate_users = ""'));
            $query->addParams([':operate_users' => \Yii::$app->view->params['curr_user_id']]);
        }

        // 不看别人数据
        if (!empty($where['filter_unclaimed'])) {
            $query->andWhere(['OR', ['Ind.operate_users' => ''], ['like', 'Ind.operate_users', $where['curr_user_id']]]);
        }

        // 签字人筛选
        if (!empty($where['inscada_signer'])) {
            $inscadaIdList = InstrumentDataHistoryModel::find()->select(['inscada_id'])->where([
                'operate_user' => $where['inscada_signer'],
            ])->andWhere(['OR',
                ['type' => '签字'],
                ['type' => '批量签字'],
                ['type' => 'instrument_sign'],
                ['type' => 'batch_sign']
            ])->andWhere(['OR',
                ['data_type' => $where['dataType']],
                ['data_type' => null] // 这里要兼容没有对接类型的老数据
            ])->asArray()->all();
            $inscadaIdList = array_unique(array_column($inscadaIdList, 'inscada_id'));
            $query->andWhere(['Ind.id' => $inscadaIdList]);
        }
        if ($forExport && !empty($where['chooseIds'])) {
            $query->andWhere(['Ind.id' => $where['chooseIds']]);
        }
        $query->groupBy('Ind.id');
        $totalCount = $query->count();

        if (!$forExport) {
            $query->offset(($page - 1) * $limit)->limit($limit);
        }


        $result = $query->orderBy('timestamp desc')->asArray()->all();

        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');
        $bindingInfo = (new CenterInterface())->initFileServer();

        if ($forExport) { // add by zwm 2022/10/31
            $groupList = (new CenterInterface())->getGroupsListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所属鹰群
            $groupsInfo = array_column($groupList, NULL, 'id');

            $departmentsInfo = [];
            $departmentList = (new CenterInterface())->getDepartmentListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所属鹰群
            if (isset($departmentList['list']) && !empty($departmentList['list'])) {
                $departmentsInfo = array_column($departmentList['list'], NULL, 'id');
            }
        }
//        foreach ($result as $key => $value) {
//            if ($forExport) {
//                // 所属鹰群名称
//                $groupNames = [];
//                $defaultGroupIds = array_filter(explode(',', $value['groupIds'])); // add by zwm 2022/10/31
//                foreach ($defaultGroupIds as $groupId) {
//                    $groupNames[] = !empty($groupsInfo[$groupId]) ? $groupsInfo[$groupId]['group_name'] : '';
//                }
//                $result[$key]['group_name_string'] = join(';', $groupNames);
//
//                $departmentNames = [];
//                $defaultDepartmentIds = array_filter(explode(',', $value['departmentIds'])); // add by zwm 2022/10/31
//                foreach ($defaultDepartmentIds as $departmentId) {
//                    $departmentNames[] = !empty($departmentsInfo[$departmentId]) ? $departmentsInfo[$departmentId]['department_name'] : '';
//                }
//                $result[$key]['department_name_string'] = join(';', $departmentNames);
//            }
//        }

        foreach ($result as $key => $value) {
            // 将操作人的 id 解析为用户名
            if (!empty($value['operate_users'])) {
                $result[$key]['operate_users_string'] = $this->getUsersNameStrings($userListInfo, $value['operate_users']);
                // file_put_contents('log2.txt', json_encode($value['operate_users']), FILE_APPEND);
            } else {
                $result[$key]['operate_users_string'] = '';
            }
            // 解析实验记录本，解析成数组
            if (!empty($value['exp_pages'])) {
                $result[$key]['exp_pages_arr'] = array_filter(explode(' ', $value['exp_pages']), function ($v) {
                    return !($v == ' ' || $v == '');
                });  //分割实验编号获得实验编号列表
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                // 导出时按照转换后的数组来
                $result[$key]['exp_pages_arr_str'] = implode(' ', $result[$key]['exp_pages_arr']);
            } else {
                $result[$key]['exp_pages_arr'] = '';
                $result[$key]['exp_pages_arr_str'] = '';
            }
            $result[$key]['status_string'] = $value['status'] == 1 ? '正常' : '已删除';
            if (isset($value['file_host']) && isset($bindingInfo['server_to_path'][$value['file_host']])) {
                $result[$key]['save_path'] = substr($bindingInfo['server_to_path'][$value['file_host']], strlen(\Yii::getAlias('@filepath')));
            } else {
                $result[$key]['save_path'] = '';
            }
        }
        $data['inscada_list'] = $result;
        $data['totalCount'] = $totalCount;
        return $data;
    }


    /**
     * Notes: inscada汇总页面顶部的统计图表-数据对接类型通过 $where 传递
     */
    public function getInscadaStatistics($where)
    {
        //
        $model = new InstrumentDataNumericalModel();
        if ($where['dataType'] == 2) $model = new InstrumentDataFileModel();
        $query = $model::find()->from($model::tableName() . ' Ind');
        $query->leftJoin(InstrumentsModel::tableName() . ' AS Ins', 'Ins.batch_number=Ind.batch_number');
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ind.batch_number';
        $query->select[] = 'Ins.position';
        $query->select[] = '`Ins`.`id` USING gbk) AS instrument_id';
        $query->select[] = 'Ind.timestamp';

        $query->select[] = 'count(Ind.batch_number) as data_num';
        $query->select[] = 'group_concat(Ind.operate_users) as operate_users';
        $query->select[] = 'group_concat(Ind.exp_pages) as exp_pages';
        $query->select[] = 'group_concat(Ind.remark) as remark';
        $query->select[] = 'Ind.status';
        $query->select[] = 'Ind.id';

        //状态
//        $query->andWhere(['Ind.status' => 1]);
        if ($where['status'] != '') {
            $query->andWhere(['Ind.status' => $where['status']]);
        }

        //关键字
        if ($where['dataType'] == "1") {
            $query->select[] = 'Ind.numerical_value';
            $query->select[] = 'Ind.unit';
            if (!empty($where['keyword'])) { // 数值类多字段模糊匹配
                $where['keyword'] = trim($where['keyword']);
                $query->andFilterWhere(['like', 'Ind.numerical_value', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.unit', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.remark', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.exp_pages', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.name', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.batch_number', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.position', $where['keyword']]);
            }
        } else {
            $query->select[] = 'Ind.filename';
            $query->select[] = 'Ind.filepath';
            if (!empty($where['keyword'])) { // 文件类多字段模糊匹配
                $where['keyword'] = trim($where['keyword']);
                $query->andFilterWhere(['like', 'Ind.remark', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.exp_pages', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.name', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.batch_number', $where['keyword']])
                    ->orFilterWhere(['like', 'Ins.position', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.filename', $where['keyword']])
                    ->orFilterWhere(['like', 'Ind.filepath', $where['keyword']]);
            }
        }

        //操作人员
        if (!empty($where['operate_users'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, Ind.operate_users)'));
            $query->addParams([':operate_users' => $where['operate_users']]);
        }

        // 位置
        if (!empty($where['position'])) {
            $query->andWhere(['like', 'Ins.position', $where['position']]);
        }

        //日期
        if (!empty($where['start_time'])) {
            $query->andWhere(['>=', 'Ind.timestamp', $where['start_time'] . ' 00:00:00']);
        }
        if (!empty($where['end_time'])) {
            $query->andWhere(['<=', 'Ind.timestamp', $where['end_time'] . ' 23:59:59']);
        }

        //权限判断
        if ($where['viewAuth'] == '2') { // add by zwm 2022/10/28 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }

            $FindStr .= "FIND_IN_SET('all', Ins.groupIds)"; // add by zwm 2022/10/28  加不限仪器的判断
            $query->andWhere(new Expression($FindStr));
        }

        if (!empty($where['belong_group'])) { // add by zwm 2022/10/28 所属鹰群筛选
            $belong_groups = explode(',', $where['belong_group']);
            $FindArr = ['or'];
            foreach ($belong_groups as $belong_group) {
                $FindArr[] = "FIND_IN_SET($belong_group, Ins.groupIds)";
            }

            $query->andWhere($FindArr);
        }

        if (!empty($where['belong_department'])) { // add by zwm 2022/10/28 所属部门筛选
            $belong_departments = explode(',', $where['belong_department']);
            $FindArr = ['or'];
            foreach ($belong_departments as $belong_department) {
                $FindArr[] = "FIND_IN_SET($belong_department, Ins.departmentIds)";
            }
            $query->andWhere($FindArr);
        }
        $result = $query->groupBy('Ind.batch_number')->orderBy('data_num desc')->asArray()->all();
        $data['data'] = $result;
        return $data;
    }

    /**
     * Notes: Inscada 仪器数据对接详情页面，对接类型为数值时
     * @param $where
     * @param $limit
     * @param $page
     */
    public function listInsDataNumericalView($where, $limit = 15, $page = 1, $forExport = false, $forAutoInsert = false)
    {
        $query = InstrumentDataNumericalModel::find()->from(InstrumentDataNumericalModel::tableName() . ' IDN');
        if (!empty($where['inscada_signer'])) {
            $inscadaIdList = InstrumentDataHistoryModel::find()->select(['inscada_id'])->where([
                'operate_user' => $where['inscada_signer'],
            ])->andWhere(['OR',
                ['type' => '签字'],
                ['type' => '批量签字'],
                ['type' => 'instrument_sign'],
                ['type' => 'batch_sign']
            ])->andWhere(['OR',
                ['data_type' => 1],
                ['data_type' => null] // 这里要兼容没有对接类型的老数据
            ])->asArray()->all();
            $inscadaIdList = array_unique(array_column($inscadaIdList, 'inscada_id'));
            $query->andWhere(['IDN.id' => $inscadaIdList]);
        }
        if (isset($where['batch_number'])) {
            $query->andWhere(['IDN.batch_number' => $where['batch_number']]);
        }
        if ($where['status'] != '') {
            $query->andWhere(['IDN.status' => $where['status']]);
        }
        if (!empty($where['keyword'])) { // 多字段模糊匹配
            $where['keyword'] = trim($where['keyword']);
            // $query->andFilterWhere(['like', 'IDN.numerical_value', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDN.unit', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDN.remark', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDN.exp_pages', $where['keyword']]);
            // bug#31548，使用新的 or 查询逻辑，原有的查询逻辑有误
            $query->andFilterWhere([
                'or',
                ['like', 'IDN.numerical_value', $where['keyword']],
                ['like', 'IDN.unit', $where['keyword']],
                ['like', 'IDN.remark', $where['keyword']],
                ['like', 'IDN.exp_pages', $where['keyword']],
            ]);
        }
        if (!empty($where['start_time'])) {
            $query->andWhere(['>=', 'IDN.timestamp', $where['start_time'] . ' 00:00:00']);
        }
        if (!empty($where['end_time'])) {
            $query->andWhere(['<=', 'IDN.timestamp', $where['end_time'] . ' 23:59:59']);
        }
        if (!empty($where['instrument_recent_time']) && !empty($where['recent_data_filter'])) {
            $query->andWhere([
                '>=',
                'IDN.timestamp',
                new Expression('NOW() - INTERVAL ' . $where['instrument_recent_time'] . ' ' . $where['recent_data_filter'])
            ]);
        }
        $userAuth = CompanyAuthServer::getCompanyAuthByUserId(\Yii::$app->view->params['curr_user_id']);
        if (empty($userAuth['company_feature']['view_other_inscada_data'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, IDN.operate_users) OR IDN.operate_users = ""'));
            $query->addParams([':operate_users' => \Yii::$app->view->params['curr_user_id']]);
        }
        if (!empty($where['operate_users'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, IDN.operate_users)'));
            $query->addParams([':operate_users' => $where['operate_users']]);
        }
        if (!empty($where['data_id'])) {
            $query->andWhere(['IDN.id' => explode(',', $where['data_id'])]);
        }

        // 自动插入，拿上次获取数据后的新增数据
        if (!empty($where['last_fetch_time'])) {
            $query->andWhere(['>', 'IDN.timestamp', $where['last_fetch_time']]);
        }

        // 不看别人数据
        if (!empty($where['filter_unclaimed'])) {
            $query->andWhere(['OR', ['IDN.operate_users' => ''], ['like', 'IDN.operate_users', $where['curr_user_id']]]);
        }

        // 倒序
        $query->orderBy('id desc')->asArray();

        if (!$forExport && !$forAutoInsert) {
            $result = $query->offset(($page - 1) * $limit)->limit($limit)->all();

            // $data['data'] = $query->offset(($page - 1) * $limit)->limit($limit)->all();
        } else { // 导出全部不分页

            if (!empty($where['chooseIds'])) {
                $queryId = InstrumentDataNumericalModel::find()->from(InstrumentDataNumericalModel::tableName() . ' IDN');
                $queryId->andWhere(['IDN.id' => $where['chooseIds']]);
                $result = $queryId->orderBy('id desc')->asArray()->all();
            } else {
                $result = $query->all();
            }
            // $data['data'] = $query->all();
        }


        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');

        foreach ($result as $key => $value) {
            // 将操作人的 id 解析为用户名
            if (!empty($value['operate_users'])) {
                $result[$key]['operate_users_string'] = $this->getUsersNameStrings($userListInfo, $value['operate_users']);
                // file_put_contents('log2.txt', json_encode($value['operate_users']), FILE_APPEND);
            } else {
                $result[$key]['operate_users_string'] = '';
            }
            // 解析实验记录本，解析成数组
            if (!empty($value['exp_pages'])) {
                $result[$key]['exp_pages_arr'] = array_filter(explode(' ', $value['exp_pages']), function ($v) {
                    return !($v == ' ' || $v == '');
                });  //分割实验编号获得实验编号列表
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                // 导出时按照转换后的数组来
                $result[$key]['exp_pages_arr_str'] = implode(' ', $result[$key]['exp_pages_arr']);
            } else {
                $result[$key]['exp_pages_arr'] = '';
                // 导出时按照转换后的数组来
                $result[$key]['exp_pages_arr_str'] = '';
            }
            $result[$key]['status_string'] = $value['status'] == 1 ? '正常' : '已删除';
        }

        $data['data'] = $result;
        // 数据总量，供分页组件使用
        $data['totalCount'] = $query->count();
        return $data;
    }

    /**
     * Notes: Inscada 仪器数据对接详情页面，对接类型为文件时
     * @param $where
     * @param $limit
     * @param $page
     */
    public function listInsDataFileView($where, $limit = 15, $page = 1, $forExport = false, $forAutoInsert = false)
    {
        $query = InstrumentDataFileModel::find()->from(InstrumentDataFileModel::tableName() . ' IDF');
        if (!empty($where['inscada_signer'])) {
            $inscadaIdList = InstrumentDataHistoryModel::find()->select(['inscada_id'])->where([
                'operate_user' => $where['inscada_signer'],
            ])->andWhere(['OR',
                ['type' => '签字'],
                ['type' => '批量签字'],
                ['type' => 'instrument_sign'],
                ['type' => 'batch_sign']
            ])->andWhere(['OR',
                ['data_type' => 2],
                ['data_type' => null] // 这里要兼容没有对接类型的老数据
            ])->asArray()->all();
            $inscadaIdList = array_unique(array_column($inscadaIdList, 'inscada_id'));
            $query->andWhere(['IDF.id' => $inscadaIdList]);
        }
        $query->andWhere(['IDF.batch_number' => $where['batch_number']]);
        if ($where['status'] != '') {
            $query->andWhere(['IDF.status' => $where['status']]);
        }
        if (!empty($where['keyword'])) { // 多字段模糊匹配
            $where['keyword'] = trim($where['keyword']);
            //bug#31548, 修改构建sql的or查询条件
            $query->andFilterWhere([
                'or',
                ['like', 'IDF.filename', $where['keyword']],
                ['like', 'IDF.filepath', $where['keyword']],
                ['like', 'IDF.remark', $where['keyword']],
                ['like', 'IDF.exp_pages', $where['keyword']],
            ]);
            // $query->andFilterWhere(['like', 'IDF.filename', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDF.filepath', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDF.remark', $where['keyword']])
            //     ->orFilterWhere(['like', 'IDF.exp_pages', $where['keyword']]);
        }
        if (!empty($where['start_time'])) {
            $query->andWhere(['>=', 'IDF.timestamp', $where['start_time'] . ' 00:00:00']);
        }
        if (!empty($where['end_time'])) {
            $query->andWhere(['<=', 'IDF.timestamp', $where['end_time'] . ' 23:59:59']);
        }

        // 允许查看他人数据
        $userAuth = CompanyAuthServer::getCompanyAuthByUserId(\Yii::$app->view->params['curr_user_id']);
        if (empty($userAuth['company_feature']['view_other_inscada_data'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, IDF.operate_users) OR IDF.operate_users = ""'));
            $query->addParams([':operate_users' => \Yii::$app->view->params['curr_user_id']]);
        }

        if (!empty($where['instrument_recent_time']) && !empty($where['recent_data_filter'])) {
            $query->andWhere([
                '>=',
                'IDF.timestamp',
                new Expression('NOW() - INTERVAL ' . $where['instrument_recent_time'] . ' ' . $where['recent_data_filter'])
            ]);
        }
        if (!empty($where['operate_users'])) {
            $query->andWhere(new Expression('FIND_IN_SET(:operate_users, IDF.operate_users)'));
            $query->addParams([':operate_users' => $where['operate_users']]);
        }
        if (!empty($where['data_id'])) {
            $query->andWhere(['IDF.id' => explode(',', $where['data_id'])]);
        }

        // 不看别人数据
        if (!empty($where['filter_unclaimed'])) {
            $query->andWhere(['OR', ['IDF.operate_users' => ''], ['like', 'IDF.operate_users', $where['curr_user_id']]]);
        }

        // 倒序
        $query->orderBy('id desc')->asArray();

        if (!$forExport) {
            $result = $query->offset(($page - 1) * $limit)->limit($limit)->all();

            // $data['data'] = $query->offset(($page - 1) * $limit)->limit($limit)->all();
        } else { // 导出全部不分页

            if (!empty($where['chooseIds'])) {
                $queryId = InstrumentDataFileModel::find()->from(InstrumentDataFileModel::tableName() . ' IDF');
                $queryId->andWhere(['IDF.id' => $where['chooseIds']]);
                $result = $queryId->orderBy('id desc')->asArray()->all();
            } else {
                $result = $query->all();
            }
            // $data['data'] = $query->all();
        }


        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');
        $bindingInfo = (new CenterInterface())->initFileServer();

        foreach ($result as $key => $value) {
            // 将操作人的 id 解析为用户名
            if (!empty($value['operate_users'])) {
                $result[$key]['operate_users_string'] = $this->getUsersNameStrings($userListInfo, $value['operate_users']);
                // file_put_contents('log2.txt', json_encode($value['operate_users']), FILE_APPEND);
            } else {
                $result[$key]['operate_users_string'] = '';
            }
            // 解析实验记录本，解析成数组
            if (!empty($value['exp_pages'])) {
                $result[$key]['exp_pages_arr'] = array_filter(explode(' ', $value['exp_pages']), function ($v) {
                    return !($v == ' ' || $v == '');
                });  //分割实验编号获得实验编号列表
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                $result[$key]['exp_pages_arr'] = array_flip($result[$key]['exp_pages_arr']);
                // 导出时按照转换后的数组来
                $result[$key]['exp_pages_arr_str'] = implode(' ', $result[$key]['exp_pages_arr']);
            } else {
                $result[$key]['exp_pages_arr'] = '';
                // 导出时按照转换后的数组来
                $result[$key]['exp_pages_arr_str'] = '';
            }
            $result[$key]['status_string'] = $value['status'] == 1 ? '正常' : '已删除';
            if (isset($bindingInfo['server_to_path'][$value['file_host']])) {
                /**
                 * 这里相当于将eln服务器上/home/<USER>/files/instruments_<id> 去除了文件根路径/home/<USER>/files
                 * @see \frontend\interfaces\CenterInterface::mountFileFolder
                 */
                $result[$key]['save_path'] = substr($bindingInfo['server_to_path'][$value['file_host']], strlen(\Yii::getAlias('@filepath')));
            } else {
                $result[$key]['save_path'] = '';
            }

        }

        $data['data'] = $result;
        // 数据总量，供分页组件使用
        $data['totalCount'] = $query->count();
        return $data;
    }


    /**
     * Notes: Inscada数据修改后台逻辑
     * @param $newData :新数据
     * @param $id 数据 id
     * @param $uid 操作人
     * @param $type 操作类型
     */
    public function editInsDataNumerical($newData, $ids, $uid, $type)
    {
        $inscadaDetails = InstrumentDataNumericalModel::findAll(['id' => $ids]);
        // file_put_contents('log2.txt', json_encode($ids));
        $transaction = \Yii::$app->integle_ineln->beginTransaction();

        foreach ($inscadaDetails as $inscadaDetail) {
            switch ($type) {
                case 'receive':
                case 'batch_receive':
                case 'instant_receive':
                    $data['operate_users'] = $uid . ',' . $inscadaDetail['operate_users'];
                    $usersArray = array_filter(explode(',', $data['operate_users']), function ($v) {
                        return !($v == ' ' || $v == '');
                    }); // 分割用户 id
                    $data['operate_users'] = implode(',', array_unique($usersArray)); // 数据去重
                    if ($newData['exp_pages'] != '') {
                        $data['exp_pages'] = ($newData['exp_pages'] . ' ' . $inscadaDetail['exp_pages']);
                    }
                    if ($newData['remark'] != '') {
                        $data['remark'] = ($newData['remark'] . ' ' . $inscadaDetail['remark']);
                    }
                    break;
                case 'edit':
                    $data['status'] = $newData['status'];
                    $data['exp_pages'] = $newData['exp_pages'];
                    $data['remark'] = $newData['remark'];
                    $data['operate_users'] = @getVar($newData['operate_users']);
                    break;
                case 'delete':
                case 'batch_delete':
                    $data['status'] = $newData['status'];
                    break;
                default:
                    break;
            }
            InstrumentDataNumericalModel::updateAll($data, ['id' => $inscadaDetail['id']]);
            // 添加痕迹
            $newData['data_type'] = 1;
            $isAddHistory = $this->addInsDataHistory($uid, $type, $newData, $inscadaDetail);
            if (!$isAddHistory) {
                // 如果没有添加痕迹，就连之前的改动也取消
                $transaction->rollBack();
                return $this->fail('Please retry');
            }
        }
        $transaction->commit();
        return $this->success([]);
    }


    /**
     * Notes: Inscada数据修改后台逻辑
     * @param $newData :新数据
     * @param $id 数据 id
     * @param $uid 操作人
     * @param $type 操作类型
     */
    public function editInsDataFile($newData, $ids, $uid, $type, $needUploadFile = true, $expId = '')
    {
        // 获取文件路径前需要先初始化挂载eln文件服务器(如果有)
        ElnFileServer::getInstance();
        //
        $inscadaDetails = InstrumentDataFileModel::find()->where(['id' => $ids])->asArray()->all();
        $receiveState = 2; // 状态2： 全部领取成功并添加到实验记录本中（假设）
        $addFilesRes = [];
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        // 如果是领取，除了标记操作人和添加痕迹外，还需要将文件加入记录本
        if (($type == 'receive' || $type == 'batch_receive' || $type == 'instant_receive_file') && $needUploadFile) {
            // 1. 上传文件
            // 先获取记录本 ID
            $expId = '';
            if (!empty($newData['exp_pages'])) {
                $exp_page = $newData['exp_pages'];
                // 根据实验编号获取实验ID
                $expServer = new ExperimentServer();
                $result = $expServer->getExpIdByExpNum($exp_page);
                // 如果实验记录本页码非法，则拦截
                if (empty($result['data'])) {
                    $transaction->rollBack();
                    return $this->fail(\Yii::t('comment', 'no_exp'));
                } else {
                    $expId = $result['data'];
                }
            } else {
                $receiveState = 3; // 仅将用户标记为操作人，不上传文件
            }
            if (!empty($expId)) {
                $addFilesRes = $this->addFilesToExp($expId, $inscadaDetails, $uid);
                // 找不到上传模块/没有权限/多个上传模块
                if (!$addFilesRes['status']) return $this->success($addFilesRes);
                // 查询失败
                if ($addFilesRes['receiveState'] < 0) {
                    $transaction->rollBack();
                    return $this->fail('Please retry');
                }
            }
        }

        // 2. 更新 inscada 数据
        foreach ($inscadaDetails as $inscadaDetail) {
            $data['operate_users'] = $uid . ',' . $inscadaDetail['operate_users'];
            $usersArray = array_filter(explode(',', $data['operate_users']), function ($v) {
                return !($v == ' ' || $v == '');
            }); // 分割用户 id
            $data['operate_users'] = implode(',', array_unique($usersArray)); // 数据去重
            switch ($type) {
                case 'receive':
                case 'batch_receive':
                case 'instant_receive_file':
                case 'instant_receive':
                    if ($newData['exp_pages'] != '') {
                        $data['exp_pages'] = ($newData['exp_pages'] . ' ' . $inscadaDetail['exp_pages']);
                    }
                    break;
                case 'edit':
                    $data['status'] = $newData['status'];
                    $data['exp_pages'] = $newData['exp_pages'];
                    $data['remark'] = $newData['remark'];
                    $data['operate_users'] = @getVar($newData['operate_users']);
                    break;
                case 'delete':
                case 'batch_delete':
                    $data['status'] = $newData['status'];
                    break;
                default:
                    break;
            }
            InstrumentDataFileModel::updateAll($data, ['id' => $inscadaDetail['id']]);

            // 3. 添加痕迹
            $newData['data_type'] = 2;
            $isAddHistory = $this->addInsDataHistory($uid, $type, $newData, $inscadaDetail);
            if (!$isAddHistory) {
                // 如果没有添加痕迹，就连之前的改动也取消
                $transaction->rollBack();
                return $this->fail('Please retry');
            }
        }

        $transaction->commit();
        return $this->success(['receiveState' => $receiveState, 'expId' => $expId, 'addFilesRes' => $addFilesRes]);
    }

    /**
     * Notes: 判断指定 id 的用户是否有权限编辑实验记录本（仅用于 inscada 向上传文件模块添加文件时，因为判断条件不全）
     */
    public function hasExpBookEditAuth($expId, $uid)
    {
        //
        global $uid1;
        $uid1 = $uid;
        $expServer = new ExperimentServer();
        $baseData = (new ExperimentModel())->getBaseDataById($expId);
        $expBaseData = $baseData;
        // 查询实验是否可以编辑 草稿状态 & 非预审中, $edit：2 不可编辑 1 可以编辑)
        $edit = 2;
        if (($expBaseData['step'] == 1) && ($expBaseData['pretrial_status'] != 1)) {
            $edit = 1;
            // 无需判断实验是否有模块被其他人编辑
            // $editingUserIds = $expServer->getEditingUsers($expId);
            // $editingUserIds = array_filter($editingUserIds, function ($userId) {
            //     return ($userId != $GLOBALS['uid1']);
            // });

            // if (!empty($editingUserIds)) {
            //     $edit = 2;
            //     // $editingUsers = (new CenterInterface())->userDetailsByUserIds($editingUserIds);
            //     // $userNames = array_column($editingUsers, 'nick_name');
            //     // $tipInfo = join(',', $userNames) . \Yii::t('exp', 'experiment_is_editing_by_others');
            // }
        }
        return $edit;
    }


    /**
     * Notes: 将多个文件插入实验记录本的“上传文件”模块，根据记录本ID来上传
     */
    public function addFilesToExp($expId, $fileList, $uid)
    {
        $moduleArr = array();
        // 查询实验是否可以被编辑 先判断一部分，其余权限用 getUserAccessModules 判断
        if (InstrumentServer::hasExpBookEditAuth($expId, $uid) == 1) {
            // 自动判断用户是否有权限读取实验数据
            $expServer = new ExperimentServer();
            $modules = $expServer->getUserAccessModules($expId, $uid);
            $baseData = (new ExperimentModel())->getBaseDataById($expId);
            // 获取实验全部的上传文件模块
            foreach ($modules as $module) {
                if ($module['component_id'] == \Yii::$app->params['component']['upload']) {
                    // 判断该模块有没有编辑权限-两种情况：1该用户为记录本创建者 2该用户有模块的合著编辑权限
                    if (((!empty($module['coauthor_action'])) &&
                            (($module['coauthor_action'] == 'edit') || ($module['coauthor_action'] == 'save')))
                        || ($baseData['user_id'] == $uid)
                    ) {
                        $moduleArr[] = $module;
                    }
                }
            }
            if (count($moduleArr) > 0) {
                // 只有一个上传文件模块，无需二次判断
                if (count($moduleArr) == 1) {
                    $uploadModel = new Upload();
                    $module = $moduleArr[0];
                    foreach ($fileList as $key => $file) {
                        $elnFileServer = ElnFileServer::getInstance();
                        // 根据文件的file_host获取binding_id
                        $mountedFileRootPath = $elnFileServer->getFileRootPathByFileHost(ArrayHelper::getValue($file, ['file_host']));
                        $mountedFilePath = $elnFileServer->getFilePathByFileData($file);

                        $fileFolderRootPath = \Yii::getAlias('@filepath') . \Yii::getAlias('@instrumentFilePath');
                        $filePath = $file['filepath'];
                        // 老版scada上传文件路径以integle_transfer开头, 如果是老版scada上传文件, 使用scada的上传文件路径
                        if (preg_match('/integle_transfer/', $filePath) && isset($mountedFileRootPath)) {
                            $fileFolderRootPath = $mountedFileRootPath;
                            $filePath = $mountedFilePath;
                        }
                        $fileSavePath = $fileFolderRootPath . DS . $filePath . DS . $file['save_name'];
                        $savePath = str_replace('\\', '/', $fileSavePath);

                        $instrumentInfo = [
                            'batch_number' => $file['batch_number'],
                            'data_id' => $file['id']
                        ];
                        $uploadData = [
                            'instrument_data_info' => json_encode($instrumentInfo),
                            'pathname' => $savePath,
                            'path' => dirname($savePath),
                            'name' => $file['filename']
                        ];
                        $uploadResult = $uploadModel->createImportLog([$uploadData], $module['id']);
                        $fileList[$key]['upload_id'] = $uploadResult[0]['upload_id'];
                        $fileList[$key]['dep_path'] = $uploadResult[0]['dep_path'];
                        $fileList[$key]['eln_save_name'] = $uploadResult[0]['save_name'];
                    }
                    return ['status' => 1, 'receiveState' => 2, 'moduleArr' => $moduleArr, 'expId' => $expId, 'fileList' => $fileList];
                } else {
                    // 多个上传文件模块，需要选择
                    $receiveState = 1;
                }
            } else {
                $receiveState = 6; // 没有上传文件模块 或 没有权限查看编辑上传文件模块
            }
        } else {
            $receiveState = 5; // 当前实验所处的阶段不可编辑（已关闭）
        }
        return ['status' => 0, 'receiveState' => $receiveState, 'moduleArr' => $moduleArr, 'expId' => $expId, 'fileList' => $fileList];
    }


    /**
     * Notes: 将多个文件根据模块的 ID 插入上传文件模块中（不校验权限）
     */
    public function addFilesToModule($moduleId, $fileList, $data0, $ids, $uid, $type, $expId)
    {
        //
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        foreach ($fileList as $key => $file) {
            $instrumentInfo = [
                'batch_number' => $file['batch_number'],
                'data_id' => $file['id']
            ];
            $filePath = ($file['filepath'] == 'integle_transfer') ? ($file['filepath'] . DS . $file['batch_number']) : $file['filepath'];
            $savePath = \Yii::getAlias('@filepath') . \Yii::getAlias('@instrumentFilePath') . DS . $filePath . DS . $file['save_name'];
            $savePath = str_replace('\\', '/', $savePath);
            $uploadData = [
                'instrument_data_info' => json_encode($instrumentInfo),
                'pathname' => $savePath,
                'path' => dirname($savePath),
                'name' => $file['filename']
            ];
            foreach ($moduleId as $mid) {
                $uploadResult = (new Upload())->createImportLog([$uploadData], $mid);
                $fileList[$key]['upload_id'] = $uploadResult[0]['upload_id'];
                $fileList[$key]['dep_path'] = $uploadResult[0]['dep_path'];
                $fileList[$key]['eln_save_name'] = $uploadResult[0]['save_name'];
            }
        }
        // 标记操作人和痕迹
        $res = $this->editInsDataFile($data0, $ids, $uid, $type, false, $expId);
        if ($res['status'] != 1) {
            $transaction->rollBack();
            return $this->fail('retry please');
        }
        $transaction->commit();
        return $this->success($res['data']);
    }


    /**
     * Notes: 添加 Inscada 操作痕迹
     * @param $operate_user : 操作人
     * @param $type : 操作类型：编辑/删除/领取等
     * @param $newData : 变更后的数据
     * @param $inscadaDetail : 变更前的数据
     * @return int 1:成功，0:失败
     */
    function addInsDataHistory($operate_user, $type, $newData, $inscadaDetail)
    {
        $insDataHistory = new InstrumentDataHistoryModel();
        $historyData['operate_user'] = $operate_user;
        $historyData['type'] = \Yii::t('base', $type);
        $historyData['inscada_id'] = $inscadaDetail['id'];
        $historyData['detail'] = '';
        $historyData['data_type'] = $newData['data_type'];
        //changed by xieyuxiang 2023.1.6 痕迹记录逻辑修改
        switch ($type) {
            case 'receive':
            case 'batch_receive':
            case 'instant_receive':
            case 'instant_receive_file':
                $historyData['detail'] .= (\Yii::t('base', 'exp_pages') . ": " . $newData['exp_pages'] . '</br>');
                if (!empty($newData['remark'])) {
                    $historyData['detail'] .= \Yii::t('base', 'remark') . ': ' . $newData['remark'] . '<br>';
                }
                break;
            case 'edit':
                if ($newData['exp_pages'] != $inscadaDetail['exp_pages']) {
                    $historyData['detail'] .= (\Yii::t('base', 'exp_pages') . ": " . $inscadaDetail['exp_pages'] . ' -> ' . $newData['exp_pages'] . '<br>');
                }
                if ($newData['status'] != $inscadaDetail['status']) {
                    $historyData['detail'] .= \Yii::t('base', 'status') . ": "
                        . ($inscadaDetail['status'] == 1 ? \Yii::t('base', 'normal') : \Yii::t('base', 'already_deleted'))
                        . ' ->'
                        . ($newData['status'] == 1 ? \Yii::t('base', 'normal') : \Yii::t('base', 'already_deleted')) . '<br>';
                }
                if ($newData['remark'] != $inscadaDetail['remark']) {
                    $historyData['detail'] .= \Yii::t('base', 'remark') . ": " . $inscadaDetail['remark'] . ' -> ' . $newData['remark'] . '<br>';
                }
                if ($newData['operate_users'] != $inscadaDetail['operate_users']) {
                    // 所有人员信息
                    $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                    $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
                    $userListInfo = ArrayHelper::index($allUserListNew, 'id');
                    // 新旧人员对比
                    $historyData['detail'] .= \Yii::t('base', 'recipient') . ": " .
                        $this->getUsersNameStrings($userListInfo, $inscadaDetail['operate_users']) . ' -> ' .
                        $this->getUsersNameStrings($userListInfo, $newData['operate_users']);
                }
                break;
            case 'delete':
            case 'batch_delete':
                $historyData['detail'] .= \Yii::t('base', 'status') . ": " . \Yii::t('base', 'already_deleted') . '<br>';
                $historyData['detail'] .= \Yii::t('base', 'instrument_delete_reason') . ": " . $newData['instrument_delete_remark'] . '<br>';
                break;
            case 'approval':
                $historyData['detail'] .= \Yii::t('base', 'status') . ": " . \Yii::t('base', 'already_deleted') . '<br>';
                break;
            case 'instrument_sign':
            case 'batch_sign':
                // 添加痕迹
                $historyData['detail'] .= \Yii::t('base', 'instrument_sign_reason') . ": " . $newData['instrument_sign_remark'] . '<br>';
                break;
            default:
                break;
        }
        $insDataHistory->setAttributes($historyData);
        if (!$insDataHistory->save()) {
            return 0;
        }
        return 1;
    }

    /**
     * Notes: inscada 查看某项数据的痕迹
     */
    public function listInsDataNumHistory($where, $limit, $page, $forSign = false)
    {
        $query = InstrumentDataHistoryModel::find()->from(InstrumentDataHistoryModel::tableName() . ' IDH');
        $query->andWhere(['IDH.inscada_id' => $where['inscada_id']]);

        if (!empty($where['type'])) {
            $query->andWhere(['IDH.type' => $where['type']]);
        }

        if (!empty($where['dataType'])) {
            $query->andWhere(['OR',
                ['IDH.data_type' => 1],
                ['IDH.data_type' => null]] // 这里要兼容没有对接类型的老数据
            );
        }

        // 倒序
        $query->orderBy('id desc')->asArray();
        $data['totalCount'] = $query->count();

        // 如果是获取签字数据，不分页
        if (!$forSign) {
            $query->offset(($page - 1) * $limit)->limit($limit);
        } else {
            $query->andWhere(['OR',
                ['IDH.type' => '签字'],
                ['IDH.type' => '批量签字'],
                ['IDH.type' => 'instrument_sign'],
                ['IDH.type' => 'batch_sign']
            ]);
        }

        $result = $query->all();
        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');

        foreach ($result as $key => $value) {
            // 将操作人的 id 解析为用户名
            if (!empty($value['operate_user'])) {
                $result[$key]['operate_user_string'] = $this->getUsersNameStrings($userListInfo, $value['operate_user']);
            } else {
                $result[$key]['operate_user_string'] = '';
            }
        }
        $data['data'] = $result;
        return $data;
    }

    /**
     * Notes: inscada 查看某项数据的痕迹
     */
    public function listInsDataFileHistory($where, $limit, $page, $forSign = false)
    {

        $query = InstrumentDataHistoryModel::find()->from(InstrumentDataHistoryModel::tableName() . ' IFH');
        $query->andWhere(['IFH.inscada_id' => $where['inscada_id']]);
        if (!empty($where['type'])) {
            $query->andWhere(['IFH.type' => $where['type']]);
        }
        if (!empty($where['dataType'])) {
            $query->andWhere(['OR',
                    ['IFH.data_type' => 2],
                    ['IFH.data_type' => null]]
            );
        }

        // 倒序
        $query->orderBy('id desc')->asArray();
        $data['totalCount'] = $query->count();
        // 如果是获取签字数据，不分页
        if (!$forSign) {
            $query->offset(($page - 1) * $limit)->limit($limit);
        } else {
            $query->andWhere(['OR',
                ['IFH.type' => '签字'],
                ['IFH.type' => '批量签字'],
                ['IFH.type' => 'instrument_sign'],
                ['IFH.type' => 'batch_sign']
            ]);
        }

        $result = $query->all();
        // 所有人员信息
        $allUserList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $allUserListNew = isset($allUserList['list']) ? $allUserList['list'] : [];
        $userListInfo = ArrayHelper::index($allUserListNew, 'id');

        foreach ($result as $key => $value) {
            // 将操作人的 id 解析为用户名
            if (!empty($value['operate_user'])) {
                $result[$key]['operate_user_string'] = $this->getUsersNameStrings($userListInfo, $value['operate_user']);
            } else {
                $result[$key]['operate_user_string'] = '';
            }
        }
        $data['data'] = $result;

        return $data;
    }

    /**
     * Notes:校验/运行/维保字段顺序
     * Author: wh
     * Date:2023/6/29 13:44
     * @param $instrumentId
     * @param $type '1'是维保 '2'是校验 '3'是运行
     * @return array
     */
    public function getRecordField($instrumentId, $type)
    {

        switch ($type) {
            case 1://维保
                //获取字段名称及顺序
                // 预定义字段
                $definedField = [
                    'operation_time' => \Yii::t('base', 'created_time'),
                    'operator' => \Yii::t('base', 'start_operator'),
                    'repair_type' => \Yii::t('base', 'repair_type'),
                    'repair_start_time' => \Yii::t('base', 'repair_start_time'),
                    'repair_end_time' => \Yii::t('base', 'repair_end_time'),
                    'repair_content' => \Yii::t('base', 'repair_content'),
                    'repair_result' => \Yii::t('base', 'repair_result'),
                    'repair_person' => \Yii::t('base', 'repair_person'),
                    'book_exp_page' => \Yii::t('base', 'exp_code'),
                    'file' => \Yii::t('base', 'file'),
                    'review_record' => \Yii::t('base', 'review_record'),
                ];
                //取自定义的字段和字段顺序
                $extendFieldSelect = (new InstrumentRepairRecordExtendFieldModel())->attributeLabels();
                $extendFieldRes = InstrumentRepairRecordExtendFieldModel::find()
                    ->select(array_keys($extendFieldSelect))
                    ->where(['instrument_id' => $instrumentId])
                    ->asArray()->one();
                break;
            case 2://校验
                // 预定义字段
                $definedField = [
                    'operator' => \Yii::t('base', 'start_operator'),
                    'operation_time' => \Yii::t('base', 'created_time'),
                    'start_check_time' => \Yii::t('base', 'check_start_time'),
                    'end_check_time' => \Yii::t('base', 'check_end_time'),
                    'check_person' => \Yii::t('base', 'check_personnel'),
                    'check_institution' => \Yii::t('base', 'check_institution'),
                    'check_content' => \Yii::t('base', 'check_content'),
                    'book_exp_page' => \Yii::t('base', 'exp_code'),
                    'start_end_expiry_time' => \Yii::t('base', 'expiry_date'),
                    'remind_expiry_time' => \Yii::t('base', 'expiry_date_remind'),
                    'file' => \Yii::t('base', 'file'),
                    'review_record' => \Yii::t('base', 'review_record'),
                ];
                //取自定义的字段和字段顺序
                $extendFieldSelect = (new InstrumentCheckRecordExtendFieldModel())->attributeLabels();
                $extendFieldRes = InstrumentCheckRecordExtendFieldModel::find()
                    ->select(array_keys($extendFieldSelect))
                    ->where(['instrument_id' => $instrumentId])
                    ->asArray()->one();
                break;
            case 3://运行
                // 预定义字段
                $definedField = [
                    'start_operator_time' => \Yii::t('base', 'created_time'),
                    'start_operator' => \Yii::t('base', 'start_operator'),
                    'start_running_time' => \Yii::t('base', 'start_running_time'),
                    'end_running_time' => \Yii::t('base', 'end_running_time'),
                    'running_content' => \Yii::t('base', 'operation_content'),
                    'operator' => \Yii::t('base', 'operator'),
                    'book_exp_page' => \Yii::t('base', 'exp_code'),
                    'file' => \Yii::t('base', 'file'),
                    'end_operator_time' => \Yii::t('base', 'end_operation_time'),
                    'end_operator' => \Yii::t('base', 'end_operator'),
                    'review_record' => \Yii::t('base', 'review_record'),

                ];
                //取自定义的字段和字段顺序
                $extendFieldSelect = (new InstrumentRunningRecordExtendFieldModel())->attributeLabels();
                $extendFieldRes = InstrumentRunningRecordExtendFieldModel::find()
                    ->select(array_keys($extendFieldSelect))
                    ->where(['instrument_id' => $instrumentId])
                    ->asArray()->one();
                break;
            default:
                break;
        }

        $extendFieldRes = !empty($extendFieldRes) ? $extendFieldRes : [];
        $fieldSortList = !empty($extendFieldRes['field_sort']) ? json_decode($extendFieldRes['field_sort'], true) : [];

        // 自定义字段，清除无数值的字段和不需要显示的字段
        $extendField = [];
        foreach ($extendFieldRes as $key => $value) {
            if (in_array($key, ['id', 'instrument_id', 'field_sort'])) {
                continue;
            }
            if (empty($value)) {
                continue;
            }
            $extendField[$key] = $value;
        }

        // 重置排序
        $returnField2Show = [];
        foreach ($fieldSortList as $field) {
            if (isset($definedField[$field])) {
                $returnField2Show[$field] = $definedField[$field];
                unset($definedField[$field]);
            }
            if (isset($extendField[$field])) {
                $returnField2Show[$field] = $extendField[$field];
                unset($extendField[$field]);
            }
        }

        return $keyResult = array_merge($returnField2Show, $definedField, $extendField);

    }

    /**
     * @Notes: 仪器库6大时间更新
     * @param $instrumentId
     * @throws Exception
     * <AUTHOR>
     * @DateTime: 2023/7/13 9:35
     */
    public function updateInstrumentsTimeRecord($instrumentId)
    {

        //仪器库时间更新跟设置挂钩
        $instrumentInfo = InstrumentsModel::find()->select('record_setting')->where([
            'id' => $instrumentId,
        ])->asArray()->one();
        $runRecordReview = false;
        $checkRecordReview = false;
        $repairRecordReview = false;
        if (!empty($instrumentInfo['record_setting'])) {
            $recordSetting = json_decode($instrumentInfo['record_setting'], true);
            if (isset($recordSetting['operate_check_users'])) {
                $runRecordReview = true;
            }
            if (isset($recordSetting['repair_check_users'])) {
                $repairRecordReview = true;
            }
            if (isset($recordSetting['check_check_users'])) {
                $checkRecordReview = true;
            }
        }
        $tran = null;
        try {
            $tran = \Yii::$app->getDb()->beginTransaction();
            //查询维保记录
            $repairRecordQuery = InstrumentRepairRecordModel::find()
                ->from(InstrumentRepairRecordModel::tableName())
                ->where([
                    'instrument_id' => $instrumentId,
                    'if_end' => 1,
                    'if_void' => 0
                ]);
            if ($repairRecordReview) {
                $repairRecordQuery->andWhere([
                    'or',
                    ['review_conclusion' => null],
                    ['review_conclusion' => 1],
                ]);
            }
            $repairRecord = $repairRecordQuery->orderBy('repair_end_time desc')
                ->asArray()
                ->one();


            //查询校验记录
            $checkRecordQuery = InstrumentCheckRecordModel::find()
                ->from(InstrumentCheckRecordModel::tableName())
                ->where([
                    'instrument_id' => $instrumentId,
                    'if_end' => 1,
                    'if_void' => 0
                ]);
            if ($checkRecordReview) {
                $checkRecordQuery->andWhere([
                    'or',
                    ['review_conclusion' => null],
                    ['review_conclusion' => 1],
                ]);
            }
            $checkRecord = $checkRecordQuery->orderBy('end_check_time desc')
                ->asArray()
                ->one();

            //查询运行记录
            $runRecordQuery = InstrumentRunningRecordModel::find()
                ->from(InstrumentRunningRecordModel::tableName())
                ->where([
                    'instrument_id' => $instrumentId,
                    'if_end' => 1,
                    'if_void' => 0
                ]);
            if ($runRecordReview) {
                $runRecordQuery->andWhere([
                    'or',
                    ['review_conclusion' => null],
                    ['review_conclusion' => 1],
                ]);
            }
            $runRecord = $runRecordQuery->orderBy('end_running_time desc')
                ->asArray()
                ->one();

            //查询仪器记录
            $instrumentData = InstrumentsModel::find()
                ->from(InstrumentsModel::tableName())
                ->where(['id' => $instrumentId])
                ->asArray()
                ->one();
            if (!empty($instrumentData)) {
                $instrumentData['repair_start_time'] = !empty($repairRecord) ? $repairRecord['repair_start_time'] : '';
                $instrumentData['repair_end_time'] = !empty($repairRecord) ? $repairRecord['repair_end_time'] : '';
                $instrumentData['start_expiry_time'] = !empty($checkRecord) ? $checkRecord['start_expiry_time'] : '';
                $instrumentData['end_expiry_time'] = !empty($checkRecord) ? $checkRecord['end_expiry_time'] : '';
                $instrumentData['start_check_time'] = !empty($checkRecord) ? $checkRecord['start_check_time'] : '';
                $instrumentData['end_check_time'] = !empty($checkRecord) ? $checkRecord['end_check_time'] : '';
                $instrumentData['start_running_time'] = !empty($runRecord) ? $runRecord['start_running_time'] : '';
                $instrumentData['end_running_time'] = !empty($runRecord) ? $runRecord['end_running_time'] : '';
            }

            $instrumentModel = InstrumentsModel::findOne($instrumentId);
            $instrumentModel->setAttributes($instrumentData);
            if (!$instrumentModel->save()) {
                // 保存失败，处理错误
                $tran->rollBack();
                echo PHP_EOL . sprintf('Instruments save error, instrumentId: %s', $instrumentId);
                exit();
            }
            $tran->commit();
        } catch (\Exception $e) {
            $tran->rollBack();
            echo PHP_EOL . 'Exception occurred during data update: ' . $e->getMessage();
            exit();
        }

        return $this->success([]);

    }

    /**
     * Notes: 维保记录页面
     * Author: hkk
     * Date: 2020/7/17 13:19
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listInsRepairRecordView($where, $limit, $page, $forExport = false)
    {
        //获取字段名称及顺序
        $keyResult = $this->getRecordField($where['instrument_id'], 1);

        //获取字段数据
        $query = InstrumentRepairRecordModel::find()
            ->where([
                'instrument_id' => $where['instrument_id'],
                'status' => 1
            ])
            ->orderBy('id desc');
        if ($where['repair_type'] != 0) {
            $query->andWhere([
                'repair_type' => $where['repair_type']
            ]);
        }

        $totalCount = $query->asArray()->count();
        if (!$forExport) {
            $dataResult = $query->offset(($page - 1) * $limit)->limit($limit)->all();
        } else { // 导出全部不分页
            $dataResult = $query->all();
        }

        //根据创建者ID获取用户姓名 operator => 操作人列   reviewer => 复核人列
        $user_ids_1 = array_column($dataResult, 'operator');
        $user_ids_2 = array_column($dataResult, 'reviewer');
        $user_ids = array_unique(array_merge($user_ids_1, $user_ids_2));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        $startNum = $totalCount - ($page - 1) * $limit;
        foreach ($dataResult as $key => $value) {
            // 操作人用户名
            if (!empty($userList[$value['operator']])) {
                $dataResult[$key]['operator'] = CommentServer::displayUserName($userList[$value['operator']]);
            } else {
                $dataResult[$key]['operator'] = '';
            }

            // 复核人用户名
            if (!empty($userList[$value['reviewer']])) {
                $dataResult[$key]['reviewer'] = CommentServer::displayUserName($userList[$value['reviewer']]);
            } else {
                $dataResult[$key]['reviewer'] = '';
            }

            $dataResult[$key]['orderNumber'] = $startNum;
            $startNum--;

            // 导出展示复核结论
            if ($forExport) {
                if ($value['review_conclusion'] == "1") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'allow');
                } else if ($value['review_conclusion'] == "2") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'refuse');
                }
                if ($value['if_void'] == '0') {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'normal');
                } else {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'cancellation');
                }
            }
        }

        return $this->success([
            'keyResult' => $keyResult,//自定义字段的名字和所有字段的显示顺序
            'dataResult' => $dataResult,//仪器库的维保记录数据
            'totalCount' => $totalCount //数据记录
        ]);
    }

    /**
     * Notes:保存新增的维修记录 2021/4/14 增加编辑维修记录
     * Author: hkk
     * Date: 2020/7/20 9:06
     * @param $recordData
     * @return array
     */
    public function saveRepairRecord($recordData)
    {

        // 1 和当前其他的维修记录时间冲突(读取设置)
        $instrumentInfo = InstrumentsModel::find()->select('name,batch_number,status,check_situation,start_expiry_time,end_expiry_time,record_setting')->where([
            'id' => $recordData['instrumentId'],
        ])->asArray()->one();

        if (!empty($instrumentInfo['record_setting'])) {
            $recordSetting = json_decode($instrumentInfo['record_setting'], true);
            if (!empty($recordSetting['time_operate']) && $recordSetting['time_operate'] == 'true') {

                $currentTime = strtotime($recordData['repairRecordData']['operation_time']);
                $startTime = strtotime($recordData['repairRecordData']['repair_start_time'] . ':59');
                if ($currentTime && ($startTime < $currentTime)) {
                    return $this->fail(\Yii::t('base', 'time_only_after_operate'));
                }
            }
        }

        //校验时间是否冲突
        $currentTimeInfo = [];
        $currentTimeInfo['deal_type'] = 'repair';
        $currentTimeInfo['current_id'] = $recordData['runningId'];
        $currentTimeInfo['start_time'] = $recordData['repairRecordData']['repair_start_time'];
        $currentTimeInfo['end_time'] = $recordData['repairRecordData']['repair_end_time'];
        $conflictInfo = $this->checkTimeConflict($recordData['instrumentId'], $instrumentInfo['record_setting'], $currentTimeInfo);
        if ($conflictInfo !== '') {
            return $this->fail('timeConflict', $conflictInfo);
        }

        //查看本次保存的数据 要求的数据是否完整
        $ifFullInput = !empty($recordData['repairRecordData']['repair_start_time']) && !empty($recordData['repairRecordData']['repair_end_time']) && !empty($recordData['repairRecordData']['repair_content']) && !empty($recordData['repairRecordData']['repair_person']);
        if ($recordData['repairRecordData']['repair_type'] == '1') {
            $ifFullInput = $ifFullInput && !empty($recordData['repairRecordData']['repair_result']);
        }
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        if ($recordData['type'] === 'addRepairRecord') { // 新增或者结束前编辑
            $recordData['repairRecordData']['operator'] = $recordData['operator']; // 自动填创建人 operator
            $saveRecordData = ['instrument_id' => $recordData['instrumentId']];
            if ($ifFullInput) { //如果数据完整，算作结束
                $recordData['repairRecordData']['operator_end'] = $recordData['repairRecordData']['operator'];
                $recordData['repairRecordData']['operation_time_end'] = $recordData['repairRecordData']['operation_time'];
                $recordData['repairRecordData']['if_end'] = 1;
            }
            foreach ($recordData['repairRecordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
            }
            if (!empty($recordData['checkIds']) && $ifFullInput) { // add by hkk 2022/6/14  更改复核状态为等待复核,新增发送复核记录
                $saveRecordData['review_conclusion'] = '3';    // “ 2022-3-8 10:20:30 hkk 新增记录，已提交待复核”
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'add_repair_record') . ', ' . \Yii::t('base', 'add_check_info');
                $saveRecordData['review_record'] = $recordString;
            }
            $ins = new InstrumentRepairRecordModel();
            $ins->setAttributes($saveRecordData);
            if (!$ins->save()) {
                $transaction->rollBack();
                return $this->fail('fail to save repair record');
            }

            // 开了复核要清空现有复核并发送复核消息
            if (!empty($recordData['checkIds']) && $ifFullInput) {

                // 发送消息给复核人
                $approvalServer = new ApprovalServer();
                $checkNode = [
                    ["approval_user_ids" => explode(",", $recordData['checkIds'])]
                ];
                $extraDataArr = [
                    'instrument_id' => $recordData['instrumentId'],
                    'statusNormal' => $recordData['statusNormal'], // 'true'  是否修改仪器状态
                    'recordType' => 'repair_record', // 记录类型，消息邮件用于区分纪录类型
                    'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                    'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                    'instrument_status' => ''
                ];
                $extraData = json_encode($extraDataArr);

                $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $ins->id, 0, $extraData, $checkNode);
                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                }
            }
            if ($recordData['statusNormal'] == 'true' && !$ifFullInput) { //如果必要信息没有填写完整且状态为true 是将仪器改为维修中
                InstrumentsModel::updateAll([
                    'status' => 0,
                ], [
                    'id' => $recordData['instrumentId']
                ]);
            }

            // 没有复核且打勾更改仪器状态，维修过后直接更改仪器表的状态
            if (empty($recordData['checkIds']) && $recordData['statusNormal'] == 'true' && $ifFullInput) { //如果必要信息填写完整且状态为true 将仪器改为正常
                InstrumentsModel::updateAll([
                    'status' => 1,
                ], [
                    'id' => $recordData['instrumentId']
                ]);
            }
        } else { // add by hkk 2021/4/14  增加编辑记录确认 结束前编辑和结束后编辑

            $saveRecordData = []; // 构造需要更新的数据
            $fieldNameArray = []; // 记录字段名称数组用于痕迹获取
            $keyResult = $this->getRecordField($recordData['instrumentId'], 1);
            foreach ($recordData['repairRecordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
                $fieldNameArray[$key] = $keyResult[$key];
            }

            // 查询现有的记录数据做痕迹比对,添加痕迹
            $action_details = '';
            $oldRecordData = InstrumentRepairRecordModel::findOne([
                'id' => $recordData['runningId'],
            ])->toArray();

            $recordTypeMap = [
                '1' => \Yii::t('base', 'repair'),
                '2' => \Yii::t('base', 'Maintenance')
            ];
            // 根据一起id和记录id计算记录的序号
            foreach ($saveRecordData as $key => $data) {
                if ($oldRecordData[$key] != $data) {
                    if ($key == 'repair_type') {
                        $action_details .= ($fieldNameArray[$key] . ":" . $recordTypeMap[$oldRecordData[$key]] . "=>" . $recordTypeMap[$data] . '</br>');
                        continue;
                    }
                    if ($key !== 'file') { // 上传文件后面单独处理
                        $action_details .= ($fieldNameArray[$key] . ":" . $oldRecordData[$key] . "=>" . $data . '</br>');
                    }
                }
            }
            if ($action_details != '') {
                $action_details = \Yii::t('base', 'record_number') . ":" . $recordData['orderNumber'] . '</br>' . $action_details;
                $this->addInstrumentHistory($recordData['instrumentId'], self::ACTION_EDIT_REPAIR_RECORD, ['action_details' => $action_details, 'orderNumber' => $recordData['orderNumber']]);
            }

            if ($ifFullInput && $recordData['type'] === 'editBeforeEndRepairRecord' && $action_details != '') { //结束前编辑 如果数据完整，算作结束
                $saveRecordData['operator_end'] = $recordData['operator'];//自动填充结束人
                $saveRecordData['operation_time_end'] = date("Y-m-d H:i:s"); // 自动填充结束时间
                $saveRecordData['if_end'] = 1;
            }

            if ($ifFullInput && $recordData['type'] === 'editAfterEndRepairRecord' && $action_details != '') { //结束后编辑 数据必然完整
                $saveRecordData['edit_after_end'] = 1;
            }

            // 开了复核 且数据完备 要清空现有复核并发送复核消息
            if (!empty($recordData['checkIds']) && $ifFullInput) {

                // 发送消息给复核人
                $approvalServer = new ApprovalServer();
                $checkNode = [
                    ["approval_user_ids" => explode(",", $recordData['checkIds'])]
                ];
                $extraDataArr = [
                    'instrument_id' => $recordData['instrumentId'],
                    'statusNormal' => $recordData['statusNormal'], // 'true'  是否修改仪器状态
                    'recordType' => 'repair_record', // 记录类型，消息邮件用于区分纪录类型
                    'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                    'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                ];
                $extraData = json_encode($extraDataArr);
                $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $recordData['runningId'], 0, $extraData, $checkNode);
                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                }

                // add by hkk 2022/6/14  更改复核状态为等待复核 添加复核记录
                $saveRecordData['review_conclusion'] = '3';
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'edit_repair_record') . ', ' . \Yii::t('base', 'add_check_info');
                if (!empty($oldRecordData['review_record'])) {
                    $recordString = $recordString . ';;' . $oldRecordData['review_record'];
                }
                $saveRecordData['review_record'] = $recordString;
            }

            // 开始更新
            InstrumentRepairRecordModel::updateAll(
                $saveRecordData,
                ['id' => $recordData['runningId']]
            );

        }

        if ($recordData['statusNormal'] == 'true' && !$ifFullInput) { //如果必要信息没有填写完整且状态为true 是将仪器改为维修中
            InstrumentsModel::updateAll([
                'status' => 3,
            ], [
                'id' => $recordData['instrumentId']
            ]);
        }
        // 没有复核且打勾更改仪器状态，维修过后直接更改仪器表的状态
        if (empty($recordData['checkIds']) && $recordData['statusNormal'] == 'true' && $ifFullInput) { //如果必要信息填写完整且状态为true 将仪器改为正常
            InstrumentsModel::updateAll([
                'status' => 1,
            ], [
                'id' => $recordData['instrumentId']
            ]);
        }


        $transaction->commit();
        $this->updateInstrumentsTimeRecord($recordData['instrumentId']);

        return $this->success([]);
    }

    /**
     * Notes:维修记录新增自定义列
     * Author: hkk
     * Date: 2020/7/20 9:06
     * @param $colData
     * @return array
     */
    public function addRepairRecordColumn($colData)
    {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $instrumentId = $colData['instrumentId'];

        $query = InstrumentRepairRecordExtendFieldModel::find()
            ->where(['instrument_id' => $instrumentId])
            ->one();

        if (!empty($query)) {
            $query->{'extend_field_' . $colData['field']} = \Yii::t('base', 'define_title');
            $query->save();
        } else {
            $InstrumentRepairRecordExtendField = new InstrumentRepairRecordExtendFieldModel();
            $InstrumentRepairRecordExtendField->{'extend_field_' . $colData['field']} = \Yii::t('base', 'define_title');
            $InstrumentRepairRecordExtendField->instrument_id = $instrumentId;
            $InstrumentRepairRecordExtendField->save();
        }

        $transaction->commit();
        $this->addInstrumentHistory($instrumentId, self::Action_ADJUST_COLUMN, ['action_details' => \Yii::t('base', 'repair_record') . ': ' . \Yii::t('base', 'add_column')]);

        return $this->success([]);
    }

    /**
     * Notes: 批量复核操作 已弃用
     * Author: hkk
     * Date: 2020/7/21 17:11
     * @param $data
     * @return array
     */
    public function checkRepairRecord($data)
    {

        InstrumentDefineTableData::updateAll(
            [
                'data47' => $data['userId'], // 复核人
                'data48' => date("Y-m-d H:i:s"), // 复核时间
                'data49' => $data['checkResult'], // 复核结论
                'data50' => $data['checkReason'], // 复核原因
            ],
            ['id' => $data['chooseIds']]
        );

        return $this->success([]);
    }

    /**
     * Notes: 运行记录页面
     * Author: hkk
     * Date: 2020/7/22 13:19
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listInsOperateRecordView($where, $limit, $page, $forExport = false)
    {

        $keyResult = $this->getRecordField($where['instrument_id'], 3);

        $operateStatus = InstrumentsModel::find()->select('running_id')->where([
            'id' => $where['instrument_id'],
        ])->asArray()->one();

        $query = InstrumentRunningRecordModel::find()->from(InstrumentRunningRecordModel::tableName() . ' IRR');
        $query->andWhere(['IRR.instrument_id' => $where['instrument_id']]);

        // 根据选中的记录id筛选 jiangdm 2022/8/9
        if (!empty($where['chooseIds'])) {
            $query->andWhere(['id' => $where['chooseIds']]);
        }
        $query->asArray();
        $totalCount = $query->count();
        $query->orderBy('id desc');

        // $dataResult = $query->offset(($page - 1) * $limit)->limit($limit)->all();

        if (!$forExport) {
            $dataResult = $query->offset(($page - 1) * $limit)->limit($limit)->all();
        } else { // 导出全部不分页
            $dataResult = $query->all();
        }

        //根据创建者ID获取用户姓名 DATA2 => 操作人列   DATA47 => 复核人列
        $user_ids_1 = array_column($dataResult, 'start_operator');
        $user_ids_3 = array_column($dataResult, 'end_operator'); // 结束操作人
        $user_ids = array_unique(array_merge($user_ids_1, $user_ids_3));
        //  $user_ids = array_unique(array_column($dataResult, 'data2'));
        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        $startNum = $totalCount - ($page - 1) * $limit;
        foreach ($dataResult as $key => $value) {

            // 开始操作人用户名
            if (!empty($userList[$value['start_operator']])) {
                $dataResult[$key]['start_operator'] = CommentServer::displayUserName($userList[$value['start_operator']]);
            } else {
                $dataResult[$key]['start_operator'] = '';
            }

            // 结束操作人用户名
            if (!empty($userList[$value['end_operator']])) {
                $dataResult[$key]['end_operator'] = CommentServer::displayUserName($userList[$value['end_operator']]);
            } else {
                $dataResult[$key]['end_operator'] = '';
            }

            $dataResult[$key]['orderNumber'] = $startNum;
            $startNum--;
            // 导出展示复核结论
            if ($forExport) {
                if ($value['review_conclusion'] == "1") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'allow');
                } else if ($value['review_conclusion'] == "2") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'refuse');
                }
                if ($value['if_void'] == '0') {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'normal');
                } else {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'cancellation');
                }
            }
        }

        return $this->success([
            'keyResult' => $keyResult,
            'dataResult' => $dataResult,
            'totalCount' => $totalCount,
            'operateStatus' => $operateStatus['running_id'],
        ]);
    }

    /**
     * Notes:保存新增的开始运行记录 编辑或结束
     * Author: hkk
     * Date: 2020/7/22 9:06
     * @param $recordData
     * @return array
     */
    public function saveOperateRecord($recordData)
    {

        // 1.状态和校验有效期条件冲突判断
        $instrumentInfo = InstrumentsModel::find()
            ->select('name,batch_number,status,check_situation,responsible_person,in_charge_person,create_by,start_expiry_time,end_expiry_time,record_setting')
            ->where([
                'id' => $recordData['instrumentId'],
            ])->asArray()->one();
        if ($instrumentInfo['status'] != 1) { // 状态非正常无法运行提示
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip1'));
        }
        if ($instrumentInfo['check_situation'] == 2) { // 未校验提示
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip2'));
        }

        if ($instrumentInfo['check_situation'] == 1) { // 过校验有效期提示
            $operateStartTime = !empty($recordData['operateRecordData']['start_running_time']) ? strtotime($recordData['operateRecordData']['start_running_time']) : null;
            $operateEndTime = !empty($recordData['operateRecordData']['end_running_time']) ? strtotime($recordData['operateRecordData']['end_running_time']) : null;
            $returnInstrId = $this->ifInExpiryTime(array($recordData['instrumentId']), $operateStartTime, $operateEndTime);
            if (empty($returnInstrId)) { //超过有效期提醒
                return $this->fail(\Yii::t('base', 'instrument_conflict_tip2'));
            }
        }

        // 1.5 编辑或结束运行人员限制判定  add by hkk 2022/6/28 开始运行人，维护人，创建人，责任人才能操作
        $oldRecordData = [];
        if ($recordData['type'] !== 'begin') {
            $oldRecordData = InstrumentRunningRecordModel::findOne([
                'id' => $recordData['runningId'],
            ])->toArray();

            $currentUser = $recordData['create_by'];
            $beginUser = $oldRecordData['start_operator'];
            $createUser = $instrumentInfo['create_by'];
            $responsibleUserIds = explode(',', $instrumentInfo['responsible_person']);
            $inChargeUserIds = explode(',', $instrumentInfo['in_charge_person']);

            if (($currentUser != $beginUser) && ($currentUser != $createUser)
                && !in_array($currentUser, $responsibleUserIds)
                && !in_array($currentUser, $inChargeUserIds)
            ) {
                return $this->fail(\Yii::t('base', 'instrument_conflict_tip5'));
            }
        }

        // 2 和当前其他的运行记录时间冲突(读取设置) changeBy prd 2022/12/30
        if (!empty($instrumentInfo['record_setting'])) {
            $recordSetting = json_decode($instrumentInfo['record_setting'], true);
            // add by hkk 2022/6/1 仅限操作时间后（当前时间）
            if (!empty($recordSetting['time_operate']) && $recordSetting['time_operate'] == 'true') {
                $currentTime = strtotime($recordData['operateRecordData']['start_operator_time']);
                $startTime = strtotime($recordData['operateRecordData']['start_running_time'] . ':59'); //开始时间精确到分钟，最大处理再比较
                if ($currentTime && ($startTime < $currentTime)) {
                    return $this->fail(\Yii::t('base', 'time_only_after_operate'));
                }
            }
        }

        //校验时间是否冲突
        $currentTimeInfo = [];
        $currentTimeInfo['deal_type'] = 'operate';
        $currentTimeInfo['current_id'] = $recordData['runningId'];
        $currentTimeInfo['start_time'] = $recordData['operateRecordData']['start_running_time'];
        $currentTimeInfo['end_time'] = $recordData['operateRecordData']['end_running_time'];
        $conflictInfo = $this->checkTimeConflict($recordData['instrumentId'], $instrumentInfo['record_setting'], $currentTimeInfo);
        if ($conflictInfo !== '') {
            return $this->fail('timeConflict', $conflictInfo);
        }

        // 5 开始修改
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $keyResult = $this->getRecordField($recordData['instrumentId'], 3);
        if ($recordData['type'] === 'begin') { // 开始运行确认 是新增
            $recordData['operateRecordData']['start_operator'] = $recordData['create_by']; // 自动填充开始操作人
            $saveRecordData = ['instrument_id' => $recordData['instrumentId'],];
            foreach ($recordData['operateRecordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
            }
            $ins = new InstrumentRunningRecordModel();
            $ins->setAttributes($saveRecordData);

            if (!$ins->save()) {
                $transaction->rollBack();
                return $this->fail('fail to add operate record');
            }

            // 开始运行要更改仪器表的运行状态
            InstrumentsModel::updateAll(
                ['running_id' => $ins['id']],
                ['id' => $recordData['instrumentId']]
            );
            $runningId = $ins['id']; // add by hkk 2022/6/17  用于实验页面新增后返回运行id

        } else if ($recordData['type'] === 'edit') { // add by hkk 2021/4/14  增加编辑记录确认

            $saveRecordData = []; // 构造需要更新的数据
            $fieldNameArray = []; // 记录字段名称数组用于痕迹获取
            foreach ($recordData['operateRecordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
                $fieldNameArray[$key] = $keyResult[$key];
            }

            // 查询现有的记录数据做痕迹比对,添加痕迹
            $action_details = '';
            foreach ($saveRecordData as $key => $data) {
                if ($oldRecordData[$key] != $data) {
                    if ($key !== 'file') { // 上传文件后面单独处理
                        $action_details .= ($fieldNameArray[$key] . ":" . $oldRecordData[$key] . "=>" . $data . '</br>');
                    }
                }
            }

            if ($action_details != '') {
                $action_details = \Yii::t('base', 'record_number') . ":" . $recordData['orderNumber'] . '</br>' . $action_details;
                $this->addInstrumentHistory($recordData['instrumentId'], self::ACTION_EDIT_OPERATE_RECORD, ['action_details' => $action_details, 'orderNumber' => $recordData['orderNumber']]);
            }


            //查询之前的数据，看是否是结束后再编辑
            $operateRecord = InstrumentRunningRecordModel:: find()->where(['id' => $recordData['runningId']])->asArray()->one();
            if (empty($operateRecord['end_operator']) && empty($operateRecord['end_operator_time'])) { //如果都是空，代表还没有结束
                $saveRecordData['edit_after_end'] = 0;
            } else if ($action_details != '') { //结束且有编辑
                $saveRecordData['edit_after_end'] = 1;
            }

            // 开了复核要清空现有复核并发送复核消息

            if (!empty($recordData['checkIds']) && $oldRecordData['if_end'] == 1) { //add by wh 有复核设置有改动且运行已经结束才会发送复核

                // 发送消息给复核人
                $approvalServer = new ApprovalServer();
                $checkNode = [
                    ["approval_user_ids" => explode(",", $recordData['checkIds'])]
                ];

                $extraDataArr = [
                    'instrument_id' => $recordData['instrumentId'],
                    'recordType' => 'operate_record',
                    'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                    'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                ];

                $extraData = json_encode($extraDataArr);
                $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $recordData['runningId'], 0, $extraData, $checkNode);
                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                }

                // add by hkk 2022/6/14  更改复核状态为等待复核 添加复核记录
                $saveRecordData['review_conclusion'] = '3';
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'edit_operate_record') . ', ' . \Yii::t('base', 'add_check_info');
                if (!empty($oldRecordData['review_record'])) {
                    $recordString = $recordString . ';;' . $oldRecordData['review_record'];
                }
                $saveRecordData['review_record'] = $recordString;
            }


            // 开始更新
            InstrumentRunningRecordModel::updateAll(
                $saveRecordData,
                ['id' => $recordData['runningId']]
            );


            $runningId = $recordData['runningId'];
        } else { // 结束运行确认 是修改覆盖  // add by hkk 2021/4/14  增加编辑记录确认

            $recordData['operateRecordData']['end_operator_time'] = date("Y-m-d H:i:s"); // 自动填充结束操作时间
            $recordData['operateRecordData']['end_operator'] = $recordData['create_by'];  // 自动填充结束操作人

            $saveRecordData = []; // 构造更新的数据
            foreach ($recordData['operateRecordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
            }

            // 开了复核要清空现有复核并发送复核消息
            if (!empty($recordData['checkIds'])) {

                // 发送消息给复核人
                $toCheckIds = explode(",", $recordData['checkIds']);
                $approvalServer = new ApprovalServer();
                $checkNode = [
                    ["approval_user_ids" => $toCheckIds]
                ];

                $extraDataArr = [
                    'instrument_id' => $recordData['instrumentId'],
                    'recordType' => 'operate_record',
                    'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                    'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                ];
                $extraData = json_encode($extraDataArr);
                $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $recordData['runningId'], 0, $extraData, $checkNode);
                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                }

                // add by hkk 2022/6/14  更改复核状态为等待复核 添加复核记录
                $saveRecordData['review_conclusion'] = '3'; // 1 复核通过 2 复核拒绝 3 等待复核
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'end_running') . ', ' . \Yii::t('base', 'add_check_info');
                if (!empty($oldRecordData['review_record'])) {
                    $recordString = $recordString . ';;' . $oldRecordData['review_record'];
                }
                $saveRecordData['review_record'] = $recordString;
            }

            $saveRecordData['if_end'] = '1'; // 0->未结束 1->已结束 老数据需要跑脚本改状态

            InstrumentRunningRecordModel::updateAll( // 开始更新
                $saveRecordData,
                ['id' => $recordData['runningId']]
            );

            $runningId = $recordData['runningId'];
        }


        $transaction->commit();
        $this->updateInstrumentsTimeRecord($recordData['instrumentId']);
        return $this->success(['running_id' => $runningId]);
    }

    /**
     * Notes:获取结束运行时所需要的数据  获取校验、运行、维保三大记录数据的公用函数
     * Author: hkk
     * Date: 2020/7/22 9:06
     * @param $runningId '数据库中的记录id'
     * @param $type 1 维保  2校验  3 运行
     * @return array
     */
    public function getOperateDataById($runningId, $type)
    {
        $dataResult = [];
        switch ($type) {
            case '1' :
                $dataResult = InstrumentRepairRecordModel::find()->where([
                    'id' => $runningId,
                ])->asArray()->one();
                break;
            case '2':
                $dataResult = InstrumentCheckRecordModel::find()->where([
                    'id' => $runningId,
                ])->asArray()->one();
                break;
            case '3':
                $dataResult = InstrumentRunningRecordModel::find()->where([
                    'id' => $runningId,
                ])->asArray()->one();
                break;
            default:
                break;
        }

        return $this->success($dataResult);
    }

    /**
     * Notes:运行记录新增自定义列
     * Author: hkk
     * Date: 2020/7/20 9:06
     * @param $colData
     * @return array
     */
    public function addOperateRecordColumn($colData)
    {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $instrumentRunningRecordExtendFieldData = InstrumentRunningRecordExtendFieldModel::findOne(['instrument_id' => $colData['instrumentId']]);
        if (empty($instrumentRunningRecordExtendFieldData)) {
            $instrumentRunningRecordExtendFieldData = new InstrumentRunningRecordExtendFieldModel();
        }

        $instrumentRunningRecordExtendFieldData->setAttributes([
            'extend_field_' . $colData['field'] => \Yii::t('base', 'define_title'),
            'instrument_id' => $colData['instrumentId'],
        ]);

        if (!$instrumentRunningRecordExtendFieldData->save()) {
            \Yii::info($instrumentRunningRecordExtendFieldData->getFirstErrors());
        }
        $transaction->commit();
        $this->addInstrumentHistory($colData['instrumentId'], self::Action_ADJUST_COLUMN, ['action_details' => \Yii::t('base', 'operate_record') . ': ' . \Yii::t('base', 'add_column')]);
        return $this->success([]);
    }

    /**
     * Notes: 校验记录页面
     * Author: hkk
     * Date: 2020/7/22 13:19
     * @param $where
     * @param $limit
     * @param $page
     * @return mixed
     */
    public function listInsCheckRecordView($where, $limit, $page, $forExport = false)
    {

        //获取字段名称及顺序
        $keyResult = $this->getRecordField($where['instrument_id'], 2);
        //获取字段数据
        $query = InstrumentCheckRecordModel::find()
            ->where([
                'instrument_id' => $where['instrument_id'],
                'status' => 1
            ])
            ->orderBy('id desc');
        $totalCount = $query->asArray()->count();
        if (!$forExport) {
            $dataResult = $query->offset(($page - 1) * $limit)->limit($limit)->all();
        } else { // 导出全部不分页
            $dataResult = $query->all();
        }

        //根据创建者ID获取用户姓  操作人列   复核人列
        $user_ids_1 = array_column($dataResult, 'operator');
        $user_ids_2 = array_column($dataResult, 'reviewer');
        $user_ids = array_unique(array_merge($user_ids_1, $user_ids_2));

        $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
        $userList = ArrayHelper::index($userList, 'user_id');
        $startNum = $totalCount - ($page - 1) * $limit;
        foreach ($dataResult as $key => $value) {

            // 操作人用户名
            if (!empty($userList[$value['operator']])) {
                $dataResult[$key]['operator'] = CommentServer::displayUserName($userList[$value['operator']]);
            } else {
                $dataResult[$key]['operator'] = '';
            }

            // 复核人用户名
            if (!empty($userList[$value['reviewer']])) {
                $dataResult[$key]['reviewer'] = CommentServer::displayUserName($userList[$value['data47']]);
            } else {
                $dataResult[$key]['reviewer'] = '';
            }
            $dataResult[$key]['orderNumber'] = $startNum;
            $startNum--;
            // 导出展示复核结论
            if ($forExport) {
                if ($value['review_conclusion'] == "1") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'allow');
                } else if ($value['review_conclusion'] == "2") {
                    $dataResult[$key]['review_conclusion'] = \Yii::t('base', 'refuse');
                }
                // 开始有效期合并
                $dataResult[$key]['start_end_expiry_time'] = $value['start_expiry_time'] . ' -- ' . $value['end_expiry_time'];
                if ($value['if_void'] == '0') {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'normal');
                } else {
                    $dataResult[$key]['if_void'] = \Yii::t('base', 'cancellation');
                }
            }
        }

        return $this->success([
            'keyResult' => $keyResult,
            'dataResult' => $dataResult,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * 维保记录/校验记录/运行记录页面渲染中文件模块
     * @param $file
     * @return string
     */
    public function fileModel($file)
    {
        if (empty($file)) {
            return '';
        }
        $trans = \Yii::t('base', 'download');
        return <<<FILE
           <div class="single_detail_file">
                <span class="ref_file_part file_up_box">
                    <span class="file_name" style="width: 80px;max-width: 100px" title="{$file['real_name']}">{$file['real_name']}</span>
                         <a class="download_ico" target="_blank" title="{$trans}" href="?r=download/file&path={$file['dep_path']}&name={$file['save_name']}&file_name={$file['real_name']}"></a>
                </span>
           </div>
FILE;
    }

    /**
     * Notes:保存新增的校验记录 2021/4/14 增加编辑维修记录
     * Author: hkk
     * Date: 2020/7/22 9:06
     * @param $recordData
     * @return array
     */
    public function saveCheckRecord($recordData)
    {

        // 和状态冲突设置 add by hkk 2021/5/25
        $instrumentInfo = InstrumentsModel::find()->select('name,batch_number,status,check_situation,start_expiry_time,end_expiry_time,record_setting')->where([
            'id' => $recordData['instrumentId'],
        ])->asArray()->one();
        if ($instrumentInfo['status'] == 0 || $instrumentInfo['status'] == 3 || $instrumentInfo['status'] == 4) { // 删除，维修中和报废无法提交校验
            return $this->fail(\Yii::t('base', 'instrument_conflict_tip1'));
        }

        if (!empty($instrumentInfo['record_setting'])) {
            $recordSetting = json_decode($instrumentInfo['record_setting'], true);
            if (!empty($recordSetting['time_operate']) && $recordSetting['time_operate'] == 'true' && isset($recordData['recordData']['operation_time'])) {
                $currentTime = strtotime($recordData['recordData']['operation_time']);
                $startTime = strtotime($recordData['recordData']['start_check_time'] . ':59');
                if ($currentTime && ($startTime < $currentTime)) {
                    return $this->fail(\Yii::t('base', 'time_only_after_operate'));
                }
            }
        }

        //校验时间是否冲突
        $currentTimeInfo = [];
        $currentTimeInfo['deal_type'] = 'check';
        $currentTimeInfo['current_id'] = $recordData['runningId'];
        $currentTimeInfo['start_time'] = $recordData['recordData']['start_check_time'];
        $currentTimeInfo['end_time'] = $recordData['recordData']['end_check_time'];
        $conflictInfo = $this->checkTimeConflict($recordData['instrumentId'], $instrumentInfo['record_setting'], $currentTimeInfo);
        if ($conflictInfo !== '') {
            return $this->fail('timeConflict', $conflictInfo);
        }

        //查看本次保存的数据 要求的数据是否完整
        $ifFullInput = !empty($recordData['recordData']['check_person']) && !empty($recordData['recordData']['start_check_time']) && !empty($recordData['recordData']['end_check_time']) && !empty($recordData['recordData']['start_expiry_time']) && !empty($recordData['recordData']['end_expiry_time']);

        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        if ($recordData['type'] === 'addCheckRecord') { // 新增
            $recordData['recordData']['operator'] = $recordData['operator']; // 自动填创建人
            $saveRecordData = ['instrument_id' => $recordData['instrumentId']];
            if ($ifFullInput) { //如果数据完整，算作结束
                $recordData['recordData']['operator_end'] = $recordData['recordData']['operator'];
                $recordData['recordData']['operation_time_end'] = $recordData['recordData']['operation_time'];
                $recordData['recordData']['if_end'] = 1;
            }
            foreach ($recordData['recordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
            }

            if (!empty($recordData['checkIds']) && $ifFullInput) { // add by hkk 2022/6/14  更改复核状态为等待复核,新增发送复核记录
                $saveRecordData['review_conclusion'] = '3';    // “ 2022-3-8 10:20:30 hkk 新增记录，已提交待复核”
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'add_check') . ', ' . \Yii::t('base', 'add_check_info');
                $saveRecordData['review_record'] = $recordString;
            }
            unset($saveRecordData['start_end_expiry_time']);

            $ins = new InstrumentCheckRecordModel();
            $ins->setAttributes(($saveRecordData));
            if (!$ins->save()) {
                $transaction->rollBack();
                return $this->fail('fail to save repair record');
            }

            // 复核并发送复核消息
            if ($ifFullInput) {
                if (!empty($recordData['checkIds'])) {
                    // 发送消息给复核人
                    $approvalServer = new ApprovalServer();
                    $checkNode = [
                        ["approval_user_ids" => explode(",", $recordData['checkIds'])]
                    ];

                    $extraDataArr = [
                        'instrument_id' => $recordData['instrumentId'],
                        'start_expiry_time' => $saveRecordData['start_expiry_time'], // 校验有效期
                        'end_expiry_time' => $saveRecordData['end_expiry_time'], // 校验有效期
                        'recordType' => 'check_record',
                        'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                        'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                        'instrument_status' => ''
                    ];
                    $extraData = json_encode($extraDataArr);

                    $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $ins->id, 0, $extraData, $checkNode);
                    if (empty($createApprovalRes['status'])) {
                        return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                    }
                } else {

                    // 新增校验过后直接更改仪器表的校验有效期 //有效期功能更新写在下面
                    InstrumentsModel::updateAll([
                        'check_situation' => 1,
                        'start_expiry_time' => $saveRecordData['start_expiry_time'],
                        'end_expiry_time' => $saveRecordData['end_expiry_time'],
                    ], [
                        'id' => $recordData['instrumentId']
                    ]);
                }

            }

        } else { // add by hkk 2021/4/14  增加编辑校验记录确认 结束前编辑校验 + 结束后编辑校验

            $saveRecordData = []; // 构造需要更新的数据
            $fieldNameArray = []; // 记录字段名称数组用于痕迹获取
            $keyResult = $this->getRecordField($recordData['instrumentId'], 2);
            unset($keyResult['start_end_expiry_time']);
            unset($recordData['recordData']['start_end_expiry_time']);
            $keyResult2 = ['start_expiry_time' => '', 'end_expiry_time' => ''];
            $keyResult = array_merge($keyResult, $keyResult2);
            foreach ($recordData['recordData'] as $key => $item) {
                $saveRecordData[$key] = $item;
                $fieldNameArray[$key] = $keyResult[$key];
            }

            // 查询现有的记录数据做痕迹比对,添加痕迹
            $action_details = '';
            $oldRecordData = InstrumentCheckRecordModel::findOne([
                'id' => $recordData['runningId'],
            ])->toArray();

            // 根据一起id和记录id计算记录的序号
            foreach ($saveRecordData as $key => $data) {
                if ($oldRecordData[$key] != $data) {
                    if ($key !== 'file') { // 上传文件后面单独处理
                        $action_details .= ($fieldNameArray[$key] . ":" . $oldRecordData[$key] . "=>" . $data . '</br>');
                    }
                }
            }
            if ($action_details != '') {
                $action_details = \Yii::t('base', 'record_number') . ":" . $recordData['orderNumber'] . '</br>' . $action_details;
                $this->addInstrumentHistory($recordData['instrumentId'], self::ACTION_EDIT_CHECK_RECORD, ['action_details' => $action_details, 'orderNumber' => $recordData['orderNumber']]);
            }

            if ($ifFullInput && $recordData['type'] === 'editBeforeEndCheckRecord' && $action_details != '') { //结束前编辑 如果数据完整，算作结束
                $saveRecordData['operator_end'] = $recordData['operator'];//自动填充结束人
                $saveRecordData['operation_time_end'] = date("Y-m-d H:i:s"); // 自动填充结束时间
                $saveRecordData['if_end'] = 1;
            }
            if ($ifFullInput && $recordData['type'] === 'editAfterEndCheckRecord' && $action_details != '') { //结束后编辑 数据必然完整
                $saveRecordData['edit_after_end'] = 1;
            }

            // 开了复核要清空现有复核并发送复核消息
            if (!empty($recordData['checkIds']) && $ifFullInput) {
                // 发送消息给复核人
                $approvalServer = new ApprovalServer();
                $checkNode = [
                    ["approval_user_ids" => explode(",", $recordData['checkIds'])]
                ];
                $extraDataArr = [
                    'instrument_id' => $recordData['instrumentId'],
                    'start_expiry_time' => $saveRecordData['start_expiry_time'], // 校验有效期
                    'end_expiry_time' => $saveRecordData['end_expiry_time'], // 校验有效期
                    'recordType' => 'check_record',
                    'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
                    'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
                ];
                $extraData = json_encode($extraDataArr);
                $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $recordData['runningId'], 0, $extraData, $checkNode);
                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], ['type' => 'popContent']);
                }

                // add by hkk 2022/6/14  更改复核状态为等待复核 添加复核记录
                $saveRecordData['review_conclusion'] = '3';
                $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
                $userName = CommonServer::displayUserName($userInfo);
                $recordString = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'edit_check') . ', ' . \Yii::t('base', 'add_check_info');
                if (!empty($oldRecordData['review_record'])) {
                    $recordString = $recordString . ';;' . $oldRecordData['review_record'];
                }
                $saveRecordData['review_record'] = $recordString;
            }
            if (empty($recordData['checkIds']) && $ifFullInput) {
                // 编辑校验过后直接更改仪器表的校验有效期
                InstrumentsModel::updateAll([
                    'check_situation' => 1,
                    'start_expiry_time' => $saveRecordData['start_expiry_time'],
                    'end_expiry_time' => $saveRecordData['end_expiry_time'],
                ], [
                    'id' => $recordData['instrumentId']
                ]);
            }
            // 开始更新
            InstrumentCheckRecordModel::updateAll(
                $saveRecordData,
                ['id' => $recordData['runningId']]
            );

        }

        $transaction->commit();
        $this->updateInstrumentsTimeRecord($recordData['instrumentId']);

        return $this->success([]);
    }

    /**
     * Notes: 撤销复核校验
     * Author: hkk
     * Date: 2022/6/14 11:12
     * @param $recordData
     * @return array
     */
    public function undoCheckRecord($recordData)
    {
        $recordType = '';
        // 根据复核状态查询当前是否能够撤销
        if ($recordData['recordType'] == '1') {
            $recordInfo = InstrumentRepairRecordModel::findOne([
                'id' => $recordData['runningId'],
            ]);
            $recordType = 'repair_record';
        }
        if ($recordData['recordType'] == '2') {
            $recordInfo = InstrumentCheckRecordModel::findOne([
                'id' => $recordData['runningId'],
            ]);
            $recordType = 'check_record';
        }
        if ($recordData['recordType'] == '3') {
            $recordInfo = InstrumentRunningRecordModel::findOne([
                'id' => $recordData['runningId'],
            ]);
            $recordType = 'operate_record';
        }

        if (!empty($recordInfo['review_conclusion']) && $recordInfo['review_conclusion'] != '3') {
            return $this->fail(\Yii::t('base', 'undo_check_conflict_tip'));
        }

        // 撤销复核
        $approvalServer = new ApprovalServer();
        $approvalServer->cancelApproval(9, $recordData['runningId'], $recordData['instrumentId'], $recordType);

        // 撤销后改复核状态为, 修改复核记录
        $userInfo = (new CenterInterface())->getUserByUserId(\Yii::$app->view->params['curr_user_id']);
        $userName = CommonServer::displayUserName($userInfo);
        $record = date("Y-m-d H:i:s") . ' ' . $userName . \Yii::t('base', 'undo_check') . '(' . $recordData['undoReason'] . ')';
        if (!empty($recordInfo['review_record'])) {
            $record .= ';;' . $recordInfo['review_record'];
        }
        $recordInfo->setAttributes([
            'review_conclusion' => '2',
            'review_record' => $record,
        ]);
        $recordInfo->save();
        return $this->success([]);
    }

    /**
     * Notes:校验记录新增自定义列
     * Author: hkk
     * Date: 2020/7/20 9:06
     * @param $colData
     * @return array
     */
    public function addCheckRecordColumn($colData)
    {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $instrumentId = $colData['instrumentId'];

        $query = InstrumentCheckRecordExtendFieldModel::findOne(['instrument_id' => $instrumentId]);

        if (!empty($query)) {
            $query->{'extend_field_' . $colData['field']} = \Yii::t('base', 'define_title');
            $query->save();
        } else {
            $InstrumentCheckRecordExtendField = new InstrumentCheckRecordExtendFieldModel();
            $InstrumentCheckRecordExtendField->{'extend_field_' . $colData['field']} = \Yii::t('base', 'define_title');
            $InstrumentCheckRecordExtendField->instrument_id = $instrumentId;
            $InstrumentCheckRecordExtendField->save();
        }

        $transaction->commit();
        $this->addInstrumentHistory($instrumentId, self::Action_ADJUST_COLUMN, ['action_details' => \Yii::t('base', 'check_record') . ': ' . \Yii::t('base', 'add_column')]);
        return $this->success([]);
    }

    /**
     * Notes: 获取仪器数据
     * Author: hkk
     * Date: 2021/4/19 11:28
     * @param $ids
     * @return array
     */
    public function getInstrumentDataByIds($ids)
    {
        $instrumentInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
            ->where(['IM.id' => $ids])
            ->asArray()->all();
        $instrumentInfo = ArrayHelper::index($instrumentInfo, 'id');
        return $this->success($instrumentInfo);
    }

    /**
     * Notes: 获取仪器数记录据
     * Author: hkk
     * Date: 2021/4/19 11:28
     * @param $ids
     * @return array
     */
    public function getInstrumentRecordDataByIds($ids)
    {

        // 根据记录详情ids 查询记录字段表 展示所有非空字段和字段类型
        $instrumentInfo = InstrumentDefineTableKey::find()->from(InstrumentDefineTableKey::tableName() . ' IK')
            ->leftJoin(InstrumentDefineTableData::tableName() . ' AS ID', 'IK.id=ID.instrument_define_table_key_id')
            ->where(['ID.id' => $ids]) // 2 校验记录  3 运行记录 1 维修记录
            ->select(['IK.type', 'ID.*'])->asArray()->all();

        $instrumentInfo = ArrayHelper::index($instrumentInfo, 'id');

        return $this->success($instrumentInfo);
    }

    /**
     * Notes: 查询具体记录的详情
     * Author: hkk
     * Date: 2021/4/19 15:37
     * @param $id
     * @return array
     */
    public function getInstrumentRecordDataById($recordId, $approveId)
    {

        // 根据记录详情id 查询记录字段表 展示所有非空字段和字段类型
        $approveRecord = (new ApprovalServer())->getApprovalInfo($approveId);
        $extraData = json_decode($approveRecord['extra_data'], true);
        switch ($extraData['recordType']) {
            case 'repair_record':
                $model = new InstrumentRepairRecordModel();
                $recordType = 1;
                break;
            case 'operate_record':
                $model = new InstrumentRunningRecordModel();
                $recordType = 3;
                break;
            case 'check_record':
                $model = new InstrumentCheckRecordModel();
                $recordType = 2;
                break;
        }
        $record = $model::find()
            ->where(['id' => $recordId]) // 2 校验记录  3 运行记录 1 维修记录
            ->asArray()->one();


        if ($recordType == '3' && (!empty($record['start_operator']) || !empty($record['end_operator']))) {
            // 运行记录 添加 开始操作人 结束操作人
            $user_ids = array_unique([$record['start_operator'], $record['end_operator']]); //
            $userList = (new CenterInterface())->userDetailsByUserIds($user_ids);
            $userList = ArrayHelper::index($userList, 'user_id');
            $record['start_operator'] = CommentServer::displayUserName($userList[$record['start_operator']]);
            $record['end_operator'] = CommentServer::displayUserName($userList[$record['end_operator']]);
        } elseif ($recordType == '1' && !empty($record['repair_start_time'])) {
            // 维修记录 添加操作人
            $userList = (new CenterInterface())->userDetailsByUserIds([$record['operator']]);
            $userList = ArrayHelper::index($userList, 'user_id');
            $record['operator'] = CommentServer::displayUserName($userList[$record['operator']]);
        } elseif ($recordType == '2' && !empty($record['operator'])) {
            // 校验记录 添加操作人
            $userList = (new CenterInterface())->userDetailsByUserIds([$record['operator']]);
            $userList = ArrayHelper::index($userList, 'user_id');
            $record['operator'] = CommentServer::displayUserName($userList[$record['operator']]);
        }
        if (!empty($record['review_record'])) {
            $reviewRecord = explode(';;', $record['review_record']);
            $record['review_record'] = implode(';\n', $reviewRecord);

        }
        $instrumentInfo['dataResult'] = $record;
        $instrumentInfo['recordType'] = $recordType;
        $instrumentInfo['fieldResult'] = (new InstrumentServer())->getRecordField($record['instrument_id'], $recordType);


        return $this->success($instrumentInfo);
    }

    /**
     * Notes: 查找运行记录，校验记录，维修记录哪些被修改过
     * Author: hkk
     * Date: 2021/4/21 9:32
     * @param $instrumentId
     * @param $type
     * @return array
     */
    public function getInstrumentRecordHistory($instrumentId, $type)
    {

        $list = InstrumentsActionRecordModel::find()
            ->where(['instrument_id' => $instrumentId, 'action' => $type, 'status' => 1]) // 10 11 12
            ->select(['remark'])->distinct()->asArray()->all(); // remark 记录了该记录的序号
        $list = array_column($list, 'remark');
        return $this->success($list);
    }

    /**
     * Notes: 获取仪器运行统计数据
     * Author: hkk
     * Date: 2021/4/23 15:07
     * @param $where
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getInstrumentUseStatistics($where)
    {

        // 1 获取所有的仪器运行记录
        $recordInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
            ->leftJoin(InstrumentRunningRecordModel::tableName() . ' AS ID', ' IM.id=ID.instrument_id')
            ->select([
                'IM.create_time',
                'ID.id',
                'ID.instrument_id',
                'group_concat(ID.operator) as operator',  // 使用人
                'count(DISTINCT ID.operator) as members',  // 使用人数
                'group_concat(TIMESTAMPDIFF(MINUTE,ID.start_running_time,ID.end_running_time)) as minutes' // 使用时间
            ])
            ->andWhere(['<>', 'ID.end_running_time', '']) // 不等于空
            ->andWhere(['not', ['ID.end_running_time' => null]]);
        if (!empty($where['start_time'])) {
            $recordInfo->andWhere(['>=', 'ID.start_running_time', $where['start_time']]);
        }
        if (!empty($where['end_time'])) {
            $recordInfo->andWhere(['<=', 'ID.end_running_time', $where['end_time']]);
        }
        $instrumentRecordInfo = $recordInfo->groupBy('ID.instrument_id')->indexBy('instrument_id')->asArray()->all();


        // 2 查询所有仪器校验次数
        $checkRecordInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
            ->leftJoin(InstrumentCheckRecordModel::tableName() . ' AS ID', ' IM.id=ID.instrument_id')
            ->select([
                'ID.id',
                'ID.instrument_id',
                'count(DISTINCT ID.id) as check_times', // 校验次数
            ])
            ->groupBy('ID.instrument_id')->indexBy('instrument_id')->asArray()->all();

        // 3 查询所有仪器维修次数
        $repairRecordInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
            ->leftJoin(InstrumentRepairRecordModel::tableName() . ' AS ID', ' IM.id=ID.instrument_id')
            ->select([
                'ID.id',
                'ID.instrument_id',
                'count(DISTINCT ID.id) as repair_times', // 维修次数
            ])
            ->groupBy('ID.instrument_id')->indexBy('instrument_id')->asArray()->all();


        // 4 查询所有的仪器并筛选
        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->select[] = 'Ins.id';
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ins.batch_number';
        $query->select[] = 'Ins.create_time'; // 创建时间
        $filter = false;
        if (!empty($where['keywords'])) {
            $query->andFilterWhere(['like', 'Ins.batch_number', $where['keywords']])
                ->orFilterWhere(['like', 'Ins.name', $where['keywords']]);
            $filter = true;
        }
        if (!empty($where['position'])) {
            $query->andWhere(['like', 'Ins.position', $where['position']]);
            $filter = true;
        }
        if (!empty($where['supplier'])) {
            $query->andWhere(['like', 'Ins.supplier', $where['supplier']]);
            $filter = true;
        }
        if (!empty($where['creator'])) {
            $query->andWhere(['Ins.create_by' => $where['creator']]);
            $filter = true;
        }
        if (!empty($where['instrument_type'])) { // add by hkk 2021/5/6
            $query->andWhere(['Ins.instrument_type' => $where['instrument_type']]);
            $filter = true;
        }

        $allInstrumentList = $query->indexBy('id')->asArray()->all();


        // 5 构造绘图数据
        $nameArr = []; // 仪器名称
        $dataArr = []; // 使用率
        $timesArr = []; // 使用次数
        $memberArr = []; // 使用人数
        $repairTimesArr = []; // 维修次数
        $checkTimesArr = []; // 校验次数
        $dayPerHour = intval($where['open_time']); // 默认24h
        foreach ($allInstrumentList as $id => $ins) {
            if (!empty($instrumentRecordInfo[$id])) { // 有对应运行记录，则计算使用率，否则使用率为0
                $time = 0;
                $operateMinutes = explode(',', $instrumentRecordInfo[$id]['minutes']);
                foreach ($operateMinutes as $minute) {
                    $time += abs((intval($minute) / 60)); // 转变为小时
                }

                $startTime = !empty($where['start_time']) ? $where['start_time'] : $ins['create_time']; // 默认按创建时间
                $endTime = !empty($where['end_time']) ? ($where['end_time']) : date("Y-m-d H:i:s "); // 默认按当前时间
                $allTimes = $dayPerHour * abs(strtotime($endTime) - strtotime($startTime)) / (60 * 60 * 24);  //计算出两个时间差，转化为小时

                $allInstrumentList[$id]['use_rate'] = round($time * 100 / $allTimes, 2) > 100 ? 100 : round($time * 100 / $allTimes, 2); // 使用率


                $allInstrumentList[$id]['times'] = count($operateMinutes); // 使用次数
                $allInstrumentList[$id]['members'] = $instrumentRecordInfo[$id]['members']; // 使用人数


            } else {
                $allInstrumentList[$id]['use_rate'] = 0; // 使用率
                $allInstrumentList[$id]['times'] = 0; // 使用次数
                $allInstrumentList[$id]['members'] = 0; // 使用人数
            }

            $allInstrumentList[$id]['repairTimes'] = !empty($repairRecordInfo[$id]['repair_times']) ? $repairRecordInfo[$id]['repair_times'] : 0;; // 维修次数
            $allInstrumentList[$id]['checkTimes'] = !empty($checkRecordInfo[$id]['check_times']) ? $checkRecordInfo[$id]['check_times'] : 0;; // 校验次数

            if ($filter) { // 筛选过后即使仪器没有运行也展示所有
                $nameArr[] = $ins['name'] . '(' . $ins['batch_number'] . ')';
                $dataArr[] = $allInstrumentList[$id]['use_rate'];
                $timesArr[] = $allInstrumentList[$id]['times'];
                $memberArr[] = $allInstrumentList[$id]['members'];
                $repairTimesArr[] = $allInstrumentList[$id]['repairTimes'];
                $checkTimesArr[] = $allInstrumentList[$id]['checkTimes'];
            } else {
                if (/*$allInstrumentList[$id]['use_rate'] < 100 &&*/
                    $allInstrumentList[$id]['use_rate'] > 0
                ) {
                    $nameArr[] = $ins['name'] . '(' . $ins['batch_number'] . ')';
                    $dataArr[] = $allInstrumentList[$id]['use_rate'];
                    $timesArr[] = $allInstrumentList[$id]['times'];
                    $memberArr[] = $allInstrumentList[$id]['members'];
                    $repairTimesArr[] = $allInstrumentList[$id]['repairTimes'];
                    $checkTimesArr[] = $allInstrumentList[$id]['checkTimes'];
                }
            }
        }

        $usageData = [
            'nameArr' => $nameArr,
            'dataArr' => $dataArr,
            'timesArr' => $timesArr,
            'memberArr' => $memberArr,
            'repairTimesArr' => $repairTimesArr,
            'checkTimesArr' => $checkTimesArr,
        ];


        return $this->success($usageData);
    }


    /**
     * Notes: 获取仪器预约统计数据
     * Author: hkk
     * Date: 2021/4/23 15:07
     * @param $where
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getInstrumentBookStatistics($where)
    {

        // 1 获取所有的仪器预约记录
        $bookInfo = InstrumentsBookModel::find()->from(InstrumentsBookModel::tableName() . ' IB')
            ->select([
                'IB.id',
                'IB.instrument_id',
                'count(DISTINCT IB.create_by) as members',  //该仪器预约过的人数
                'group_concat(TIMESTAMPDIFF(MINUTE,IB.start_time,IB.end_time)) as minutes' // 所有的预约时间段
            ])
            ->where(['IB.status' => 1])
            ->andWhere(['not', ['IB.start_time' => null]])
            ->andWhere(['not', ['IB.end_time' => null]]);
        if (!empty($where['start_time'])) {
            $bookInfo->andWhere(['>=', 'IB.start_time', $where['start_time']]);
        }
        if (!empty($where['end_time'])) {
            $bookInfo->andWhere(['<=', 'IB.end_time', $where['end_time']]);
        }
        $bookInfoList = $bookInfo->groupBy('IB.instrument_id')->indexBy('instrument_id')->asArray()->all();


        // 2 查询所有的仪器并筛选
        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->select[] = 'Ins.id';
        $query->select[] = 'Ins.name';
        $query->select[] = 'Ins.batch_number';
        $query->select[] = 'Ins.create_time'; // 创建时间
        $filter = false;
        if (!empty($where['keywords'])) {
            $query->andFilterWhere(['like', 'Ins.batch_number', $where['keywords']])
                ->orFilterWhere(['like', 'Ins.name', $where['keywords']]);
            $filter = true;
        }
        if (!empty($where['position'])) {
            $query->andWhere(['like', 'Ins.position', $where['position']]);
            $filter = true;
        }
        if (!empty($where['supplier'])) {
            $query->andWhere(['like', 'Ins.supplier', $where['supplier']]);
            $filter = true;
        }
        if (!empty($where['creator'])) {
            $query->andWhere(['Ins.create_by' => $where['creator']]);
            $filter = true;
        }
        if (!empty($where['instrument_type'])) { // add by hkk 2021/5/6
            $query->andWhere(['Ins.instrument_type' => $where['instrument_type']]);
            $filter = true;
        }
        $allInstrumentList = $query->indexBy('id')->asArray()->all();


        // 3 构造绘图数据
        $nameArr = []; // 仪器名称
        $dataArr = []; // 预约率
        $timesArr = []; // 预约次数
        $memberArr = []; // 预约人数
        $dayPerHour = intval($where['open_time']); // 默认24h
        foreach ($allInstrumentList as $id => $ins) {
            if (!empty($bookInfoList[$id])) { // 有对应预约记录，则计算预约率，为0
                $time = 0;
                $operateMinutes = explode(',', $bookInfoList[$id]['minutes']);
                foreach ($operateMinutes as $minute) {
                    $time += abs((intval($minute) / 60)); // 转变为小时
                }

                $startTime = !empty($where['start_time']) ? $where['start_time'] : $ins['create_time']; // 默认按创建时间
                $endTime = !empty($where['end_time']) ? ($where['end_time']) : date("Y-m-d H:i:s "); // 默认按当前时间
                $allTimes = $dayPerHour * abs(strtotime($endTime) - strtotime($startTime)) / (60 * 60 * 24);  //计算出两个时间差，转化为小时

                $allInstrumentList[$id]['book_rate'] = round($time * 100 / $allTimes, 2);
                $allInstrumentList[$id]['times'] = count($operateMinutes);
                $allInstrumentList[$id]['members'] = $bookInfoList[$id]['members'];
            } else {
                $allInstrumentList[$id]['book_rate'] = 0;
                $allInstrumentList[$id]['times'] = 0;
                $allInstrumentList[$id]['members'] = 0;
            }


            if ($filter) { // 筛选过后即使仪器没有预约也展示所有
                $nameArr[] = $ins['name'] . '(' . $ins['batch_number'] . ')';
                $dataArr[] = $allInstrumentList[$id]['book_rate'];
                $timesArr[] = $allInstrumentList[$id]['times'];
                $memberArr[] = $allInstrumentList[$id]['members'];
            } else {
                if ($allInstrumentList[$id]['book_rate'] > 0) {
                    $nameArr[] = $ins['name'] . '(' . $ins['batch_number'] . ')';
                    $dataArr[] = $allInstrumentList[$id]['book_rate'];
                    $timesArr[] = $allInstrumentList[$id]['times'];
                    $memberArr[] = $allInstrumentList[$id]['members'];
                }
            }
        }

        $usageData = [
            'nameArr' => $nameArr,
            'dataArr' => $dataArr,
            'timesArr' => $timesArr,
            'memberArr' => $memberArr,
        ];


        return $this->success($usageData);
    }

    /**
     * Notes: 获取仪单个仪器使用情况
     * Author: hkk
     * Date: 2021/4/23 15:07
     * @param $instrumentId
     * @param $where
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getInstrumentSingleUsageStatistics($instrumentId, $where)
    {


        if ($where['instrument_display_type'] == 1) {

            // 1 获取该仪器运行记录
            $recordInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
                ->leftJoin(InstrumentRunningRecordModel::tableName() . ' AS ID', 'ID.instrument_id = IM.id')
                ->select([
                    'IM.create_time',
                    'IM.batch_number',
                    'IM.name',
                    'ID.id',
                    'ID.instrument_id',
                    'ID.operator',
                    'group_concat(TIMESTAMPDIFF(MINUTE,ID.start_running_time,ID.end_running_time)) as minutes' // 使用时间
                ])
                ->where(['IM.id' => $instrumentId])
                ->andWhere(['<>', 'ID.end_running_time', '']) // 不等于空
                ->andWhere(['not', ['ID.end_running_time' => null]]) // 不等于空 说明已经结束运行了
                ->andWhere(['<>', 'ID.operator', '']) // 不等于空
                ->andWhere(['not', ['ID.operator' => null]]); // 不等于空
            if (!empty($where['start_time'])) {
                $recordInfo->andWhere(['>=', 'ID.start_running_time', $where['start_time']]);
            }
            if (!empty($where['end_time'])) {
                $recordInfo->andWhere(['<=', 'ID.end_running_time', $where['end_time']]);
            }
            $instrumentRecordInfo = $recordInfo->groupBy('ID.operator')->asArray()->all(); // 按操作人分类

            if (count($instrumentRecordInfo) < 1) {
                return $this->success(['info' => 'no record']); // 该仪器没有运行记录，不用画图
            }


            // 2 构造数据属类型
            $nameArr = []; // 操作人名称
            $dataArr = []; // 使用率
            $timeArr = []; // 使用时长
            $timesArr = []; // 使用次数
            $dayPerHour = intval($where['open_time']); // 默认24h
            foreach ($instrumentRecordInfo as $key => $data) {

                $nameArr[] = $data['operator'];
                // 计算使用时长
                $time = 0;
                $operateMinutes = explode(',', $data['minutes']);
                foreach ($operateMinutes as $minute) {
                    $time += abs((intval($minute) / 60)); // 转变为小时
                }
                $timeArr[] = round($time, 2);

                // 计算使用次数
                $timesArr[] = count($operateMinutes);

                // 计算使用率
                $startTime = !empty($where['start_time']) ? $where['start_time'] : $data['create_time']; // 默认按创建时间
                $endTime = !empty($where['end_time']) ? ($where['end_time']) : date("Y-m-d H:i:s "); // 默认按当前时间
                $allTimes = $dayPerHour * abs(strtotime($endTime) - strtotime($startTime)) / (60 * 60 * 24);  //计算出两个时间差，转化为小时
                $dataArr[] = round($time * 100 / $allTimes, 2) > 100 ? 100 : round($time * 100 / $allTimes, 2);
            }

            $usageData = [
                'nameArr' => $nameArr,
                'dataArr' => $dataArr,
                'timeArr' => $timeArr,
                'timesArr' => $timesArr,
            ];
        } else {

            // 1 获取该仪器所有运行记录
            $recordInfo = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' IM')
                ->leftJoin(InstrumentRunningRecordModel::tableName() . ' AS ID', 'IM.id = ID.instrument_id')
                ->select([
                    'IM.create_time',
                    'IM.name',
                    'ID.start_running_time',
                    'ID.end_running_time',
                ])
                ->where(['IM.id' => $instrumentId])
                ->andWhere(['<>', 'ID.start_running_time', '']) // 不等于空
                ->andWhere(['not', ['ID.start_running_time' => null]]) // 不等于空 说明已经结束运行了
                ->andWhere(['<>', 'ID.end_running_time', '']) // 不等于空
                ->andWhere(['not', ['ID.end_running_time' => null]]); // 不等于空
            if (!empty($where['start_time'])) {
                $recordInfo->andWhere(['>=', 'ID.start_running_time', $where['start_time']]);
            }
            if (!empty($where['end_time'])) {
                $recordInfo->andWhere(['<=', 'ID.end_running_time', $where['end_time']]);
            }
            $instrumentRecordInfo = $recordInfo->asArray()->all(); // 按操作人分类

            // VarDumper::dump($instrumentRecordInfo);DIE;
            if (count($instrumentRecordInfo) < 1) {
                return $this->success(['info' => 'no record']); // 该仪器没有运行记录，不用画图
            }

            // 计算所有的日期作为横坐标，默认仪器创建日期开始,当前日期结束
            $startTime = !empty($where['start_time']) ? $where['start_time'] : $instrumentRecordInfo[0]['create_time'];
            $endTime = !empty($where['end_time']) ? ($where['end_time']) : date("Y-m-d H:i:s ");
            $allDate = $this->day_time_array($startTime, $endTime); // 所有日期数组

            // 遍历记录,添加时长到日期数组,运行时间分割为按天的日期，分配计算时长
            foreach ($instrumentRecordInfo as $key => $data) {
                $dayTime = $this->day_time_array($data['start_running_time'], $data['end_running_time'], true);
                foreach ($dayTime as $date => $data2) {
                    if (!empty($allDate[$date])) {
                        $allDate[$date]['h_time'] = $allDate[$date]['h_time'] + $data2['h_time']; // 叠加可以允许同时运行
                    }
                }
            }

            // 构造画图的日期-时长数据
            $usageData = [];
            foreach ($allDate as $key => $data3) {
                $usageData[] = [$key, $data3['h_time']];
            }
        }
        return $this->success($usageData);
    }

    /**
     * Notes:仪器新增自定义列
     * Author: hkk
     * Date: 2021/4/26 9:06
     * @return array
     */
    public function addInstrumentColumn()
    {

        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $record = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();
        $colIndex = 1; // add by hkk 2022/8/12 记录要增加的列索引
        if ($record) { // 更新记录

            foreach ($record as $key => $value) {
                if (substr($key, 0, 5) == 'field' && !empty($value)) {
                    $colIndex += 1;
                }
            }
            if ($colIndex >= 20) { // add by hkk 2022/8/12  改为后端验证
                return $this->fail(\Yii::t('base', 'instrument_reminder_tip10'));
            }

            // 计算要增加的列索引
            $fieldIdx = "field{$colIndex}";
            InstrumentDefineFields::updateAll(
                ['field' . $colIndex => \Yii::t('base', 'define_title')],
                [
                    'type' => 1,
                    'status' => 1,
                ]
            );
        } else {  //新增记录
            $insField = new InstrumentDefineFields();
            $insField->setAttributes([
                'type' => 1,
                'status' => 1,
                'field1' => \Yii::t('base', 'define_title'),
            ]);
            if (!$insField->save()) {
                $transaction->rollBack();
                return $this->fail('fail to add new column');
            }
            $fieldIdx = 'field1';
        }

        //! 更新默认需要显示的仪器管理字段
        $showFieldConfig_row = InstrumentDefineFields::findOne(['type' => 2, 'status' => 1]);
        if (!isset($showFieldConfig_row)) {
            $showFieldConfig_row = new InstrumentDefineFields();
            $showFieldConfig_row->setAttributes(['type' => 2, 'status' => 1, 'field_config' => '']);
        }
        $showFieldSetting_str = $showFieldConfig_row->field_config;
        $showFieldSetting_list = array_values(array_filter(explode(',', $showFieldSetting_str)));
        $showFieldSetting_list[] = $fieldIdx;
        $showFieldConfig_row->field_config = join(',', $showFieldSetting_list);
        if (!$showFieldConfig_row->save()) {
            $transaction->rollBack();
            return $this->fail('fail to set new column show');
        }

        $transaction->commit();
        return $this->success([
            'addField' => 'field' . $colIndex
        ]);
    }


    /**
     * Notes: 查询仪器字段配置
     * Author: hkk
     * Date: 2022/8/9 14:29
     * @return array
     */
    public function viewInstrumentFieldConfig()
    {

        /// WARNING: 如果想要添加eln系统初始就默认有效的字段,需添加到\frontend\models\InstrumentDefineFields::InitShowFields中 cmt dx
        /// 注意逗号分隔
        // 默认显示全部字段，包括20个自定义字段
        $defaultField_list = array_merge(
            ['picture', 'name', 'batch_number', 'specification', 'instrument_type', 'model', 'manufacturer', 'supplier', 'position', 'status', 'check_situation'],
            ['create_by', 'create_time', 'responsible_person', 'in_charge_person', 'maintenance_person'],
            ['remark', 'data_type', 'files', 'groupIds', 'departmentIds'],
            ['start_expiry_time', 'end_expiry_time', 'start_check_time', 'end_check_time', 'start_running_time', 'end_running_time', 'repair_start_time', 'repair_end_time'],
            ['available_slots', 'max_advance_day', 'min_advance', 'max_booking_duration'],
            /// WARNING自定义字段默认显示在最后
            ['field1', 'field2', 'field3', 'field4', 'field5', 'field6', 'field7', 'field8', 'field9', 'field10'],
            ['field11', 'field12', 'field13', 'field14', 'field15', 'field16', 'field17', 'field18', 'field19', 'field20']
        );
        $defaultFieldString = join(',', $defaultField_list);

        // 查询字段设置,type = 2 这里查询的是有效字段，有效字段的顺序和页面字段顺序是一致的
        $defaultFields = InstrumentDefineFields::find()->select('field_config')->where(['type' => 2, 'status' => 1])->asArray()->one();

        // 查询所有字段字段名称
        $defineFieldMap = InstrumentDefineFields::find()->select([])->where(['type' => 1, 'status' => 1,])->asArray()->one();//查询自定义的字段名称
        $defineFieldMap = !$defineFieldMap ? [] : $defineFieldMap;

        $basicFieldMap = [ //仪器库基本字段名称
            'picture' => \Yii::t('base', 'picture'),
            'name' => \Yii::t('base', 'name'),
            'batch_number' => \Yii::t('base', 'batch_number'),
            'specification' => \Yii::t('base', 'specification'),
            'instrument_type' => \Yii::t('base', 'instrument_type'),
            'model' => \Yii::t('base', 'model'),
            'manufacturer' => \Yii::t('base', 'manufacturer'),
            'status' => \Yii::t('base', 'status'),
            'check_situation' => \Yii::t('base', 'check_situation'),
            'position' => \Yii::t('base', 'position'),
            'create_by' => \Yii::t('base', 'create_person'),
            'responsible_person' => \Yii::t('base', 'response_person'),
            'in_charge_person' => \Yii::t('base', 'person_in_charge'),
            'maintenance_person' => \Yii::t('base', 'maintainer'),
            'supplier' => \Yii::t('base', 'supplier'),
            'remark' => \Yii::t('base', 'remark'),
            'data_type' => \Yii::t('base', 'data_type'),
            'create_time' => \Yii::t('base', 'create_time'),
            'groupIds' => \Yii::t('base', 'group_name'),
            'departmentIds' => \Yii::t('base', 'belong_department'),
            'files' => \Yii::t('base', 'file'),
            'start_expiry_time' => \Yii::t('base', 'expiry_data_start_time'),
            'end_expiry_time' => \Yii::t('base', 'expiry_data_end_time'),
            'start_check_time' => \Yii::t('base', 'check_start_time'),
            'end_check_time' => \Yii::t('base', 'check_end_time'),
            'start_running_time' => \Yii::t('base', 'start_running_time'),
            'end_running_time' => \Yii::t('base', 'end_running_time'),
            'repair_start_time' => \Yii::t('base', 'repair_start_time'),
            'repair_end_time' => \Yii::t('base', 'repair_end_time'),
            'available_slots' => \Yii::t('base', 'available_slots'),
            'max_advance_day' => \Yii::t('base', 'max_advance_day'),
            'min_advance' => \Yii::t('base', 'min_advance'),
            'max_booking_duration' => \Yii::t('base', 'max_booking_duration'),
        ];
        $fieldMap = array_merge($defineFieldMap, $basicFieldMap);

        if (!$defaultFields) { // 添加字段配置
            $transaction = \Yii::$app->integle_ineln->beginTransaction();

            // 默认选择所有的字段
            $insField = new InstrumentDefineFields();
            $insField->setAttributes([
                'type' => 2,
                'status' => 1,
                'field_config' => join(',', InstrumentDefineFields::InitShowFields),//如果由于默认设置没有设置过显示自定义字段设置,新增默认显示设置
            ]);

            if (!$insField->save()) {
                $transaction->rollBack();
                return $this->fail('fail to add new field config');
            }
            $transaction->commit();

            /// 修复当用户没有设置过仪器库自定义字段时,由于查询到的仪器自定义字段总是为空,
            /// 而保存后,显示字段设置会将自定义字段也保存进显示字段设置,导致报错的问题. mod dx
            $defaultFields = $insField->toArray();
        }
        if (!strpos($defaultFields['field_config'], "status")) { //Bug961 状态改为仪器库必须的有效字段
            $defaultFields['field_config'] .= ',status';
        }
        return $this->success([
            'showFields' => !$defaultFields ? $defaultFieldString : $defaultFields['field_config'], //有效字段 若为空，返回默认字段 有效字段顺序即为字段顺序
            'allFields' => $defaultFieldString,  //默认显示的所有字段
            'fieldMap' => $fieldMap, //返回字段名
        ]);
    }


    /**
     * Notes:设置仪器字段配置
     * Author: hkk
     * @param $fieldConfig
     * Date: 2022/8/9 17:36
     * @return array
     */
    public function setInstrumentFieldConfig($fieldConfig)
    {
        if (!empty($fieldConfig)) {
            $oldDetails = InstrumentDefineFields::find()
                ->select('field_config')
                ->where(['type' => 2, 'status' => 1])
                ->asArray()->one();
            $oldFieldConfig = explode(',', $oldDetails['field_config']);
            $newFieldConfig = explode(',', $fieldConfig);
            foreach ($newFieldConfig as $field) {
                if (!in_array($field, $oldFieldConfig)) {
                    array_push($oldFieldConfig, $field);
                }
            }
            foreach ($oldFieldConfig as $key => &$field) {
                if (!in_array($field, $newFieldConfig)) {
                    unset($oldFieldConfig[$key]);
                }
            }
            InstrumentDefineFields::updateAll(
                ['field_config' => implode(',', $oldFieldConfig)],
                [
                    'type' => 2,
                    'status' => 1,
                ]
            );
        }
        return $this->success([]);
    }

    /**
     * Notes:把起止日期拆分成中间每天的日期及时长数组 $needTime是否显示每天的小时数目
     * Author: hkk
     * Date: 2021/4/26 17:44
     * @param $start_time
     * @param $end_time
     * @param $needTime
     * @return array
     */
    protected function day_time_array($start_time, $end_time, $needTime = false)
    {
        $day_list = [];
        $day_key = ceil($this->diffBetweenTwoDays($start_time, $end_time));  //计算请假跨度天数
        for ($i = 1; $i <= $day_key; $i++) {
            //判断是否是第一天
            $day_info = array("start_time" => '0:00', "end_time" => '23:59');
            $key = $i - 1;
            $week_i = $key;
            if ($i == 1) {
                $data_time = strtotime($start_time);
                $day_info['start_time'] = date("H:i", strtotime($start_time));
            } else {
                $data_time = strtotime("$start_time + $week_i day");
            }

            //判断是否是最后一天 是否是第一天
            if ($i == $day_key) {
                $day_info['end_time'] = date("H:i", strtotime($end_time));
            }

            $day_info['day_time'] = date("Y-m-d", $data_time);
            if ($needTime) {
                if ($day_info['start_time'] == '0:00' && $day_info['end_time'] == '23:59') {
                    $day_info['h_time'] = 24;
                } else {
                    $day_info['h_time'] = round((strtotime(date($day_info['day_time'] . ' ' . $day_info['end_time']))
                            - strtotime(date($day_info['day_time'] . ' ' . $day_info['start_time']))) / 3600, 2);
                }
            } else {
                $day_info['h_time'] = 0;
            }

            $day_list[$day_info['day_time']] = $day_info; // 日期为key
        }
        return $day_list;
    }

    /**
     * Notes:计算两日期之间占用的天数，包含头尾
     * Author: hkk
     * Date: 2021/4/26 17:47
     * @param $day1
     * @param $day2
     * @return float|int
     */
    protected function diffBetweenTwoDays($day1, $day2)
    {
        $second1 = strtotime($day1);
        $second2 = strtotime($day2);
        if ($second1 < $second2) {
            $tmp = $second2;
            $second2 = $second1;
            $second1 = $tmp;
        }
        $date_start = strtotime(date("Y-m-d", $second2));
        $date_end = strtotime(date("Y-m-d", $second1));
        $date_day = (($date_end - $date_start) / 86400) + 1;
        $real_day = ($second1 - $second2) / 86400;
        return $date_day > $real_day ? $date_day : $real_day;
    }

    /**
     * Notes:批量编辑 上传生成excel
     * Author: hkk
     * Date: 2021/6/7 16:45
     * @param $chooseIds
     * @param $unCheckedField
     * @return array
     * @throws \PHPExcel_Exception
     */
    public function generalBatchEditOriginExcel($chooseIds, $unCheckedField)
    {

        // 获取查询的数据
        $where['chooseIds'] = $chooseIds;
        $where['viewAuth'] = 1; // add by hkk 2022/8/4
        $instrumentsData = (new InstrumentServer())->listInstrumentsView($where, null, null, true);

        // 解析隐藏不导出的字段
        $hideDefineField = [];
        $hideGeneralField = [];
        $unCheckedField = explode(',', $unCheckedField);

        // 构造标题数据，过滤隐藏字段，分通用和自定义字段
        $typeString = '';
        $defineField = [];
        $generalField = [];
        $hideExcelField = []; // excel 第一行的隐藏字段
        $type_list = $instrumentsData['type_list'];
        $defineFields = $instrumentsData['defineFields'];

        // 需要隐藏的字段，无论是否是有效字段
        $defaultHideFields = [
            'picture', 'create_by', 'start_expiry_time', 'end_expiry_time', 'start_check_time', 'end_check_time',
            'start_running_time', 'end_running_time', 'repair_start_time', 'repair_end_time', 'files', 'data_type', 'create_time'
        ];
        // 增加字段配置过滤
        // 查询字段配置
        $fieldConfig = (new InstrumentServer())->viewInstrumentFieldConfig();
        $fieldConfigShowFields = explode(',', $fieldConfig['data']['showFields']);
        array_unshift($fieldConfigShowFields, 'instrument_id');
        foreach ($type_list as $type) {
            $typeString .= ($type['dict_value'] . ' ');
        }
        foreach ($fieldConfigShowFields as $field) {
            if (!in_array($field, $defaultHideFields)) {
                $enField = $field;
                if ($field == 'instrument_id') {
                    $enField = 'instrument_id_excel';
                }
                if ($field == 'name') {
                    $enField = 'instrument_name_excel';
                }
                if ($field == 'status') {
                    $enField = 'instrument_status_excel';
                }
                if ($field == 'groupIds') {
                    $enField = 'instrument_group_excel';
                }
                if ($field == 'departmentIds') {
                    $enField = 'instrument_department_excel';
                }
                if ($field == 'check_situation') {
                    $enField = 'instrument_check_excel';
                }
                if ($field == 'response_person' || $field == 'responsible_person') {
                    $enField = 'instrument_response_excel';
                }
                if ($field == 'person_in_charge' || $field == 'in_charge_person') {
                    $enField = 'instrument_charge_excel';
                }
                if ($field == 'maintainer' || $field == 'maintenance_person') {
                    $enField = 'instrument_maintainer_excel';
                }
                if ($field == 'instrument_type') {
                    $generalField[] = \Yii::t('base', $enField) . '(' . trim($typeString) . ')';
                } else {
                    $generalField[] = \Yii::t('base', $enField);
                }

                $hideExcelField[] = $field;

                if (substr($field, 0, 5) == 'field') {
                    if (!in_array($field, $unCheckedField)) {
                        $generalField[count($generalField) - 1] = $defineFields[$field];
                        $defineString = str_replace("field", "define_", $field);
                        $hideExcelField[count($generalField) - 1] = $defineString;
                    }
                }
            }
        }
        $keyResult = $generalField;

        // 新建excel
        // require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        //  require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new \PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 数字转excel列标函数
        function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 1 写入隐藏行字段
        foreach ($hideExcelField as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '1', $title); // 设置隐藏行
        }

        // 2 写入标题字段
        foreach ($keyResult as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key) . '2', $title); // 设置标题字段
        }


        // 3 写入内容字段
        $instrumentList = $instrumentsData['instruments_list'];
        foreach ($instrumentList as $row => $insItem) {
            foreach ($hideExcelField as $colIndex => $keyItem) {
                switch ($keyItem) {
                    case "instrument_id":
                        $value = $insItem['id'];
                        break;
                    case "status":
                        $value = $insItem['status_string'];
                        break;
                    case "check_situation":
                        if ($insItem['check_situation'] != '0') {
                            $value = \Yii::t('base', 'needCheck');
                        } else {
                            $value = $insItem['check_situation_string'];
                        }
                        break;
                    case "groupIds":
                        $value = $insItem['group_name_string'];
                        break;
                    case "departmentIds":
                        $value = $insItem['department_name_string'];
                        break;
                    case "response_person":
                        $value = $insItem['responsible_person_string'];
                        break;
                    case "person_in_charge":
                        $value = $insItem['in_charge_person_string'];
                        break;
                    case "maintainer":
                        $value = $insItem['maintenance_person_string'];
                        break;
                    default:
                        if (preg_match('/^define\_(\d+)/', $keyItem, $matches)) {
                            $value = !empty($insItem['data' . $matches[1]]) ? $insItem['data' . $matches[1]] : '';
                        } else {
                            $value = !empty($insItem[$keyItem]) ? $insItem[$keyItem] : '';
                        }
                        break;
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($colIndex) . ($row + 3), $value);
            }
        }


        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->getActiveSheet()->getRowDimension(1)->setRowHeight(0); // 隐藏第一行
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');


        (new \yii\helpers\FileHelper())->mkdirs(\Yii::getAlias('@filepath') . DS . 'batch_edit_instruments');
        $filePath = 'batch_edit_instruments' . DS . 'origin_' . time() . '.xlsx';
        $file = \Yii::getAlias('@filepath') . DS . $filePath;
        $objWriter->save($file);

        // 写入路径到数据库的表
        return $this->success($filePath);
    }

    /**
     * Notes:添加批量编辑历史信息
     * Author: hkk
     * Date: 2019/10/31 14:52
     * @param $data
     * @return array
     */
    public function addInstrumentBatchEditHistory($data)
    {

        $his = new InstrumentsBatchEditFile();
        $his->setAttributes($data);
        if (!$his->save()) {
            return $this->fail('fail to add instrument batch edit history');
        }
        $insertId = $his->getDb()->getLastInsertID(); // add by hkk 2022/5/31

        return $this->success(['id' => $insertId]);
    }

    /**
     * Notes: 批量添加记录每条失败的信息到数据库
     * Author: hkk
     * Date: 2022/5/30 18:54
     * @param $batchFileId
     * @param $failInfo
     * @return array
     */
    public function addInstrumentBatchErrorInfo($batchFileId, $failInfo)
    {
        foreach ($failInfo as $row => $rowError) {
            $his = new InstrumentBatchUploadInfo();
            $his->setAttributes([
                'batch_file_id' => $batchFileId,
                'row' => $row,
                'error_info' => $rowError,
            ]);
            if (!$his->save()) {
                return $this->fail('fail to add instrument batch add info');
            }
        }
        return $this->success([]);
    }
    /**
     * 根据仪器名称模糊搜索
     * @param $expPage
     * @return array
     * <AUTHOR> 2025/5/21
     */
    public function getInstrumentsByName($name)
    {
        // 查询符合条件的记录

        $ins = InstrumentsModel::find()
            ->where(['regexp', 'name', '^' . $name])  // 使用正则表达式，从左到右进行匹配
            ->andWhere(['status' => 1])
            ->orderBy(['name' => SORT_ASC])
            ->limit(10)
            ->asArray()
            ->all();
        // 如果需要调试，确保仅在开发环境中输出
        return $this->success($ins);  // 返回结果
    }
    /**
     * Notes: 设置预约规则字段
     * *  Author: zsm
     * *  Date: 2025/5/8 17:30
     * * @param $postData
     * * * @return array
     */
    public function instrumentBookingConfig($postData)
    {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $instrumentIds = $postData['instrumentIds'];
        $available_slots = $postData['available_slots'];
        $max_advance_day = $postData['max_advance_day'];
        $min_advance = $postData['min_advance'];
        $max_booking_duration = $postData['max_booking_duration'];

        // 批量预加载
        $instruments = InstrumentsModel::find()
            ->where(['id' => $instrumentIds])
            ->andWhere(['status' => 1])
            ->indexBy('id') // 按ID索引
            ->all();

        foreach ($instrumentIds as $insId) {

            $instrument = $instruments[$insId];
            if ($instrument) {
                $instrument->available_slots = json_encode($available_slots);
                $instrument->max_advance_day = $max_advance_day;
                $instrument->min_advance = json_encode($min_advance);
                $instrument->max_booking_duration = json_encode($max_booking_duration);
                if (!$instrument->save()) {
                    $transaction->rollBack();
                    return $this->fail('fail to save instrument booking config');
                }
            }
        }
        $transaction->commit();
        return $this->success([]);
    }

    /**
     * Notes: 创建/编辑预约
     * *  Author: zsm
     * *  Date: 2025/5/21 19:00
     * *
     * @param $instrumentId  仪器id
     * @param $type 类型 0 创建 1编辑
     * @param $timeArr 更新时间的数组
     * @param $detail 其他细节
     * @param @id 预约记录id 仅在编辑情况下传入，创建是传入不使用
     * @return array
     */
    public function handleInstrumentBooking($instrumentId, $type, $timeArr, $detail, $id)
    {
        $transaction = \Yii::$app->integle_ineln->beginTransaction();
        $related_experiment = '';
        $remark = '';
        if (isset($detail['remark'])) {
            $remark = $detail['remark'];
        }
        if (isset($detail['related_experiment'])) {
            $remark = $detail['related_experiment'];
        }
        if (isset($detail['remark'])) {
            $remark = $detail['remark'];
        }
        $user = $detail['user'];

        switch ($type) {
            case 0: // 创建
                // 检查所有时间段是否可用
                foreach ($timeArr as $time) {
                    $check = $this->checkInstrumentAvailability(
                        $instrumentId,
                        $type,
                        $time['start_time'],
                        $time['end_time']
                    );
                    if ($check['status'] === 0) {
                        return $this->fail($check['info']);
                    }
                }

                // 创建预约记录
                foreach ($timeArr as $time) {
                    $instrument = new InstrumentsBookModel();
                    $instrument->instrument_id = $instrumentId;
                    $instrument->start_time = $time['start_time'];
                    $instrument->end_time = $time['end_time'];
                    $instrument->related_experiment = $related_experiment;
                    $instrument->remark = $remark;
                    $instrument->create_by = $user;

                    if (!$instrument->save()) {
                        $transaction->rollBack();
                        return $this->fail('fail to save instrument booking create');
                    }
                }
                $transaction->commit();
                return $this->success([]);

            case 1: // 编辑

                $currentTime = new \DateTime();
                $startTime = new \DateTime($timeArr[0]['start_time']);
                // 起始时间在当前时间之后则校验所有情况
                $check = $this->checkInstrumentAvailability(
                    $instrumentId,
                    $type,
                    $timeArr[0]['start_time'],
                    $timeArr[0]['end_time']
                );
                if ($check['status'] === 0) {
                    return $this->fail($check['info']);
                }


                $bookList = InstrumentsBookModel::find()
                    ->where(['id' => $id])
                    ->andWhere(['status' => 1])
                    ->one();

                $bookList->start_time = $timeArr[0]['start_time'];
                $bookList->end_time = $timeArr[0]['end_time'];

                $bookList->related_experiment = $related_experiment;
                $bookList->remark = $remark;
                $bookList->update_by = $user;

                if (!$bookList->save()) {
                    $transaction->rollBack();
                    return $this->fail('fail to save instrument booking edit');
                }
                $transaction->commit();
                return $this->success([]);

            default:
                break;
        }
    }

    /**
     * Notes: 校验选择的时间段有效性
     * *  Author: zsm
     * *  Date: 2025/5/21 19:00
     * @param $instrument_id 仪器id
     * @param $type 类型
     * @param $start_time 开始时间
     * @param $end_time 结束时间
     * @return array
     */
    public function checkInstrumentAvailability($instrument_id, $type, $start_time, $end_time)
    {


        // 2. 获取仪器配置信息
        $ins = InstrumentsModel::find()
            ->select(['available_slots', 'max_advance_day', 'min_advance', 'max_booking_duration'])
            ->where(['id' => $instrument_id])
            ->andWhere(['status' => 1])
            ->asArray()
            ->one();


        // 仅在创建状态校验
        if($type === 0) {
            
            // 1. 检查时间冲突：查询是否有其他预约与当前预约时间重叠  创建阶段才检查这个
            // 查询发生时间冲突的预约记录
            $conflictCount = InstrumentsBookModel::find()
                ->where(['instrument_id' => $instrument_id])
                ->andWhere(['status' => 1])
                ->andWhere([
                    'and',
                    ['<', 'start_time', $end_time],  // 其他预约的开始时间在当前预约结束时间之前
                    ['>', 'end_time', $start_time]   // 其他预约的结束时间在当前预约开始时间之后
                ])
                ->count();

            // 如果存在冲突，返回失败并包含冲突条数信息
            if ($conflictCount > 0) {
                return $this->fail('fail to create/edit instrument booking', ['conflict_count' => $conflictCount]);
            }

            // 5. 检查最大提前预约天数
            if (isset($ins['max_advance_day']) && is_numeric($ins['max_advance_day']) && $ins['max_advance_day'] > 0) {
                $currentTime = new \DateTime();
                $bookingStartTime = new \DateTime($start_time);
                $maxAdvanceDate = new \DateTime();
                $maxAdvanceDate->modify('+' . intval($ins['max_advance_day']) . ' days');

                // 如果预约开始时间超过最大提前天数，返回失败
                if ($bookingStartTime > $maxAdvanceDate) {
                    return $this->fail('fail to create/edit instrument booking because of max advance day');
                }
            }

            // 6. 检查最小提前时间
            $currentTime = new \DateTime();
            $bookingStartTime = new \DateTime($start_time);

            // 确保 min_advance 是数组，如果它是 JSON 字符串，需要解码
            if (is_string($ins['min_advance'])) {
                $ins['min_advance'] = json_decode($ins['min_advance'], true);
            }

            // 只在 min_advance 配置存在且有效时进行校验
            if (!empty($ins['min_advance']['value']) && !empty($ins['min_advance']['unit'])) {
                // 计算最小提前时间
                $minAdvanceSeconds = 0;
                switch ($ins['min_advance']['unit']) {
                    case 'day':
                        $minAdvanceSeconds = $ins['min_advance']['value'] * 24 * 3600;
                        break;
                    case 'hour':
                        $minAdvanceSeconds = $ins['min_advance']['value'] * 3600;
                        break;
                    case 'min':
                        $minAdvanceSeconds = $ins['min_advance']['value'] * 60;
                        break;
                    default:
                        return $this->fail('无效的时间单位');
                }

                // 计算预约开始时间与当前时间的差值（秒）
                $timeDiff = $bookingStartTime->getTimestamp() - $currentTime->getTimestamp();

                // 如果时间差小于最小提前时间，返回失败
                if ($timeDiff < $minAdvanceSeconds) {
                    return $this->fail('预约时间必须至少提前' . $ins['min_advance']['value'] . $ins['min_advance']['unit']);
                }
            }
            
            
            
        }
       



        // 3. 处理可用时间段配置
        if (is_string($ins['available_slots'])) {
            $ins['available_slots'] = json_decode($ins['available_slots'], true);
        }

        // 5. 检查预约时间是否在允许的时间段内
        $startTimeFormatted = date("H:i", strtotime($start_time));
        $endTimeFormatted = date("H:i", strtotime($end_time));

        // 如果传入的时间段是 "00:00 - 00:00"，则当作 "00:00 - 23:59" 处理
        if ($startTimeFormatted === '00:00' && $endTimeFormatted === '00:00') {
            $endTimeFormatted = '23:59';
        }

        $bookingTimeInSlot = false;

        if (isset($ins['available_slots']) && !empty($ins['available_slots']) && is_array($ins['available_slots'])) {
            foreach ($ins['available_slots'] as $slot) {
                // 如果时间段是 00:00 - 23:59，则不进行校验，直接跳过
                if ($slot[0] === '00:00' && $slot[1] === '23:59') {
                    $bookingTimeInSlot = true;
                    break;
                }

                // 检查预约时间是否在某个允许的时间段内
                if ($startTimeFormatted >= $slot[0] && $endTimeFormatted <= $slot[1]) {
                    $bookingTimeInSlot = true;
                    break;
                }
            }
            if (!$bookingTimeInSlot) {
                return $this->fail('fail to create instrument booking because of time slot');
            }
        }


        // 7. 检查最大预约时长
        // 确保 max_booking_duration 是数组，如果它是 JSON 字符串，需要解码
        if (is_string($ins['max_booking_duration'])) {
            $ins['max_booking_duration'] = json_decode($ins['max_booking_duration'], true);
        }

        // 只在 max_booking_duration 配置存在且有效时进行校验
        if (!empty($ins['max_booking_duration']['value']) && !empty($ins['max_booking_duration']['unit'])) {
            $bookingStartTime = new \DateTime($start_time);
            $bookingEndTime = new \DateTime($end_time);

            // 计算预约时长（秒）
            $durationInSeconds = $bookingEndTime->getTimestamp() - $bookingStartTime->getTimestamp();

            // 计算最大允许时长（秒）
            $maxDurationInSeconds = intval($ins['max_booking_duration']['value']);
            switch ($ins['max_booking_duration']['unit']) {
                case 'day':
                    $maxDurationInSeconds *= 24 * 3600;
                    break;
                case 'hour':
                    $maxDurationInSeconds *= 3600;
                    break;
                case 'min':
                    $maxDurationInSeconds *= 60;
                    break;
                default:
                    return $this->fail('无效的时长单位');
            }
            // 如果预约时长超过最大限制，返回失败
            // 使用 >= 而不是 > 来包含等于的情况
            if ($durationInSeconds >= $maxDurationInSeconds) {
                return $this->fail('预约时长不能超过' . $ins['max_booking_duration']['value'] . $ins['max_booking_duration']['unit']);
            }
        }
        // 所有检查都通过，返回成功
        return $this->success([]);
    }

    /**
     * /**
     * Notes:批量调整仪器 鹰群 部门 状态 类别 维护人 维修人 责任人
     * Author: hkk
     * Date: 2022/6/23 17:26
     * @param $postData
     * @return array
     */
    public function batchAdjustInstrument($postData)
    {

        $instrumentIds = $postData['instrumentIds'];
        $type = $postData['type'];
        $value = $postData['value'];


        switch ($type) {
            case 'group':

                $groupList = (new CenterInterface())->getGroupsListByCompanyId(\Yii::$app->view->params['curr_company_id']); // 用户所有鹰群信息
                $groupList = array_column($groupList, 'group_name', 'id'); // 鹰群名数组

                // 改了鹰群就加痕迹
                $emptyDepartmentInsIds = []; // add by hkk 2022/8/8  记录空部门的仪器id
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        $beforeGroupNameString = $this->getGroupNameStrings($groupList, $instrumentInfo['data']['groupIds']);
                        $afterGroupString = $this->getGroupNameStrings($groupList, $value);
                        $action_details = (\Yii::t('base', 'group') . ":" . $beforeGroupNameString . "=>" . $afterGroupString . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                    if (empty($instrumentInfo['data']['departmentIds'])) {
                        $emptyDepartmentInsIds[] = $insId;
                    }
                }

                // 更新数据
                InstrumentsModel::updateAll(['groupIds' => $value], ['id' => $instrumentIds]);


                // add by hkk 2022/8/8 改为空，则判断部门，若部门也为空，则改动对应的仪器为"不限仪器"
                if (empty($value) && count($emptyDepartmentInsIds) > 0) {
                    InstrumentsModel::updateAll(['groupIds' => 'all'], ['id' => $emptyDepartmentInsIds]);
                }


                return $this->success([]);

                break;

            case 'department':

                // 获取部门信息
                $redis = \Yii::$app->redis;
                $allDepartments = $redis->get('department_list');
                if (!empty($allDepartments)) {
                    $allDepartments = json_decode($allDepartments, TRUE);
                }
                $allDepartments = array_column($allDepartments, 'department_name', 'id'); // 部门名数组

                // 改了部门就加痕迹
                $emptyGroupInsIds = []; // add by hkk 2022/8/8  记录空鹰群的仪器id
                $allGroupInsIds = []; // add by hkk 2022/8/8  记录不限仪器id
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['departmentIds'] != $value) {
                        $beforeDepartmentNameString = $this->getGroupNameStrings($allDepartments, $instrumentInfo['data']['departmentIds']);
                        $afterDepartmentString = $this->getGroupNameStrings($allDepartments, $value);
                        $action_details = (\Yii::t('base', 'department') . ":" . $beforeDepartmentNameString . "=>" . $afterDepartmentString . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                    if (empty($instrumentInfo['data']['groupIds'])) {
                        $emptyGroupInsIds[] = $insId;
                    } else if ($instrumentInfo['data']['groupIds'] == 'all') {
                        $allGroupInsIds[] = $insId;
                    }
                }

                // 更新数据
                InstrumentsModel::updateAll(['departmentIds' => $value], ['id' => $instrumentIds]);

                // add by hkk 2022/8/8 改为空，则判断鹰群，若鹰群也为空，则改动对应的仪器为"不限仪器"，
                if (empty($value) && count($emptyGroupInsIds) > 0) {
                    InstrumentsModel::updateAll(['groupIds' => 'all'], ['id' => $emptyGroupInsIds]);
                }

                // add by hkk 2022/8/8 改为非空，取消原来的不限仪器，取消原来的all字段,
                if (!empty($value) && count($allGroupInsIds) > 0) {
                    InstrumentsModel::updateAll(['groupIds' => ''], ['id' => $allGroupInsIds]);
                }


                return $this->success([]);

                break;
            case 'status': // 改状态
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        // 仪器状态：0-> 已删除，1->正常，2->停用，3>维修中，4->报废
                        $statusMap = [
                            0 => \Yii::t('base', 'already_deleted'),
                            1 => \Yii::t('base', 'normal'),
                            2 => \Yii::t('base', 'suspend_use'),
                            3 => \Yii::t('base', 'repairing'),
                            4 => \Yii::t('base', 'scrap'),
                        ];
                        $action_details = (\Yii::t('base', 'status') . ":" . $statusMap[$instrumentInfo['data']['status']] . "=>" . $statusMap[$value] . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                }
                InstrumentsModel::updateAll(['status' => $value], ['id' => $instrumentIds]);
                return $this->success([]);
                break;
            case 'instrument_type': // 改类别

                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        $beforeType = !empty($instrumentInfo['data']['instrument_type']) ? $instrumentInfo['data']['instrument_type'] : '';
                        $action_details = (\Yii::t('base', 'instrument_type') . ":" . $beforeType . "=>" . $value . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                }
                InstrumentsModel::updateAll(['instrument_type' => $value], ['id' => $instrumentIds]);
                return $this->success([]);
                break;

            case 'person_in_charge': // 维护人
                $userList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                $userListNew = isset($userList['list']) ? $userList['list'] : [];
                $userListInfo = ArrayHelper::index($userListNew, 'id');
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['in_charge_person']);
                        $afterNameString = $this->getUsersNameStrings($userListInfo, $value);
                        $action_details = (\Yii::t('base', 'person_in_charge') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                }
                InstrumentsModel::updateAll(['in_charge_person' => $value], ['id' => $instrumentIds]);
                return $this->success([]);
            case 'response_person': // 负责人
                $userList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                $userListNew = isset($userList['list']) ? $userList['list'] : [];
                $userListInfo = ArrayHelper::index($userListNew, 'id');
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['responsible_person']);
                        $afterNameString = $this->getUsersNameStrings($userListInfo, $value);
                        $action_details = (\Yii::t('base', 'response_person') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                }
                InstrumentsModel::updateAll(['responsible_person' => $value], ['id' => $instrumentIds]);
                return $this->success([]);
                break;
            case 'maintainer': // 负责人
                $userList = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
                $userListNew = isset($userList['list']) ? $userList['list'] : [];
                $userListInfo = ArrayHelper::index($userListNew, 'id');
                foreach ($instrumentIds as $insId) {
                    $instrumentInfo = $this->getInsInfoById($insId);
                    if ($instrumentInfo['data']['groupIds'] != $value) {
                        $beforeUserNameString = $this->getUsersNameStrings($userListInfo, $instrumentInfo['data']['maintenance_person']);
                        $afterNameString = $this->getUsersNameStrings($userListInfo, $value);
                        $action_details = (\Yii::t('base', 'maintainer') . ":" . $beforeUserNameString . "=>" . $afterNameString . '</br>');
                        $this->addInstrumentHistory($insId, self::ACTION_EDIT, ['action_details' => $action_details]);
                    }
                }
                InstrumentsModel::updateAll(['maintenance_person' => $value], ['id' => $instrumentIds]);
                return $this->success([]);
                break;

            default:
                break;
        }

        // 根据check_status 和原本的check_situation确定新的check_situation

        return $this->success([]);
    }

    /**
     * Notes:查看修改状态是否需要审批
     * Author: zhouweiming
     * Date: 2023/4/12
     * @param $instrumentId
     * @param $instrumentStatus
     * @return array|int
     */
    public function instrumentChangeStatusApproval($instrumentId, $instrumentStatus)
    {
        $instrumentInfo = InstrumentsModel::find()->select('record_setting')->where(['id' => $instrumentId])->asArray()->one();
        $statusApproval = [];
        $statusApproval['instrument_status'] = 1;
        $recordSetting = json_decode($instrumentInfo['record_setting'], true);
        if (!empty($recordSetting)) {
            switch ($instrumentStatus) {
                case 4:
                    // 报废需要审核
                    if (isset($recordSetting['scrap_must_check_users'])) {
                        $statusApproval = ["instrument_status" => $instrumentStatus, "approval_user_ids" => $recordSetting['scrap_must_check_users']];
                    }
                    break;
                case 2:
                    // 停用需要审核
                    if (isset($recordSetting['suspend_use_must_check_users'])) {
                        $statusApproval = ["instrument_status" => $instrumentStatus, "approval_user_ids" => $recordSetting['suspend_use_must_check_users']];
                    }
                    break;
                case 0:
                    if (isset($recordSetting['delete_must_check_users'])) {
                        $statusApproval = ["instrument_status" => $instrumentStatus, "approval_user_ids" => $recordSetting['delete_must_check_users']];
                    }
                    break;
                case 3:
                    if (isset($recordSetting['apply_repair_must_check_users'])) {
                        $statusApproval = ["instrument_status" => $instrumentStatus, "approval_user_ids" => $recordSetting['apply_repair_must_check_users']];
                    }
                    break;
            }
        }

        return $statusApproval;

    }

    /**
     * Notes:创建状态创建审批
     * Author: zhouweiming
     * Date: 2023/4/12
     * @param $instrumentId int 仪器id
     * @param $instrumentStatus int 仪器状态
     * @param $reason string 修改状态原因
     * @param int $approvalFromBtn 是否由报修按钮发起审批
     * @return array
     */
    public function instrumentStatusCreateApproval($instrumentId, $instrumentStatus, $reason, $approvalFromBtn = 0)
    {
        $instrumentInfo = InstrumentsModel::find()->select('name, batch_number, record_setting, status')->where(['id' => $instrumentId,])->asArray()->one();
        $extraDataArr = [
            'instrument_id' => $instrumentId,
            'instrument_status' => $instrumentStatus, // 仪器状态
            'instrumentName' => $instrumentInfo['name'], // 消息邮件需要
            'instrumentBatchNumber' => $instrumentInfo['batch_number'], // 消息邮件需要
            'reason' => $reason, // 调整原因
            'approvalFromBtn' => $approvalFromBtn //审批是否来自报修按钮
        ];
        $extraData = json_encode($extraDataArr);
        $recordSetting = json_decode($instrumentInfo['record_setting'], true);
        $checkNode = [];
        if (!empty($recordSetting)) {
            switch ($instrumentStatus) {
                case 4:
                    // 报废需要审核
                    if (isset($recordSetting['scrap_must_check_users'])) {
                        $checkNode[0]['approval_user_ids'] = $recordSetting['scrap_must_check_users'];
                    }
                    $instrumentStatusNew = \Yii::t('base', 'scrap');
                    break;
                case 2:
                    // 停用需要审核
                    if (isset($recordSetting['suspend_use_must_check_users'])) {
                        $checkNode[0]['approval_user_ids'] = $recordSetting['suspend_use_must_check_users'];
                    }
                    $instrumentStatusNew = \Yii::t('base', 'suspend_use');
                    break;
                case 0:
                    if (isset($recordSetting['delete_must_check_users'])) {
                        $checkNode[0]['approval_user_ids'] = $recordSetting['delete_must_check_users'];
                    }
                    $instrumentStatusNew = \Yii::t('base', 'already_deleted');
                    break;
                case 3:
                    if (isset($recordSetting['apply_repair_must_check_users'])) {
                        $checkNode[0]['approval_user_ids'] = $recordSetting['apply_repair_must_check_users'];
                    }
                    $instrumentStatusNew = \Yii::t('base', 'repairing');
                    break;
            }
        }
        $approvalServer = new ApprovalServer();
        $createApprovalRes = $approvalServer->createApproval(\Yii::$app->params['approval_type']['instrument_check'], $instrumentId, 0, $extraData, $checkNode);
        $approvalUserInfo = (new CenterInterface())->userDetailsByUserIds($checkNode[0]['approval_user_ids']);
        $approvalUserInfo = ArrayHelper::index($approvalUserInfo, 'id');
        $approvalUserArr = [];
        foreach ($approvalUserInfo as $value) {
            $approvalUserArr[] = CommonServer::displayUserName($value);
        }
        switch ($instrumentInfo['status']) {
            case 0:
                $instrumentStatusOld = \Yii::t('base', 'already_deleted');
                break;
            case 2:
                $instrumentStatusOld = \Yii::t('base', 'suspend_use');
                break;
            case 3:
                $instrumentStatusOld = \Yii::t('base', 'repairing');
                break;
            case 4:
                $instrumentStatusOld = \Yii::t('base', 'scrap');
                break;
            case 1:
            default:
                $instrumentStatusOld = \Yii::t('base', 'normal');
                break;
        }
        $instrumentStatusOld = strtolower($instrumentStatusOld);
        if ($approvalFromBtn == 1 && $instrumentStatus == 0) {
            $action_details = (\Yii::t('base', 'approver') . ":" . implode(',', $approvalUserArr) . '</br>');
            $this->addInstrumentHistory($instrumentId, self::ACTION_DELETE, ['action_details' => $action_details]);
        } elseif ($approvalFromBtn == 1 && $instrumentStatus == 3) {
            $action_details = (\Yii::t('base', 'approver') . ":" . implode(',', $approvalUserArr) . '</br>');
            $this->addInstrumentHistory($instrumentId, self::ACTION_REPAIR, ['action_details' => $action_details]);
        } else {
            $action_details = (\Yii::t('base', 'status') . ":" . $instrumentStatusOld . "=>" . $instrumentStatusNew . '</br>');
            $action_details .= (\Yii::t('base', 'approver') . ":" . implode(',', $approvalUserArr) . '</br>');
            $this->addInstrumentHistory($instrumentId, self::ACTION_APPROVAL, ['action_details' => $action_details]);
        }

        return $this->success([]);
    }

    /**
     * Notes: 上传仪器图片
     * Author: zwm
     * Date: 2023/4/20
     * @param $data
     * @return array
     * @throws Exception
     */
    public function uploadInstrumentPicture($data)
    {
        // 更新数据库文件栏
        InstrumentsModel::updateAll(
            ['pictures' => $data['currentFiles']],
            ['id' => $data['instrumentId']]
        );

        $action_details = (\Yii::t('base', 'upload_picture') . ":" . $data['uploadFiles']);
        $hisData = [
            'action_details' => $action_details,
        ];


        $this->addInstrumentHistory($data['instrumentId'], self::ACTION_UPLOAD_PICTURE, $hisData);

        return $this->success([]);
    }

    /**
     * Notes: 三大记录字段顺序保存
     * Author: zwm
     * Date: 2023/7/13
     * @param $fieldArray
     * @param $instrumentId
     * @param $type
     * @return array
     */
    public function SaveRecordSortField($fieldConfig, $instrumentId, $type)
    {
        switch ($type) {
            case 'getInstrumentOperateRecordContent':
                $model = new InstrumentRunningRecordExtendFieldModel();
                break;
            case 'getInstrumentRepairRecordContent':
                $model = new InstrumentRepairRecordExtendFieldModel();
                break;
            case 'getInstrumentCheckRecordContent':
                $model = new InstrumentCheckRecordExtendFieldModel();
                break;
            case 'instruments':
                $model = new InstrumentDefineFields();
                $fieldConfig = explode(',', $fieldConfig);
                break;
        }
        $fieldConfig = array_unique($fieldConfig);
        if ($type == 'instruments') {
            $result = $model::findOne(['type' => 2, 'status' => 1]);
            $data = ['field_config' => implode(',', $fieldConfig)];
        } else {
            $result = $model::findOne(['instrument_id' => $instrumentId]);
            $data = ['field_sort' => json_encode($fieldConfig)];
        }

        if (empty($result)) {
            $result = $model;
            if ($type == 'instruments') {
                $data = ['field_config' => implode(',', $fieldConfig), 'type' => 2, 'status' => 1];
            } else {
                $data = ['field_sort' => json_encode($fieldConfig), 'instrument_id' => $instrumentId];
            }

        }
        $result->setAttributes($data);
        if (!$result->save()) {
            return $this->fail($result->getFirstErrors());
        }

        return $this->success([]);
    }

    public function getRepeatBatchNumber($batchNumber, $instrumentId)
    {
        $InstrumentsModel = new InstrumentsModel();
        $batch = $InstrumentsModel::find()
            ->where(['batch_number' => $batchNumber])
            ->andWhere(['!=', 'id', $instrumentId]) //bug#31492,bug#31556,编辑仪器时，判断batch_number是是否已存在时，排除本仪器和其他仪器batch_number为空的仪器，不校验仪器id的唯一性 mod dx
            ->asArray()->all();
        return count($batch);
    }

    /**
     * Notes: inTable功能：“引用他表数据”，根据配置获取他表数据
     * Author: xie yuxiang
     * Date : 2023/10/8 17:56
     * @param $text
     * @param $referenceFieldList
     * @param $getOtherDataByKey
     * @param $instrumentsData array 设备信息
     * @return array|null
     */
    public function getOtherData($text, $referenceFieldList, $getOtherDataByKey, $instrumentsData)
    {
        if (empty($text) || empty($instrumentsData)) {
            return null;
        } else {
            if (!$getOtherDataByKey) {
                // 如果按value查找，提前获取标题的数据，不要写在循环里
                $headerResult = (new InstrumentServer())->viewInstrumentFieldConfig();
                if ($headerResult['status'] == 0) {
                    return null;
                }
                $header = [];
                $fieldMap = $headerResult['data']['fieldMap'];
                $showFields = explode(',', $headerResult['data']['showFields']);

                foreach ($showFields as $showField) {
                    $value = $fieldMap[$showField];
                    if (!empty($value)) {
                        $header[$showField] = $value;
                    }
                }
            }

            $referenceDataList = [];
            foreach ($referenceFieldList as $referenceField) {
                if ($getOtherDataByKey) {
                    // 按照key获取
                    $key = $referenceField['key'];
                    if (strpos($key, 'field') === 0) {
                        $referenceFieldKey = str_replace('field', 'data', $key);
                    } else {
                        $referenceFieldKey = $key;
                    }
                } else {
                    // 按照value获取
                    $value = $referenceField['value'];
                    $referenceFieldKey = '';
                    foreach ($header as $headerKey => $headerValue) {
                        if ($headerValue == $value) {
                            if (strpos($headerKey, 'field') === 0) {
                                $referenceFieldKey = str_replace('field', 'data', $headerKey);
                            } else {
                                $referenceFieldKey = $headerKey;
                            }
                        }
                    }
                }
                if (emptyExclude0($referenceFieldKey)) {
                    $referenceDataList[] = ['key' => $referenceField['key'], 'value' => $referenceField['value'], 'otherData' => ''];
                } else {
                    $referenceData = isset($instrumentsData[$referenceFieldKey . '_string']) ? $instrumentsData[$referenceFieldKey . '_string'] : $instrumentsData[$referenceFieldKey];
                    if (emptyExclude0($referenceData)) {
                        $referenceDataList[] = ['key' => $referenceField['key'], 'value' => $referenceField['value'], 'otherData' => ''];
                    } else {
                        $referenceDataList[] = ['key' => $referenceField['key'], 'value' => $referenceField['value'], 'otherData' => $referenceData];
                    }
                }
            }
            \Yii::info($referenceFieldList);
            return $referenceDataList;
        }
    }

    /**
     * @Notes:检测仪器库三大记录添加的时间是否冲突
     * @param $instrumentId
     * @param $recordSetting
     * @param $currentTimeInfo
     * @return string
     * @author: wh
     * @DateTime: 2023/12/6 10:09
     */
    public function checkTimeConflict($instrumentId, $recordSetting, $currentTimeInfo)
    {

        //获取三大记录的时间
        $checkRecordTimeRange = InstrumentCheckRecordModel::find()->from(InstrumentCheckRecordModel::tableName())
            ->where(['instrument_id' => $instrumentId, 'status' => 1, 'if_void' => 0])
            ->select(['id', 'start_check_time', 'end_check_time', 'operator'])->asArray()->all();

        $repairRecordTimeRange = InstrumentRepairRecordModel::find()->from(InstrumentRepairRecordModel::tableName())
            ->where(['instrument_id' => $instrumentId, 'status' => 1, 'if_void' => 0])
            ->select(['id', 'repair_start_time', 'repair_end_time', 'operator'])->asArray()->all();

        $operateRecordTimeRange = InstrumentRunningRecordModel::find()->from(InstrumentRunningRecordModel::tableName())
            ->where(['instrument_id' => $instrumentId, 'status' => 1, 'if_void' => 0])
            ->select(['id', 'start_running_time', 'end_running_time', 'start_operator as operator'])->asArray()->all();

        //如果是空 返回的是false
        $startTime = strtotime($currentTimeInfo['start_time']);
        $endTime = strtotime($currentTimeInfo['end_time']);

        //获取记录创建人信息
        $operateRecordIds = array_unique(array_column($operateRecordTimeRange, 'operator'));
        $repairRecordIds = array_unique(array_column($repairRecordTimeRange, 'operator'));
        $checkRecordIds = array_unique(array_column($checkRecordTimeRange, 'operator'));
        $allOperateIds = array_unique(array_merge($operateRecordIds, $repairRecordIds, $checkRecordIds));
        $userList = (new CenterInterface())->userDetailsByUserIds($allOperateIds);
        $userList = ArrayHelper::index($userList, 'user_id');

        //初始化冲突数组
        $conflictInfoArr = [];
        $conflictInfoArr['check'] = [];
        $conflictInfoArr['repair'] = [];
        $conflictInfoArr['operate'] = [];
        $ifDealCheck = $ifDealRepair = $ifDealOperate = true;
        //判断当前检验的类型以及进行预处理 判断是否检测自身冲突以及清除记录中自己的数据
        switch ($currentTimeInfo['deal_type']) {
            case 'check':
                $ifDealCheck = false;
                if (!empty($recordSetting)) {
                    $recordSetting = json_decode($recordSetting, true);
                    $ifDealCheck = !empty($recordSetting['check_repeat']) && ($recordSetting['check_repeat'] == 'true');
                }
                if ($currentTimeInfo['current_id']) {
                    $checkRecordTimeRange = array_filter($checkRecordTimeRange, function ($element) use ($currentTimeInfo) {
                        return $element['id'] != $currentTimeInfo['current_id'];
                    });
                }
                break;
            case 'repair':
                $ifDealRepair = false;
                if (!empty($recordSetting)) {
                    $recordSetting = json_decode($recordSetting, true);
                    $ifDealRepair = !empty($recordSetting['repair_repeat']) && ($recordSetting['repair_repeat'] == 'true');
                }
                if ($currentTimeInfo['current_id']) {
                    $repairRecordTimeRange = array_filter($repairRecordTimeRange, function ($element) use ($currentTimeInfo) {
                        return $element['id'] != $currentTimeInfo['current_id'];
                    });
                }
                break;
            case 'operate':
                $ifDealOperate = false;
                if (!empty($recordSetting)) {
                    $recordSetting = json_decode($recordSetting, true);
                    $ifDealOperate = !empty($recordSetting['operate_repeat']) && ($recordSetting['operate_repeat'] == 'true');
                }
                if ($currentTimeInfo['current_id']) {
                    $operateRecordTimeRange = array_filter($operateRecordTimeRange, function ($element) use ($currentTimeInfo) {
                        return $element['id'] != $currentTimeInfo['current_id'];
                    });
                }
                break;
            default:
                break;
        }

        //得带冲突的数组
        if ($ifDealCheck) {
            foreach ($checkRecordTimeRange as $time) {
                $startCheckTime = strtotime($time['start_check_time']);
                $endCheckTime = strtotime($time['end_check_time']);
                if ($startCheckTime && $endCheckTime) { //只和记录中段时间比较
                    if ($startTime && $endTime) { //填写的是段时间
                        if ($endCheckTime >= $startTime && $startCheckTime <= $endTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['check'][] = $time;
                        }
                    }
                    if ($startTime && !$endTime) { //填写的是点时间
                        if ($startTime >= $startCheckTime && $startTime <= $endCheckTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['check'][] = $time;
                        }
                    }
                }
            }
        }
        if ($ifDealRepair) {

            foreach ($repairRecordTimeRange as $time) {
                $startRepairTime = strtotime($time['repair_start_time']);
                $endRepairTime = strtotime($time['repair_end_time']);
                if ($startRepairTime && $endRepairTime) { //只和记录中段时间比较
                    if ($startTime && $endTime) { //填写的是段时间
                        if ($endRepairTime >= $startTime && $startRepairTime <= $endTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['repair'][] = $time;
                        }
                    }
                    if ($startTime && !$endTime) { //填写的是点时间
                        if ($startTime >= $startRepairTime && $startTime <= $endRepairTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['repair'][] = $time;
                        }
                    }
                }
            }
        }
        if ($ifDealOperate) {
            foreach ($operateRecordTimeRange as $time) {
                $startOperateTime = strtotime($time['start_running_time']);
                $endOperateTime = strtotime($time['end_running_time']);
                if ($startOperateTime && $endOperateTime) { //只和记录中段时间比较
                    if ($startTime && $endTime) { //填写的是段时间
                        if ($endOperateTime >= $startTime && $startOperateTime <= $endTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['operate'][] = $time;
                        }
                    }
                    if ($startTime && !$endTime) { //填写的是点时间
                        if ($startTime >= $startOperateTime && $startTime <= $endOperateTime) {
                            $time['operator'] = CommentServer::displayUserName($userList[$time['operator']]);
                            $conflictInfoArr['operate'][] = $time;
                        }
                    }
                }
            }
        }
        return $this->initTimeConflictHtml($conflictInfoArr['check'], $conflictInfoArr['repair'], $conflictInfoArr['operate'], $currentTimeInfo['deal_type']);
    }

    /**
     * @Notes:初始化时间冲突信息展示
     * @param $checkArr
     * @param $repairArr
     * @param $operateArr
     * @param $dealType
     * @return string
     * @author: wh
     * @DateTime: 2023/12/6 10:10
     */
    public function initTimeConflictHtml($checkArr, $repairArr, $operateArr, $dealType)
    {
        $timeConflictHtml = '';
        if (empty($checkArr) && empty($repairArr) && empty($operateArr)) {
            return $timeConflictHtml;
        }

        $initOrder = [];
        $creatBy = \Yii::t('base', 'create_person');
        $checkRemind = \Yii::t('base', 'instrument_conflict_check');
        $repairRemind = \Yii::t('base', 'instrument_conflict_repair');
        $operateRemind = \Yii::t('base', 'instrument_conflict_operate');
        $checkTime = \Yii::t('base', 'check_time');
        $repairTime = \Yii::t('base', 'repair_time');
        $operateTime = \Yii::t('base', 'running_time');
        switch ($dealType) {
            case 'check':
                $timeConflictHtml = "<div>{$checkRemind}：</div>";
                $initOrder = ['check', 'repair', 'operate'];
                break;
            case 'repair':
                $timeConflictHtml = "<div>{$repairRemind}：</div>";
                $initOrder = ['repair', 'check', 'operate'];
                break;
            case 'operate':
                $timeConflictHtml = "<div>{$operateRemind}：</div>";
                $initOrder = ['operate', 'check', 'repair'];
                break;
            default:
                break;
        }
        $dealIndex = 0;
        while ($dealIndex < count($initOrder)) {
            switch ($initOrder[$dealIndex]) {
                case 'check':
                    foreach ($checkArr as $item) {
                        $timeConflictHtml .= "<div>{$checkTime}： {$item['start_check_time']} -- {$item['end_check_time']}  {$creatBy} : {$item['operator']}</div>";
                    }
                    break;
                case 'repair':
                    foreach ($repairArr as $item) {
                        $timeConflictHtml .= "<div>{$repairTime}： {$item['repair_start_time']} -- {$item['repair_end_time']}  {$creatBy} : {$item['operator']}</div>";
                    }
                    break;
                case 'operate':
                    foreach ($operateArr as $item) {
                        $timeConflictHtml .= "<div>{$operateTime}： {$item['start_running_time']} -- {$item['end_running_time']}  {$creatBy} : {$item['operator']}</div>";
                    }
                    break;
                default:
                    break;

            }
            $dealIndex++;
        }
        return $timeConflictHtml;

    }


    /**
     * @Notes:更新类名
     * @param $recordModel
     * @param $recordType
     * @param $instrumentId
     * @param $field
     * @param $fieldValue
     * @author: wh
     * @DateTime: 2023/12/8 13:56
     */
    public function updateInstrumentRecord($recordModel, $recordType, $instrumentId, $field, $fieldValue)
    {
        $filedRecord = $recordModel::find()->where(['instrument_id' => $instrumentId])->asArray()->one();
        $this->addInstrumentHistory($instrumentId, self::Action_ADJUST_COLUMN, ['action_details' => \Yii::t('base', $recordType) . ': ' . $filedRecord[$field] . '=>' . $fieldValue]);
        $recordModel::updateAll([$field => $fieldValue], ['instrument_id' => $instrumentId]);
    }

    /**
     * @Notes: 保存备注
     * @return array
     * @author: zwm
     * @DateTime: 2024/2/20 14:49:45
     */
    public function saveInscadaRemarkData($dataId, $dataType, $remarkData)
    {
        if ($dataType == 1) {
            $inscadaModel = new InstrumentDataNumericalModel;
        } elseif ($dataType == 2) {
            $inscadaModel = new InstrumentDataFileModel;
        }

        $detail = $inscadaModel::find()->where(['id' => $dataId])->one();
        $detail->setAttributes([
            'remark' => $remarkData
        ]);

        if (!$detail->save()) {
            return $this->fail($detail->getFirstErrors());
        }

        return $this->success([]);
    }

    /**
     * 用法一：只传$startTime（当前时间）和 $returnChecked，仪器库管理，我的仪器库 返回符合筛选项(已校验、未校验)的仪器id
     * 用法二：传递仪器id 开始时间和结束时间，判断是否在有效期内
     * @Notes 判断仪器是否在校验有效期内 返回在校验有效期（或者未校验）的仪器id
     * @param array $instrumentIds 仪器id的集合
     * @param null $startTime 开始时间（当前时间）
     * @param null $endTime 结束时间
     * @param bool $returnChecked 标定返回的仪器id 已校验或者未校验
     * @return array
     * <AUTHOR>
     * @DateTime: 2024/3/13 15:00
     */
    public function ifInExpiryTime($instrumentIds = [], $startTime = null, $endTime = null, $returnChecked = true)
    {
        //仪器库管理 筛选当前已校验的仪器id
        if (empty($instrumentIds) && $startTime) {
            $currentTime = $startTime;
            $instrumentCheckList = InstrumentCheckRecordModel::find()
                ->select('instrument_id')
                ->where([
                    'if_end' => 1,
                    'if_void' => 0,
                    'status' => 1,
                ])->andWhere([
                        'or',
                        ['review_conclusion' => null],
                        ['review_conclusion' => 1],
                    ])->andWhere([
                        'and',
                        ['<=', 'start_expiry_time', $currentTime],
                        ['>=', 'end_expiry_time', $currentTime]
                    ])->asArray()->all();
            // 无记录(bug#12544)
            if (empty($instrumentCheckList)) {
                if ($returnChecked) {
                    return [];
                }
                $instrumentList = InstrumentsModel::find()->select('id')
                    ->where([
                        'or',
                        ['check_situation' => 1],
                        ['check_situation' => 2],
                    ])->asArray()->all();
                return array_column($instrumentList, 'id');
            } else {
                $checkedIds = array_unique(array_column($instrumentCheckList, 'instrument_id'));
                if ($returnChecked) { //要求返回已经校验 且在有效期内的
                    return $checkedIds;
                } else { // 要求返回未校验
                    //（situation=1 且超过有效期） + （situation = 2）
                    $instrumentList = InstrumentsModel::find()->select('id')
                        ->where([
                            'or',
                            ['check_situation' => 1],
                            ['check_situation' => 2],
                        ])->asArray()->all();
                    if (count($instrumentList)) {
                        $allIds = array_unique(array_column($instrumentList, 'id'));
                        $instrumentIds = array_diff($allIds, $checkedIds);
                    }
                }
            }
            return $instrumentIds;
        }

        //运行编辑和结束 校正是否在有效期
        if (count($instrumentIds) && ($startTime || $endTime)) {
            $instrumentCheckList = InstrumentCheckRecordModel::find()
                ->where([
                    'if_end' => 1,
                    'if_void' => 0,
                    'status' => 1,
                    'instrument_id' => $instrumentIds,
                ])->andWhere([
                        'or',
                        ['review_conclusion' => null],
                        ['review_conclusion' => 1],
                    ])->asArray()->all();

            $inExpiryTimeIds = [];
            if (empty($instrumentCheckList)) { //如果没有校验记录，返回空数组，表明未校验
                return $inExpiryTimeIds;
            }

            $checkListGroupByIds = [];
            foreach ($instrumentCheckList as $checkItem) {
                $checkListGroupByIds[$checkItem['instrument_id']][] = $checkItem;
            }
            $inExpiryTimes = [];
            foreach ($checkListGroupByIds as $instrumentId => $checkList) {
                foreach ($checkList as $Item) {
                    $inExpiryTimes[] = [strtotime($Item['start_expiry_time']), strtotime($Item['end_expiry_time'])];
                }
                //合并有效期
                $mergeExpiryTimes = @merge_line_segment($inExpiryTimes);
                if ($startTime && $endTime) { //段时间校验
                    for ($i = count($mergeExpiryTimes) - 1; $i >= 0; $i--) {
                        if ($startTime >= $mergeExpiryTimes[$i][0] && $endTime <= $mergeExpiryTimes[$i][1]) {
                            $inExpiryTimeIds[] = $instrumentId;
                            break;
                        }
                    }
                }
                if ($startTime && empty($endTime)) { //点时间校验
                    for ($i = count($mergeExpiryTimes) - 1; $i >= 0; $i--) {
                        if ($startTime >= $mergeExpiryTimes[$i][0] && $startTime <= $mergeExpiryTimes[$i][1]) {
                            $inExpiryTimeIds[] = $instrumentId;
                            break;
                        }
                    }
                }
            }
            return $inExpiryTimeIds;
        }
        return $instrumentIds;
    }


    /**
     * @Notes 新建记录检查未完成的其余记录
     * @param $instrumentIds int 仪器的id
     * @param $newRecordType string 新建记录的类别
     * @param $companyId int 公司的id
     * @param $formAction string 新增或者恢复
     * @return array
     * <AUTHOR>
     * @DateTime: 2024/3/18 15:19
     */
    public function unFinishedRecord($instrumentIds, $newRecordType, $companyId, $formAction = 'add')
    {
        $checkRecords = [];
        $repairRecords = [];
        $runningRecords = [];
        //查询未完成的三大记录
        if ($newRecordType !== 'checkRecord') {
            $checkRecords = InstrumentCheckRecordModel::find()->where([
                'instrument_id' => $instrumentIds,
                'status' => 1,
                'if_void' => 0
            ])->andWhere([
                        'or',
                        ['if_end' => 0],
                        ['if_end' => null]
                    ])->asArray()->all();
        }
        if ($newRecordType !== 'runningRecord') {
            $runningRecords = InstrumentRunningRecordModel::find()->where([
                'instrument_id' => $instrumentIds,
                'status' => 1,
                'if_void' => 0
            ])->andWhere([
                        'or',
                        ['if_end' => 0],
                        ['if_end' => null]
                    ])->asArray()->all();
        }
        if ($newRecordType !== 'repairRecord') {
            $repairRecords = InstrumentRepairRecordModel::find()->where([
                'instrument_id' => $instrumentIds,
                'if_void' => 0,
                'status' => 1
            ])->andWhere([
                        'or',
                        ['if_end' => 0],
                        ['if_end' => null]
                    ])->asArray()->all();
        }
        //如果有未完成的三大记录
        if (count($repairRecords) || count($runningRecords) || count($checkRecords)) {
            $creatBy = \Yii::t('base', 'create_person');
            $checkTime = \Yii::t('base', 'check_time');
            $repairTime = \Yii::t('base', 'repair_time');
            $operateTime = \Yii::t('base', 'running_time');
            $unFinished = \Yii::t('base', 'on_going');
            if ($formAction == 'add') {
                $unFinishedRecords = \Yii::t('base', 'on_going_records');
            } else {
                $unFinishedRecords = \Yii::t('base', 'on_going_records2');
            }

            $userList = (new CenterInterface())->getUserListByCompanyId($companyId, true)['list'];
            $userList = ArrayHelper::index($userList, 'user_id');
            $unFinishedHtml = "<div>{$unFinishedRecords}</div>";
            foreach ($checkRecords as $checkRecord) {
                $operator = CommentServer::displayUserName($userList[$checkRecord['operator']]);
                $unFinishedHtml .= "<div>{$checkTime}： {$checkRecord['start_check_time']} -- <span style='color:red;'>({$unFinished})</span>  {$creatBy} : {$operator}</div>";
            }
            foreach ($repairRecords as $repairRecord) {
                $operator = CommentServer::displayUserName($userList[$repairRecord['operator']]);
                $unFinishedHtml .= "<div>{$repairTime}： {$repairRecord['repair_start_time']} -- <span style='color:red;'>({$unFinished})</span> {$creatBy} : {$operator}</div>";
            }
            foreach ($runningRecords as $runningRecord) {
                $operator = CommentServer::displayUserName($userList[$runningRecord['start_operator']]);
                $unFinishedHtml .= "<div>{$operateTime}： {$runningRecord['start_running_time']} -- <span style='color:red;'>({$unFinished})</span> {$creatBy} : {$operator}</div>";
            }
            return $this->fail($unFinishedHtml);
        }
        return $this->success('');


    }

    /**
     * @Notes: 修正当前时间校验情况
     * <AUTHOR>
     * @DateTime: 2024/5/13 11:17
     */
    public function fixCheckSituation()
    {
        $today = date("Y-m-d H:i");
        //校验有效期内的仪器id
        $checkedIds = $this->ifInExpiryTime([], $today);
        //不在校验有效期内的仪器id
        $unCheckedIds = $this->ifInExpiryTime([], $today, null, false);
        try {
            if (count($checkedIds)) {
                InstrumentsModel::updateAll(['check_situation' => 1], ['id' => $checkedIds]);
            }
            if (count($unCheckedIds)) {
                InstrumentsModel::updateAll(['check_situation' => 2], ['id' => $unCheckedIds]);
            }
        } catch (Exception $e) {
            \Yii::info('updateAll failed: ' . $e->getMessage());
        }
    }

    /**
     * Note: 项目管理获取仪器列表
     * Author: zhouweiming
     * Date: 2024/8/23 下午1:52
     * @param $where
     * @return array
     *
     */
    public function listAllInstrumentsForProject($where)
    {
        $query = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $query->select[] = 'Ins.id';
        $query->select[] = 'Ins.name AS name'; // 增加中文排序
        $query->select[] = 'Ins.groupIds';
        $query->select[] = 'Ins.departmentIds';
        if ($where['viewAuth'] == '2') { // 只能查看可见鹰群和我的部门仪器
            $FindStr = "";
            foreach ($where['groups'] as $key => $groupId) { // 所有可见鹰群 -- ins.group
                $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
                $query->addParams([':groupId' . $key => $groupId]);
            }
            foreach ($where['departments'] as $key => $departmentId) {
                $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
                $query->addParams([':departmentId' . $key => $departmentId]);
            }

            $FindStr .= "FIND_IN_SET('all', Ins.groupIds)";
            $query->andWhere(new Expression($FindStr));
        }
        $orderString = 'name asc';
        $query->orderBy($orderString);
        return $query->asArray()->all();
    }

    /**
     * Note: 获取仪器列表与仪器预约信息集合
     * Author: ChenQi
     * Date: 2025/05/25
     * @param $where
     * @return array
     *
     */
    public function queryInstrumentsWithBooking($where, $limit, $page)
    {
        // 获取当前用户ID
        $currentUserId = \Yii::$app->view->params['curr_user_id'];
        $editInstruments = \Yii::$app->view->params['instruments_manage_write'];
        // 第一步：通过JOIN查询当前用户预约过的仪器，按预约创建时间倒序
        $myBookedQuery = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
        $myBookedQuery->innerJoin(InstrumentsBookModel::tableName() . ' IB', 'Ins.id = IB.instrument_id');
        
        // 获取当前用户语言
        $lang = $where['lang'];
        // 选择仪器基本信息和最新预约创建时间
        $myBookedQuery->select([
            'Ins.id',
            'Ins.name',
            'Ins.groupIds',
            'Ins.departmentIds',
            'Ins.responsible_person',
            'Ins.position',
            'Ins.available_slots',
            'Ins.max_advance_day',
            'Ins.max_booking_duration',
            'Ins.min_advance',
            'Ins.model',
            'Ins.pictures',
            'Ins.create_time',
            'MAX(IB.create_time) as latest_booking_time'
        ]);

        // 基础过滤条件
        $myBookedQuery->andWhere(['Ins.status' => '1']);
        $myBookedQuery->andWhere(['IB.create_by' => $currentUserId]);

        // 仪器名称筛选
        if (!empty($where['instrumentName'])) {
            $myBookedQuery->andWhere(['like', 'Ins.name', $where['instrumentName']]);
        }

        // 权限过滤
        if ($where['viewAuth'] == '2') {
            $this->applyAuthFilter($myBookedQuery, $where);
        }

        $myBookedQuery->groupBy('Ins.id');
        $myBookedQuery->orderBy('latest_booking_time desc');

        // 先获取我预约过的仪器，用于分页计算
        $myBookedTotal = $myBookedQuery->asArray()->all();
        $myBookedTotalCount = count($myBookedTotal);
        $startIndex = ($page - 1) * $limit;
        if (!$where['onlyBooked']) {
            // 第二步：查询其他仪器的总数
            $otherQuery = InstrumentsModel::find()->from(InstrumentsModel::tableName() . ' Ins');
            $otherQuery->andWhere(['Ins.status' => '1']);

            // 仪器名称筛选
            if (!empty($where['instrumentName'])) {
                $otherQuery->andWhere(['like', 'Ins.name', $where['instrumentName']]);
            }

            // 权限过滤
            if ($where['viewAuth'] == '2') {
                $this->applyAuthFilter($otherQuery, $where);
            }

            // 如果有我预约的仪器，需要排除它们
            if ($myBookedTotalCount > 0) {
                $myBookedIds = array_column($myBookedTotal, 'id');

                if (!empty($myBookedIds)) {
                    $otherQuery->andWhere(['not in', 'Ins.id', $myBookedIds]);
                }
            }
            $otherTotalCount = $otherQuery->count();

            // 第三步：根据分页计算需要查询的数据
            $myBookedInstruments = [];
            $otherInstruments = [];

            if ($startIndex < $myBookedTotalCount) {
                // 当前页需要显示我预约的仪器
                // 直接从已有的 $myBookedTotal 数组中获取分页数据
                $myBookedInstruments = array_slice($myBookedTotal, $startIndex, $limit);

                $myBookedPageCount = count($myBookedInstruments);
                $needOtherCount = $limit - $myBookedPageCount;

                // 如果当前页还有空间，补充其他仪器
                if ($needOtherCount > 0 && $otherTotalCount > 0) {
                    $otherQuery->select([
                        'Ins.id',
                        'Ins.name',
                        'Ins.groupIds',
                        'Ins.departmentIds',
                        'Ins.responsible_person',
                        'Ins.position',
                        'Ins.available_slots',
                        'Ins.max_advance_day',
                        'Ins.max_booking_duration',
                        'Ins.min_advance',
                        'Ins.model',
                        'Ins.pictures',
                        'Ins.create_time'
                    ]);
                    $otherQuery->orderBy('Ins.create_time desc');
                    $otherQuery->limit($needOtherCount);
                    $otherInstruments = $otherQuery->asArray()->all();
                }
            } else {
                // 当前页只显示其他仪器
                $otherStartIndex = $startIndex - $myBookedTotalCount;
                $otherQuery->select([
                    'Ins.id',
                    'Ins.name',
                    'Ins.groupIds',
                    'Ins.departmentIds',
                    'Ins.responsible_person',
                    'Ins.position',
                    'Ins.available_slots',
                    'Ins.max_advance_day',
                    'Ins.max_booking_duration',
                    'Ins.min_advance',
                    'Ins.model',
                    'Ins.pictures',
                    'Ins.create_time'
                ]);
                $otherQuery->orderBy('Ins.create_time desc');
                $otherQuery->offset($otherStartIndex)->limit($limit);
                $otherInstruments = $otherQuery->asArray()->all();
            }

            // 合并结果
            $instruments = array_merge($myBookedInstruments, $otherInstruments);
        } else {
            // 只查看已预约过的仪器时，不用获取其他仪器的信息
            $instruments = array_slice($myBookedTotal, $startIndex, $limit);
            $otherTotalCount = 0;
        }
        $uniqueUserIds = array_unique(array_column($instruments, 'responsible_person'));
        if (empty($instruments)) {
            return [];
        }

        // 获取当前页仪器的ID列表
        $instrumentIds = array_column($instruments, 'id');

        // 第四步：查询当前页仪器的预约记录
        $bookingQuery = InstrumentsBookModel::find()->from(InstrumentsBookModel::tableName() . ' IB');
        $bookingQuery->select([
            'IB.id AS booking_id',
            'IB.instrument_id',
            'IB.start_time',
            'IB.end_time',
            'IB.status AS booking_status',
            'IB.create_by',
            'IB.create_time AS booking_create_time',
            'IB.related_experiment',
            'IB.remark AS booking_remark',
            'IB.reminder'
        ]);

        // 限制查询的仪器ID
        $bookingQuery->andWhere(['IB.instrument_id' => $instrumentIds]);

        // 时间范围过滤条件
        if (isset($where['bookTime'])) {
            $this->applyTimeFilter($bookingQuery, $where['bookTime']);
        }

        // 按预约开始时间排序
        $bookingQuery->orderBy('IB.start_time asc');
        $bookings = $bookingQuery->asArray()->all();

        // 第五步：将预约记录绑定到仪器信息上
        $bookingsByInstrument = [];
        foreach ($bookings as $booking) {
            if (!in_array($booking['create_by'], $uniqueUserIds)) {
                $uniqueUserIds[] = $booking['create_by'];
            }
            $booking['currentBooked'] = $booking['create_by'] === $currentUserId ? 1: 0;
            $instrumentId = $booking['instrument_id'];
            if (!isset($bookingsByInstrument[$instrumentId])) {
                $bookingsByInstrument[$instrumentId] = [];
            }
            // 移除instrument_id字段，避免重复
            unset($booking['instrument_id']);
            $bookingsByInstrument[$instrumentId][] = $booking;
        }
        $usersRes = (new CenterInterface())->userDetailsByUserIds($uniqueUserIds);
        $userDetailArr = ArrayHelper::index($usersRes, 'id');
        // 将预约记录绑定到仪器信息上
        $instruments = array_map(function($instrument) use ($bookingsByInstrument, $userDetailArr, $lang, $editInstruments) {
            $instrumentId = $instrument['id'];
            if(isset($bookingsByInstrument[$instrumentId])){
                $instrument['bookedInfo'] = array_map(function($booking) use ($instrument, $userDetailArr, $lang, $editInstruments) {
                    if (!empty($instrument['model'])) {
                        $booking['instrument'] = $instrument['name'] . '(' . $instrument['model'] . ')';
                    } else {
                        $booking['instrument'] = $instrument['name'];
                    }
                    $user = @getVar($userDetailArr[$booking['create_by']], null);
                    $userName = $user ? CommonServer::displayUserName($user) : '';
                    // 获取当前时间并与预约结束时间比较
                    var_dump(time());
                    var_dump(strtotime($booking['end_time']));
                    $notExpired = time() < strtotime($booking['end_time']);
                    if($notExpired&&($booking['currentBooked']===1||$editInstruments===1)){
                        $btnShow = true;
                    } else {
                        $btnShow = false;
                    }
                    return [
                        'experimenter' => $userName,
                        'bookingId' => $booking['booking_id'],
                        'instrument' => $booking['instrument'],
                        'bookedTime' => [$booking['start_time'] , $booking['end_time']],
                        'reminder' => $this->formatReminderText($booking['reminder'], $lang),
                        'ELNPage' => !empty($booking['related_experiment']) ? explode(',', $booking['related_experiment']) : null,
                        'remark' => $booking['booking_remark'],
                        'currentBooked' => $booking['currentBooked'],
                        'btnShow'=> $btnShow
                    ];
                }, $bookingsByInstrument[$instrumentId]);

            } else{
                $instrument['bookedInfo'] = [];
            }
            $user = @getVar($userDetailArr[$instrument['responsible_person']], null);
            $userName = $user ? CommonServer::displayUserName($user) : '';
            $decoded = json_decode($instrument['pictures'], true);
            $imgData = !empty($decoded[0]) ? $decoded[0] : '';
            $pictureSrc = $imgData != '' ? PIC_URL . (new Picture())->getSourcePicture($imgData['dep_path'], $imgData['save_name']) : '';
            // 移除临时排序字段
            unset($instrument['latest_booking_time']);
            return [
                'instrumentName' => $instrument['name'],
                'instrumentCode' => $instrument['model'],
                'instrumentId' => $instrument['id'],
                'bookedInfo' => $instrument['bookedInfo'],
                'state' => '正常',
                'responsiblePerson' => $userName,
                'position' => $instrument['position'],
                'singleTimeLimit' => $this->formatAdvanceData($instrument['max_booking_duration']),
                'advance' => $this->formatAdvanceData($instrument['min_advance']),
                'bookableTime' => $this->formatTimeSlots($instrument['available_slots']),
                'picture' => $pictureSrc,
                'available_slots' => $instrument['available_slots'],
                'max_advance_day' => $instrument['max_advance_day'],
                'min_advance' => $instrument['min_advance'],
                'max_booking_duration' => $instrument['max_booking_duration']
            ];
        }, $instruments);
        return [
            'instruments' => $instruments,
            'totalCount' => $myBookedTotalCount + $otherTotalCount
        ];
    }

     /**
     * 格式化时间和时间单位的json对象
     */
    private function formatAdvanceData($advanceTime)
    {
        if (empty($advanceTime)) {
            return '';
        }

        $obj = json_decode($advanceTime);
        $time = $obj->unit;
        if ($time === 'min') {
            $time = '分钟';
        } else if($time === 'hour'){
            $time = '小时';
        } else if($time === 'day'){
            $time = '天';
        }
        return $obj->value . $time;
    }

    /**
     * 格式化提醒时间
     */
    private function formatReminderText($reminder, $lang)
    {
        $reminderMapCn = [
            0 => '不提醒',
            1 => '提前5分钟提醒',
            2 => '提前15分钟提醒',
            3 => '提前30分钟提醒',
            4 => '提前1小时提醒',
            5 => '提前2小时提醒',
            6 => '提前1天提醒'
        ];

        $reminderMapEn = [
            0 => 'No reminder',
            1 => 'Remind 5 minutes before',
            2 => 'Remind 15 minutes before',
            3 => 'Remind 30 minutes before',
            4 => 'Remind 1 hour before',
            5 => 'Remind 2 hours before',
            6 => 'Remind 1 day before'
        ];
        $reminderMap = $lang === 'cn' ? $reminderMapCn : $reminderMapEn;        
        return isset($reminderMap[$reminder]) ? $reminderMap[$reminder] : $reminderMap[0];         // 若没有设置提醒，则返回默认值“不提醒”
    }

    /**
     * 格式化时间段
     */
    private function formatTimeSlots($available_slots)
    {
        if (empty($available_slots)) {
            return [];
        }

        $slots = json_decode($available_slots, true);
        if (!is_array($slots)) {
            return [];
        }

        return array_map(function($slot) {
            return isset($slot[0], $slot[1]) ? $slot[0] . '-' . $slot[1] : '';
        }, $slots);
    }

    /**
     * 应用权限过滤
     */
    private function applyAuthFilter($query, $where)
    {
        $FindStr = "";
        foreach ($where['groups'] as $key => $groupId) {
            $FindStr .= "FIND_IN_SET(:groupId" . $key . ", Ins.groupIds) or ";
            $query->addParams([':groupId' . $key => $groupId]);
        }
        foreach ($where['departments'] as $key => $departmentId) {
            $FindStr .= "FIND_IN_SET(:departmentId" . $key . ", Ins.departmentIds) or ";
            $query->addParams([':departmentId' . $key => $departmentId]);
        }
        $FindStr .= "FIND_IN_SET('all', Ins.groupIds)";
        $query->andWhere(new Expression($FindStr));
    }

    /**
     * 应用时间范围过滤
     */
    private function applyTimeFilter($query, $bookTime)
    {
        $startTime = $bookTime[0] . ' 00:00:00';
        $endTime = $bookTime[1] . ' 23:59:59';
        // 条件：预约开始时间 <= 当天结束时间 AND 预约结束时间 >= 当天开始时间
        $query->andWhere(['<=', 'IB.start_time', $endTime]);
        $query->andWhere(['>=', 'IB.end_time', $startTime]);
    }

}