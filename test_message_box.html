<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除确认弹框</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 50px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* 自定义删除确认弹框样式 */
        .delete-booking-confirm {
            border-radius: 12px !important;
            padding: 24px !important;
            min-width: 320px !important;
        }

        .delete-booking-confirm .el-message-box__header {
            padding-bottom: 16px !important;
            border-bottom: none !important;
        }

        .delete-booking-confirm .el-message-box__title {
            font-size: 16px !important;
            font-weight: 500 !important;
            color: #303133 !important;
            line-height: 24px !important;
        }

        .delete-booking-confirm .el-message-box__content {
            padding: 16px 0 24px 0 !important;
            color: #606266 !important;
            font-size: 14px !important;
            line-height: 20px !important;
        }

        .delete-booking-confirm .el-message-box__btns {
            padding: 0 !important;
            text-align: right !important;
        }

        .delete-booking-confirm .el-button {
            margin-left: 12px !important;
            padding: 8px 16px !important;
            border-radius: 6px !important;
            font-size: 14px !important;
        }

        .delete-booking-confirm .el-button--default {
            color: #606266 !important;
            border-color: #DCDFE6 !important;
            background-color: #FFFFFF !important;
        }

        .delete-booking-confirm .el-button--primary {
            background-color: #FF6B47 !important;
            border-color: #FF6B47 !important;
            color: #FFFFFF !important;
        }

        .delete-booking-confirm .el-button--primary:hover {
            background-color: #FF5722 !important;
            border-color: #FF5722 !important;
        }

        .delete-booking-confirm .el-message-box__close {
            color: #C0C4CC !important;
            font-size: 16px !important;
        }

        .delete-booking-confirm .el-message-box__close:hover {
            color: #909399 !important;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h2>删除确认弹框测试</h2>
            <p>点击下面的按钮测试删除确认弹框的样式和功能：</p>
            
            <el-button type="danger" @click="showDeleteConfirm">
                测试删除弹框
            </el-button>
            
            <div style="margin-top: 20px;">
                <h3>弹框信息：</h3>
                <p><strong>实验者：</strong>天平</p>
                <p><strong>时间：</strong>2025年1月13日 9:00 - 12:00</p>
                <p><strong>操作：</strong>撤销预约</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElButton, ElMessageBox, ElMessage } = ElementPlus;

        createApp({
            components: {
                ElButton
            },
            methods: {
                async showDeleteConfirm() {
                    try {
                        // 构建确认删除的消息内容
                        const confirmMessage = `撤销对 天平 于 2025年1月13日 9:00 - 12:00 的预约`;
                        
                        // 显示确认删除弹框
                        await ElMessageBox.confirm(
                            confirmMessage,
                            '确定要撤销此预约吗',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                                customClass: 'delete-booking-confirm',
                                showClose: true,
                                closeOnClickModal: false,
                                closeOnPressEscape: true
                            }
                        );
                        
                        // 用户确认后的操作
                        ElMessage({
                            message: '预约已成功撤销',
                            type: 'success'
                        });
                        
                    } catch (error) {
                        // 用户取消删除
                        console.log('用户取消了删除操作');
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
