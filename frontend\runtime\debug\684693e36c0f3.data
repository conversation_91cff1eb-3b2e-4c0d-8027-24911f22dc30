a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:59:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:53:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:57:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:12:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"46";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:16:"application/json";s:12:"content-type";s:16:"application/json";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:258:"eln_page_limit=15; lock_interval=180; ldap_check=0; integle_session=f8vk9hvml0inf5jip4j452di63; group_ids=; department_ids=; role_ids=all; sims_u=7f2fa82b3d0ac8c728f119ebd3deffc7; user_ids=1148; selectedUsersNames=cqtest1(cq1); last_active_time=**********264";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:25:"instrument/get-book-by-id";s:6:"action";s:62:"frontend\controllers\InstrumentController::actionGetBookById()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:46:"{"id":"2655","day":"2025-06-09T06:44:50.342Z"}";s:17:"Decoded to Params";a:2:{s:2:"id";s:4:"2655";s:3:"day";s:24:"2025-06-09T06:44:50.342Z";}}s:6:"SERVER";a:45:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"46";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:16:"application/json";s:12:"CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:258:"eln_page_limit=15; lock_interval=180; ldap_check=0; integle_session=f8vk9hvml0inf5jip4j452di63; group_ids=; department_ids=; role_ids=all; sims_u=7f2fa82b3d0ac8c728f119ebd3deffc7; user_ids=1148; selectedUsersNames=cqtest1(cq1); last_active_time=**********264";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:52:"D:/integle2025/eln_5.3.11_dev/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"65361";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:27:"r=instrument/get-book-by-id";s:11:"REQUEST_URI";s:29:"/?r=instrument/get-book-by-id";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.2809999;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:25:"instrument/get-book-by-id";}s:4:"POST";a:0:{}s:6:"COOKIE";a:11:{s:14:"eln_page_limit";s:2:"15";s:13:"lock_interval";s:3:"180";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"f8vk9hvml0inf5jip4j452di63";s:9:"group_ids";s:0:"";s:14:"department_ids";s:0:"";s:8:"role_ids";s:3:"all";s:6:"sims_u";s:32:"7f2fa82b3d0ac8c728f119ebd3deffc7";s:8:"user_ids";s:4:"1148";s:18:"selectedUsersNames";s:12:"cqtest1(cq1)";s:16:"last_active_time";s:13:"**********264";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1148";s:5:"email";N;s:4:"name";s:3:"cq1";s:5:"phone";N;s:6:"ticket";s:32:"7f2fa82b3d0ac8c728f119ebd3deffc7";s:8:"reg_time";s:10:"1744270466";s:5:"Token";s:32:"65df2c5aec757b20d496ba82fc315e25";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:7:"cqtest1";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"0";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,85";s:10:"department";a:1:{i:0;a:22:{s:2:"id";s:3:"381";s:13:"department_id";s:3:"381";s:7:"user_id";s:4:"1148";s:6:"status";s:1:"1";s:11:"create_time";s:19:"2025-04-10 09:41:02";s:11:"update_time";s:19:"2025-04-10 15:41:03";s:10:"company_id";s:1:"0";s:15:"department_name";s:8:"CQ部门";s:15:"department_code";s:19:"2.1777226530103E+18";s:15:"department_type";s:1:"1";s:6:"tel_no";s:0:"";s:6:"fax_no";s:0:"";s:18:"department_address";s:0:"";s:13:"department_no";s:1:"0";s:17:"parent_department";s:1:"0";s:7:"manager";s:4:"1135";s:11:"leader_main";s:0:"";s:13:"leader_branch";s:0:"";s:9:"assistant";s:4:"1148";s:14:"branch_manager";s:0:"";s:6:"remark";N;s:7:"ad_guid";N;}}s:2:"id";s:4:"1148";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"1";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:26:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.298934;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.3074031;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.4431491;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.443779;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.444809;i:4;a:0:{}}i:5;a:5:{i:0;s:44:"Route requested: 'instrument/get-book-by-id'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.4448271;i:4;a:0:{}}i:6;a:5:{i:0;s:50:"请求数据为:{"r":"instrument\/get-book-by-id"}";i:1;i:4;i:2;s:7:"Request";i:3;d:**********.5360589;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:132;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}}i:7;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.6948709;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.71784;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7183349;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.718641;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.719044;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.719094;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.719368;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.7204499;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.733407;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7340281;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.7344041;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:18;a:5:{i:0;s:39:"Route to run: instrument/get-book-by-id";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.736445;i:4;a:0:{}}i:19;a:5:{i:0;s:78:"Running action: frontend\controllers\InstrumentController::actionGetBookById()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.7388389;i:4;a:0:{}}i:20;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.757035;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:612:"SELECT `BOOK`.`instrument_id`, `BOOK`.`id`, `BOOK`.`start_time`, `BOOK`.`end_time`, `BOOK`.`related_experiment`, `BOOK`.`create_time`, `BOOK`.`remark`, `BOOK`.`create_by`, `Ins`.`name` AS `instrument_name`, `Ins`.`batch_number` AS `instrument_batch_number`, `Ins`.`specification` AS `instrument_specification`, `Ins`.`model` AS `instrument_model` FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.780668;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:26;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.782058;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.784668;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:281:"SELECT COUNT(*) FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7855101;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1588;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9592509;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:15569592;s:4:"time";d:0.74169588088989258;s:8:"messages";a:10:{i:21;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.7570651;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.780611;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:612:"SELECT `BOOK`.`instrument_id`, `BOOK`.`id`, `BOOK`.`start_time`, `BOOK`.`end_time`, `BOOK`.`related_experiment`, `BOOK`.`create_time`, `BOOK`.`remark`, `BOOK`.`create_by`, `Ins`.`name` AS `instrument_name`, `Ins`.`batch_number` AS `instrument_batch_number`, `Ins`.`specification` AS `instrument_specification`, `Ins`.`model` AS `instrument_model` FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.780694;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:612:"SELECT `BOOK`.`instrument_id`, `BOOK`.`id`, `BOOK`.`start_time`, `BOOK`.`end_time`, `BOOK`.`related_experiment`, `BOOK`.`create_time`, `BOOK`.`remark`, `BOOK`.`create_by`, `Ins`.`name` AS `instrument_name`, `Ins`.`batch_number` AS `instrument_batch_number`, `Ins`.`specification` AS `instrument_specification`, `Ins`.`model` AS `instrument_model` FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.781563;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7820871;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.783699;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.784698;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785378;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:281:"SELECT COUNT(*) FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785531;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1588;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:281:"SELECT COUNT(*) FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.786052;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1588;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:8:{i:24;a:5:{i:0;s:612:"SELECT `BOOK`.`instrument_id`, `BOOK`.`id`, `BOOK`.`start_time`, `BOOK`.`end_time`, `BOOK`.`related_experiment`, `BOOK`.`create_time`, `BOOK`.`remark`, `BOOK`.`create_by`, `Ins`.`name` AS `instrument_name`, `Ins`.`batch_number` AS `instrument_batch_number`, `Ins`.`specification` AS `instrument_specification`, `Ins`.`model` AS `instrument_model` FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.780694;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:612:"SELECT `BOOK`.`instrument_id`, `BOOK`.`id`, `BOOK`.`start_time`, `BOOK`.`end_time`, `BOOK`.`related_experiment`, `BOOK`.`create_time`, `BOOK`.`remark`, `BOOK`.`create_by`, `Ins`.`name` AS `instrument_name`, `Ins`.`batch_number` AS `instrument_batch_number`, `Ins`.`specification` AS `instrument_specification`, `Ins`.`model` AS `instrument_model` FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.781563;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.7820871;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:41:"SHOW FULL COLUMNS FROM `instruments_book`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.783699;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.784698;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:619:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments_book' AND kcu.table_name = 'instruments_book'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785378;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1585;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:281:"SELECT COUNT(*) FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.785531;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1588;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:281:"SELECT COUNT(*) FROM `instruments_book` `BOOK` LEFT JOIN `instruments` `Ins` ON BOOK.instrument_id = Ins.id WHERE (`BOOK`.`instrument_id`='2655') AND ((`BOOK`.`start_time` <= '2025-06-09 23:59:59') OR (`BOOK`.`end_time` >= '2025-06-09 00:00:00')) ORDER BY `BOOK`.`create_time` DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.786052;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1588;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:1274;s:8:"function";s:12:"listBookById";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"684693e36c0f3";s:3:"url";s:55:"http://dev.eln.integle.com/?r=instrument/get-book-by-id";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1749455844;s:10:"statusCode";i:200;s:8:"sqlCount";i:4;s:9:"mailCount";i:0;}}