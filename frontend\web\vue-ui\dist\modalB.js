import { createElementBlock as l, openBlock as r, createElementVNode as t } from "vue";
import { _ as c } from "./_plugin-vue_export-helper.js";
const n = {
  methods: {
    close() {
      this.$emit("close");
    }
  }
}, d = { class: "modal-b" };
function a(m, e, _, i, p, o) {
  return r(), l("div", d, [
    e[1] || (e[1] = t("h2", null, "弹窗 B", -1)),
    t("button", {
      onClick: e[0] || (e[0] = (...s) => o.close && o.close(...s))
    }, "关闭")
  ]);
}
const B = /* @__PURE__ */ c(n, [["render", a], ["__scopeId", "data-v-041780c7"]]);
export {
  B as default
};
