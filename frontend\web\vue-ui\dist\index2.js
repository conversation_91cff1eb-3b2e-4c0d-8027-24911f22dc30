import { computed as c, unref as t, getCurrentInstance as ne, inject as Ze, ref as U, provide as Je, defineComponent as Te, useAttrs as Xe, useSlots as Qe, shallowRef as X, watch as Q, nextTick as M, onMounted as qe, toRef as et, createElementBlock as x, openBlock as v, normalizeStyle as we, normalizeClass as y, createCommentVNode as m, Fragment as q, createElementVNode as V, renderSlot as _, createBlock as C, withCtx as L, resolveDynamicComponent as D, mergeProps as Ce, withModifiers as tt, createVNode as ot, toDisplayString as j } from "vue";
import { aF as at, J as nt, n as oe, aG as st, Z as lt, aH as rt, aI as it, aJ as ut, aK as ct, aL as dt, aM as pt, aN as ft, O as Fe, P as Se, q as ee, h as vt, U as ae, T as mt, j as te, k as Ie, m as yt, aO as gt, _ as ht, f as bt, g as xt, a0 as wt, u as Ct, V as St, v as Ee, a2 as It, aP as Et, aQ as zt, aR as Nt, B as Pt, $ as Vt, E as R, ay as kt, a8 as Tt, Y as ze, ax as Ne, Q as Pe, w as Ft } from "./el-input.js";
const Oe = Symbol();
function Ot(a, i, n) {
  return a == null ? a : at(a, i, n);
}
const Ve = (a) => Object.keys(a), eo = (a, i, n) => ({
  get value() {
    return nt(a, i, n);
  },
  set value(o) {
    Ot(a, i, o);
  }
}), W = U();
function Me(a, i = void 0) {
  const n = ne() ? Ze(Oe, W) : W;
  return a ? c(() => {
    var o, s;
    return (s = (o = n.value) == null ? void 0 : o[a]) != null ? s : i;
  }) : n;
}
function to(a, i) {
  const n = Me(), o = oe(a, c(() => {
    var u;
    return ((u = n.value) == null ? void 0 : u.namespace) || st;
  })), s = lt(c(() => {
    var u;
    return (u = n.value) == null ? void 0 : u.locale;
  })), p = rt(c(() => {
    var u;
    return ((u = n.value) == null ? void 0 : u.zIndex) || it;
  })), l = c(() => {
    var u;
    return t(i) || ((u = n.value) == null ? void 0 : u.size) || "";
  });
  return Mt(c(() => t(n) || {})), {
    ns: o,
    locale: s,
    zIndex: p,
    size: l
  };
}
const Mt = (a, i, n = !1) => {
  var o;
  const s = !!ne(), p = s ? Me() : void 0, l = (o = void 0) != null ? o : s ? Je : void 0;
  if (!l)
    return;
  const u = c(() => {
    const f = t(a);
    return p != null && p.value ? Lt(p.value, f) : f;
  });
  return l(Oe, u), l(ut, c(() => u.value.locale)), l(ct, c(() => u.value.namespace)), l(dt, c(() => u.value.zIndex)), l(pt, {
    size: c(() => u.value.size || "")
  }), l(ft, c(() => ({
    emptyValues: u.value.emptyValues,
    valueOnClear: u.value.valueOnClear
  }))), (n || !W.value) && (W.value = u.value), u;
}, Lt = (a, i) => {
  const n = [.../* @__PURE__ */ new Set([...Ve(a), ...Ve(i)])], o = {};
  for (const s of n)
    o[s] = i[s] !== void 0 ? i[s] : a[s];
  return o;
}, Rt = () => Fe && /firefox/i.test(window.navigator.userAgent);
let b;
const Bt = {
  height: "0",
  visibility: "hidden",
  overflow: Rt() ? "" : "hidden",
  position: "absolute",
  "z-index": "-1000",
  top: "0",
  right: "0"
}, Ht = [
  "letter-spacing",
  "line-height",
  "padding-top",
  "padding-bottom",
  "font-family",
  "font-weight",
  "font-size",
  "text-rendering",
  "text-transform",
  "width",
  "text-indent",
  "padding-left",
  "padding-right",
  "border-width",
  "box-sizing"
];
function Kt(a) {
  const i = window.getComputedStyle(a), n = i.getPropertyValue("box-sizing"), o = Number.parseFloat(i.getPropertyValue("padding-bottom")) + Number.parseFloat(i.getPropertyValue("padding-top")), s = Number.parseFloat(i.getPropertyValue("border-bottom-width")) + Number.parseFloat(i.getPropertyValue("border-top-width"));
  return { contextStyle: Ht.map((l) => [
    l,
    i.getPropertyValue(l)
  ]), paddingSize: o, borderSize: s, boxSizing: n };
}
function ke(a, i = 1, n) {
  var o;
  b || (b = document.createElement("textarea"), document.body.appendChild(b));
  const { paddingSize: s, borderSize: p, boxSizing: l, contextStyle: u } = Kt(a);
  u.forEach(([h, k]) => b == null ? void 0 : b.style.setProperty(h, k)), Object.entries(Bt).forEach(([h, k]) => b == null ? void 0 : b.style.setProperty(h, k, "important")), b.value = a.value || a.placeholder || "";
  let f = b.scrollHeight;
  const S = {};
  l === "border-box" ? f = f + p : l === "content-box" && (f = f - s), b.value = "";
  const g = b.scrollHeight - s;
  if (Se(i)) {
    let h = g * i;
    l === "border-box" && (h = h + s + p), f = Math.max(h, f), S.minHeight = `${h}px`;
  }
  if (Se(n)) {
    let h = g * n;
    l === "border-box" && (h = h + s + p), f = Math.min(h, f);
  }
  return S.height = `${f}px`, (o = b.parentNode) == null || o.removeChild(b), b = void 0, S;
}
const $t = (a) => a, At = vt({
  id: {
    type: String,
    default: void 0
  },
  size: yt,
  disabled: Boolean,
  modelValue: {
    type: te([
      String,
      Number,
      Object
    ]),
    default: ""
  },
  maxlength: {
    type: [String, Number]
  },
  minlength: {
    type: [String, Number]
  },
  type: {
    type: String,
    default: "text"
  },
  resize: {
    type: String,
    values: ["none", "both", "horizontal", "vertical"]
  },
  autosize: {
    type: te([Boolean, Object]),
    default: !1
  },
  autocomplete: {
    type: String,
    default: "off"
  },
  formatter: {
    type: Function
  },
  parser: {
    type: Function
  },
  placeholder: {
    type: String
  },
  form: {
    type: String
  },
  readonly: Boolean,
  clearable: Boolean,
  showPassword: Boolean,
  showWordLimit: Boolean,
  suffixIcon: {
    type: Ie
  },
  prefixIcon: {
    type: Ie
  },
  containerRole: {
    type: String,
    default: void 0
  },
  tabindex: {
    type: [String, Number],
    default: 0
  },
  validateEvent: {
    type: Boolean,
    default: !0
  },
  inputStyle: {
    type: te([Object, Array, String]),
    default: () => $t({})
  },
  autofocus: Boolean,
  rows: {
    type: Number,
    default: 2
  },
  ...mt(["ariaLabel"])
}), _t = {
  [ae]: (a) => ee(a),
  input: (a) => ee(a),
  change: (a) => ee(a),
  focus: (a) => a instanceof FocusEvent,
  blur: (a) => a instanceof FocusEvent,
  clear: () => !0,
  mouseleave: (a) => a instanceof MouseEvent,
  mouseenter: (a) => a instanceof MouseEvent,
  keydown: (a) => a instanceof Event,
  compositionstart: (a) => a instanceof CompositionEvent,
  compositionupdate: (a) => a instanceof CompositionEvent,
  compositionend: (a) => a instanceof CompositionEvent
}, Dt = ["class", "style"], jt = /^on[A-Z]/, Ut = (a = {}) => {
  const { excludeListeners: i = !1, excludeKeys: n } = a, o = c(() => ((n == null ? void 0 : n.value) || []).concat(Dt)), s = ne();
  return s ? c(() => {
    var p;
    return gt(Object.entries((p = s.proxy) == null ? void 0 : p.$attrs).filter(([l]) => !o.value.includes(l) && !(i && jt.test(l))));
  }) : c(() => ({}));
};
function Wt(a) {
  let i;
  function n() {
    if (a.value == null)
      return;
    const { selectionStart: s, selectionEnd: p, value: l } = a.value;
    if (s == null || p == null)
      return;
    const u = l.slice(0, Math.max(0, s)), f = l.slice(Math.max(0, p));
    i = {
      selectionStart: s,
      selectionEnd: p,
      value: l,
      beforeTxt: u,
      afterTxt: f
    };
  }
  function o() {
    if (a.value == null || i == null)
      return;
    const { value: s } = a.value, { beforeTxt: p, afterTxt: l, selectionStart: u } = i;
    if (p == null || l == null || u == null)
      return;
    let f = s.length;
    if (s.endsWith(l))
      f = s.length - l.length;
    else if (s.startsWith(p))
      f = p.length;
    else {
      const S = p[u - 1], g = s.indexOf(S, u - 1);
      g !== -1 && (f = g + 1);
    }
    a.value.setSelectionRange(f, f);
  }
  return [n, o];
}
const Yt = "ElInput", Gt = Te({
  name: Yt,
  inheritAttrs: !1
}), Zt = /* @__PURE__ */ Te({
  ...Gt,
  props: At,
  emits: _t,
  setup(a, { expose: i, emit: n }) {
    const o = a, s = Xe(), p = Ut(), l = Qe(), u = c(() => [
      o.type === "textarea" ? se.b() : r.b(),
      r.m(k.value),
      r.is("disabled", E.value),
      r.is("exceed", $e.value),
      {
        [r.b("group")]: l.prepend || l.append,
        [r.m("prefix")]: l.prefix || o.prefixIcon,
        [r.m("suffix")]: l.suffix || o.suffixIcon || o.clearable || o.showPassword,
        [r.bm("suffix", "password-clear")]: A.value && G.value,
        [r.b("hidden")]: o.type === "hidden"
      },
      s.class
    ]), f = c(() => [
      r.e("wrapper"),
      r.is("focus", $.value)
    ]), { form: S, formItem: g } = Ct(), { inputId: h } = St(o, {
      formItemContext: g
    }), k = bt(), E = xt(), r = oe("input"), se = oe("textarea"), B = X(), w = X(), Y = U(!1), H = U(!1), le = U(), K = X(o.inputStyle), z = c(() => B.value || w.value), { wrapperRef: Le, isFocused: $, handleFocus: Re, handleBlur: Be } = wt(z, {
      beforeFocus() {
        return E.value;
      },
      afterBlur() {
        var e;
        o.validateEvent && ((e = g == null ? void 0 : g.validate) == null || e.call(g, "blur").catch((d) => Ee()));
      }
    }), re = c(() => {
      var e;
      return (e = S == null ? void 0 : S.statusIcon) != null ? e : !1;
    }), T = c(() => (g == null ? void 0 : g.validateState) || ""), ie = c(() => T.value && It[T.value]), He = c(() => H.value ? Et : zt), Ke = c(() => [
      s.style
    ]), ue = c(() => [
      o.inputStyle,
      K.value,
      { resize: o.resize }
    ]), I = c(() => Nt(o.modelValue) ? "" : String(o.modelValue)), A = c(() => o.clearable && !E.value && !o.readonly && !!I.value && ($.value || Y.value)), G = c(() => o.showPassword && !E.value && !!I.value && (!!I.value || $.value)), N = c(() => o.showWordLimit && !!o.maxlength && (o.type === "text" || o.type === "textarea") && !E.value && !o.readonly && !o.showPassword), Z = c(() => I.value.length), $e = c(() => !!N.value && Z.value > Number(o.maxlength)), Ae = c(() => !!l.suffix || !!o.suffixIcon || A.value || o.showPassword || N.value || !!T.value && re.value), [ce, de] = Wt(B);
    Pt(w, (e) => {
      if (_e(), !N.value || o.resize !== "both")
        return;
      const d = e[0], { width: P } = d.contentRect;
      le.value = {
        right: `calc(100% - ${P + 15 + 6}px)`
      };
    });
    const F = () => {
      const { type: e, autosize: d } = o;
      if (!(!Fe || e !== "textarea" || !w.value))
        if (d) {
          const P = ze(d) ? d.minRows : void 0, be = ze(d) ? d.maxRows : void 0, xe = ke(w.value, P, be);
          K.value = {
            overflowY: "hidden",
            ...xe
          }, M(() => {
            w.value.offsetHeight, K.value = xe;
          });
        } else
          K.value = {
            minHeight: ke(w.value).minHeight
          };
    }, _e = ((e) => {
      let d = !1;
      return () => {
        var P;
        if (d || !o.autosize)
          return;
        ((P = w.value) == null ? void 0 : P.offsetParent) === null || (e(), d = !0);
      };
    })(F), O = () => {
      const e = z.value, d = o.formatter ? o.formatter(I.value) : I.value;
      !e || e.value === d || (e.value = d);
    }, J = async (e) => {
      ce();
      let { value: d } = e.target;
      if (o.formatter && o.parser && (d = o.parser(d)), !fe.value) {
        if (d === I.value) {
          O();
          return;
        }
        n(ae, d), n(Ne, d), await M(), O(), de();
      }
    }, pe = (e) => {
      let { value: d } = e.target;
      o.formatter && o.parser && (d = o.parser(d)), n(Pe, d);
    }, {
      isComposing: fe,
      handleCompositionStart: ve,
      handleCompositionUpdate: me,
      handleCompositionEnd: ye
    } = Vt({ emit: n, afterComposition: J }), De = () => {
      ce(), H.value = !H.value, setTimeout(de);
    }, je = () => {
      var e;
      return (e = z.value) == null ? void 0 : e.focus();
    }, Ue = () => {
      var e;
      return (e = z.value) == null ? void 0 : e.blur();
    }, We = (e) => {
      Y.value = !1, n("mouseleave", e);
    }, Ye = (e) => {
      Y.value = !0, n("mouseenter", e);
    }, ge = (e) => {
      n("keydown", e);
    }, Ge = () => {
      var e;
      (e = z.value) == null || e.select();
    }, he = () => {
      n(ae, ""), n(Pe, ""), n("clear"), n(Ne, "");
    };
    return Q(() => o.modelValue, () => {
      var e;
      M(() => F()), o.validateEvent && ((e = g == null ? void 0 : g.validate) == null || e.call(g, "change").catch((d) => Ee()));
    }), Q(I, () => O()), Q(() => o.type, async () => {
      await M(), O(), F();
    }), qe(() => {
      !o.formatter && o.parser, O(), M(F);
    }), i({
      input: B,
      textarea: w,
      ref: z,
      textareaStyle: ue,
      autosize: et(o, "autosize"),
      isComposing: fe,
      focus: je,
      blur: Ue,
      select: Ge,
      clear: he,
      resizeTextarea: F
    }), (e, d) => (v(), x("div", {
      class: y([
        t(u),
        {
          [t(r).bm("group", "append")]: e.$slots.append,
          [t(r).bm("group", "prepend")]: e.$slots.prepend
        }
      ]),
      style: we(t(Ke)),
      onMouseenter: Ye,
      onMouseleave: We
    }, [
      m(" input "),
      e.type !== "textarea" ? (v(), x(q, { key: 0 }, [
        m(" prepend slot "),
        e.$slots.prepend ? (v(), x("div", {
          key: 0,
          class: y(t(r).be("group", "prepend"))
        }, [
          _(e.$slots, "prepend")
        ], 2)) : m("v-if", !0),
        V("div", {
          ref_key: "wrapperRef",
          ref: Le,
          class: y(t(f))
        }, [
          m(" prefix slot "),
          e.$slots.prefix || e.prefixIcon ? (v(), x("span", {
            key: 0,
            class: y(t(r).e("prefix"))
          }, [
            V("span", {
              class: y(t(r).e("prefix-inner"))
            }, [
              _(e.$slots, "prefix"),
              e.prefixIcon ? (v(), C(t(R), {
                key: 0,
                class: y(t(r).e("icon"))
              }, {
                default: L(() => [
                  (v(), C(D(e.prefixIcon)))
                ]),
                _: 1
              }, 8, ["class"])) : m("v-if", !0)
            ], 2)
          ], 2)) : m("v-if", !0),
          V("input", Ce({
            id: t(h),
            ref_key: "input",
            ref: B,
            class: t(r).e("inner")
          }, t(p), {
            minlength: e.minlength,
            maxlength: e.maxlength,
            type: e.showPassword ? H.value ? "text" : "password" : e.type,
            disabled: t(E),
            readonly: e.readonly,
            autocomplete: e.autocomplete,
            tabindex: e.tabindex,
            "aria-label": e.ariaLabel,
            placeholder: e.placeholder,
            style: e.inputStyle,
            form: e.form,
            autofocus: e.autofocus,
            role: e.containerRole,
            onCompositionstart: t(ve),
            onCompositionupdate: t(me),
            onCompositionend: t(ye),
            onInput: J,
            onChange: pe,
            onKeydown: ge
          }), null, 16, ["id", "minlength", "maxlength", "type", "disabled", "readonly", "autocomplete", "tabindex", "aria-label", "placeholder", "form", "autofocus", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend"]),
          m(" suffix slot "),
          t(Ae) ? (v(), x("span", {
            key: 1,
            class: y(t(r).e("suffix"))
          }, [
            V("span", {
              class: y(t(r).e("suffix-inner"))
            }, [
              !t(A) || !t(G) || !t(N) ? (v(), x(q, { key: 0 }, [
                _(e.$slots, "suffix"),
                e.suffixIcon ? (v(), C(t(R), {
                  key: 0,
                  class: y(t(r).e("icon"))
                }, {
                  default: L(() => [
                    (v(), C(D(e.suffixIcon)))
                  ]),
                  _: 1
                }, 8, ["class"])) : m("v-if", !0)
              ], 64)) : m("v-if", !0),
              t(A) ? (v(), C(t(R), {
                key: 1,
                class: y([t(r).e("icon"), t(r).e("clear")]),
                onMousedown: tt(t(kt), ["prevent"]),
                onClick: he
              }, {
                default: L(() => [
                  ot(t(Tt))
                ]),
                _: 1
              }, 8, ["class", "onMousedown"])) : m("v-if", !0),
              t(G) ? (v(), C(t(R), {
                key: 2,
                class: y([t(r).e("icon"), t(r).e("password")]),
                onClick: De
              }, {
                default: L(() => [
                  (v(), C(D(t(He))))
                ]),
                _: 1
              }, 8, ["class"])) : m("v-if", !0),
              t(N) ? (v(), x("span", {
                key: 3,
                class: y(t(r).e("count"))
              }, [
                V("span", {
                  class: y(t(r).e("count-inner"))
                }, j(t(Z)) + " / " + j(e.maxlength), 3)
              ], 2)) : m("v-if", !0),
              t(T) && t(ie) && t(re) ? (v(), C(t(R), {
                key: 4,
                class: y([
                  t(r).e("icon"),
                  t(r).e("validateIcon"),
                  t(r).is("loading", t(T) === "validating")
                ])
              }, {
                default: L(() => [
                  (v(), C(D(t(ie))))
                ]),
                _: 1
              }, 8, ["class"])) : m("v-if", !0)
            ], 2)
          ], 2)) : m("v-if", !0)
        ], 2),
        m(" append slot "),
        e.$slots.append ? (v(), x("div", {
          key: 1,
          class: y(t(r).be("group", "append"))
        }, [
          _(e.$slots, "append")
        ], 2)) : m("v-if", !0)
      ], 64)) : (v(), x(q, { key: 1 }, [
        m(" textarea "),
        V("textarea", Ce({
          id: t(h),
          ref_key: "textarea",
          ref: w,
          class: [t(se).e("inner"), t(r).is("focus", t($))]
        }, t(p), {
          minlength: e.minlength,
          maxlength: e.maxlength,
          tabindex: e.tabindex,
          disabled: t(E),
          readonly: e.readonly,
          autocomplete: e.autocomplete,
          style: t(ue),
          "aria-label": e.ariaLabel,
          placeholder: e.placeholder,
          form: e.form,
          autofocus: e.autofocus,
          rows: e.rows,
          role: e.containerRole,
          onCompositionstart: t(ve),
          onCompositionupdate: t(me),
          onCompositionend: t(ye),
          onInput: J,
          onFocus: t(Re),
          onBlur: t(Be),
          onChange: pe,
          onKeydown: ge
        }), null, 16, ["id", "minlength", "maxlength", "tabindex", "disabled", "readonly", "autocomplete", "aria-label", "placeholder", "form", "autofocus", "rows", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend", "onFocus", "onBlur"]),
        t(N) ? (v(), x("span", {
          key: 0,
          style: we(le.value),
          class: y(t(r).e("count"))
        }, j(t(Z)) + " / " + j(e.maxlength), 7)) : m("v-if", !0)
      ], 64))
    ], 38));
  }
});
var Jt = /* @__PURE__ */ ht(Zt, [["__file", "input.vue"]]);
const oo = Ft(Jt);
export {
  oo as E,
  Ut as a,
  to as b,
  eo as g,
  Me as u
};
