a:8:{s:6:"config";a:5:{s:10:"phpVersion";s:6:"5.6.40";s:10:"yiiVersion";s:5:"2.0.7";s:11:"application";a:4:{s:3:"yii";s:5:"2.0.7";s:4:"name";s:14:"My Application";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:4:{s:7:"version";s:6:"5.6.40";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;}s:10:"extensions";a:7:{s:24:"yiisoft/yii2-swiftmailer";a:3:{s:4:"name";s:24:"yiisoft/yii2-swiftmailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/swiftmailer";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-swiftmailer";}}s:24:"yiisoft/yii2-codeception";a:3:{s:4:"name";s:24:"yiisoft/yii2-codeception";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:16:"@yii/codeception";s:61:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-codeception";}}s:22:"yiisoft/yii2-bootstrap";a:3:{s:4:"name";s:22:"yiisoft/yii2-bootstrap";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:14:"@yii/bootstrap";s:59:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-bootstrap";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-debug";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:53:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-gii";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:10:"@yii/faker";s:55:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-faker";}}s:20:"yiisoft/yii2-mongodb";a:3:{s:4:"name";s:20:"yiisoft/yii2-mongodb";s:7:"version";s:5:"2.0.0";s:5:"alias";a:1:{s:12:"@yii/mongodb";s:57:"D:\integle2025\eln_5.3.11_dev\vendor/yiisoft/yii2-mongodb";}}}}s:7:"request";a:14:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:12:{s:4:"host";s:19:"dev.eln.integle.com";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:2:"48";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:6:"origin";s:26:"http://dev.eln.integle.com";s:7:"referer";s:27:"http://dev.eln.integle.com/";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:14:"zh-CN,zh;q=0.9";s:6:"cookie";s:258:"eln_page_limit=15; lock_interval=180; ldap_check=0; integle_session=f8vk9hvml0inf5jip4j452di63; group_ids=; department_ids=; role_ids=all; sims_u=7f2fa82b3d0ac8c728f119ebd3deffc7; user_ids=1148; selectedUsersNames=cqtest1(cq1); last_active_time=**********350";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:10:"PHP/5.6.40";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:62:"no-store, no-cache, must-revalidate, post-check=0, pre-check=0";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=utf-8";}s:5:"route";s:17:"instrument/manage";s:6:"action";s:57:"frontend\controllers\InstrumentController::actionManage()";s:12:"actionParams";a:0:{}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:48:"needUpdateAllPage=1&type=my_instruments&limit=15";s:17:"Decoded to Params";a:3:{s:17:"needUpdateAllPage";s:1:"1";s:4:"type";s:14:"my_instruments";s:5:"limit";s:2:"15";}}s:6:"SERVER";a:45:{s:7:"MIBDIRS";s:24:"D:/xampp/php/extras/mibs";s:10:"MYSQL_HOME";s:16:"\xampp\mysql\bin";s:12:"OPENSSL_CONF";s:31:"D:/xampp/apache/bin/openssl.cnf";s:20:"PHP_PEAR_SYSCONF_DIR";s:10:"\xampp\php";s:5:"PHPRC";s:10:"\xampp\php";s:3:"TMP";s:10:"\xampp\tmp";s:9:"HTTP_HOST";s:19:"dev.eln.integle.com";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:2:"48";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:46:"application/json, text/javascript, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:11:"HTTP_ORIGIN";s:26:"http://dev.eln.integle.com";s:12:"HTTP_REFERER";s:27:"http://dev.eln.integle.com/";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"zh-CN,zh;q=0.9";s:11:"HTTP_COOKIE";s:258:"eln_page_limit=15; lock_interval=180; ldap_check=0; integle_session=f8vk9hvml0inf5jip4j452di63; group_ids=; department_ids=; role_ids=all; sims_u=7f2fa82b3d0ac8c728f119ebd3deffc7; user_ids=1148; selectedUsersNames=cqtest1(cq1); last_active_time=**********350";s:4:"PATH";s:1123:"C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\PerkinElmerInformatics\ChemOffice2017\ChemScript\Lib;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\DSOC\ExtractContent;C:\Program Files (x86)\DSOC\ExtractContent64\OCR;D:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk-1.8\bin;D:\Program Files\php\php-5.6.40-Win32-VC11-x64;D:\composer;D:\Program Files\Git\cmd;D:\Program Files\nodejs\node_global\node_modules;D:\nvm;D:\nvm4w\nodejs;D:\Program Files\nodejs\node_global;D:\Program Files\wget-1.21.4-win64;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2024.1.4\bin;;D:\Program Files\JetBrains\PhpStorm 2024.1.4\bin;;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;D:\Program Files\JetBrains\WebStorm 2024.1.5\bin;;D:\Users\chenc\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\cursor\resources\app\bin";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:105:"<address>Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40 Server at dev.eln.integle.com Port 80</address>
";s:15:"SERVER_SOFTWARE";s:47:"Apache/2.4.38 (Win64) OpenSSL/1.0.2q PHP/5.6.40";s:11:"SERVER_NAME";s:19:"dev.eln.integle.com";s:11:"SERVER_ADDR";s:3:"::1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:3:"::1";s:13:"DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:42:"D:/integle2025/eln_5.3.11_dev/frontend/web";s:12:"SERVER_ADMIN";s:20:"postmaster@localhost";s:15:"SCRIPT_FILENAME";s:52:"D:/integle2025/eln_5.3.11_dev/frontend/web/index.php";s:11:"REMOTE_PORT";s:5:"63189";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:19:"r=instrument/manage";s:11:"REQUEST_URI";s:21:"/?r=instrument/manage";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.47;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:17:"instrument/manage";}s:4:"POST";a:3:{s:17:"needUpdateAllPage";s:1:"1";s:4:"type";s:14:"my_instruments";s:5:"limit";s:2:"15";}s:6:"COOKIE";a:11:{s:14:"eln_page_limit";s:2:"15";s:13:"lock_interval";s:3:"180";s:10:"ldap_check";s:1:"0";s:15:"integle_session";s:26:"f8vk9hvml0inf5jip4j452di63";s:9:"group_ids";s:0:"";s:14:"department_ids";s:0:"";s:8:"role_ids";s:3:"all";s:6:"sims_u";s:32:"7f2fa82b3d0ac8c728f119ebd3deffc7";s:8:"user_ids";s:4:"1148";s:18:"selectedUsersNames";s:12:"cqtest1(cq1)";s:16:"last_active_time";s:13:"**********350";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:8:"userinfo";a:35:{s:7:"user_id";s:4:"1148";s:5:"email";N;s:4:"name";s:3:"cq1";s:5:"phone";N;s:6:"ticket";s:32:"7f2fa82b3d0ac8c728f119ebd3deffc7";s:8:"reg_time";s:10:"1744270466";s:5:"Token";s:32:"65df2c5aec757b20d496ba82fc315e25";s:13:"register_from";s:0:"";s:9:"from_ldap";s:1:"0";s:6:"gender";s:1:"0";s:9:"nick_name";s:0:"";s:13:"contact_phone";s:0:"";s:9:"real_name";s:7:"cqtest1";s:5:"point";s:1:"0";s:12:"company_name";s:0:"";s:3:"job";s:0:"";s:12:"office_phone";s:0:"";s:2:"qq";s:0:"";s:7:"country";s:0:"";s:8:"province";s:0:"";s:4:"city";s:0:"";s:14:"detail_address";s:0:"";s:9:"post_code";s:0:"";s:7:"id_card";s:0:"";s:7:"big_img";s:0:"";s:9:"small_img";s:0:"";s:14:"unread_message";s:1:"0";s:13:"default_group";s:1:"0";s:13:"contact_email";s:0:"";s:8:"role_ids";s:4:"1,85";s:10:"department";a:1:{i:0;a:22:{s:2:"id";s:3:"381";s:13:"department_id";s:3:"381";s:7:"user_id";s:4:"1148";s:6:"status";s:1:"1";s:11:"create_time";s:19:"2025-04-10 09:41:02";s:11:"update_time";s:19:"2025-04-10 15:41:03";s:10:"company_id";s:1:"0";s:15:"department_name";s:8:"CQ部门";s:15:"department_code";s:19:"2.1777226530103E+18";s:15:"department_type";s:1:"1";s:6:"tel_no";s:0:"";s:6:"fax_no";s:0:"";s:18:"department_address";s:0:"";s:13:"department_no";s:1:"0";s:17:"parent_department";s:1:"0";s:7:"manager";s:4:"1135";s:11:"leader_main";s:0:"";s:13:"leader_branch";s:0:"";s:9:"assistant";s:4:"1148";s:14:"branch_manager";s:0:"";s:6:"remark";N;s:7:"ad_guid";N;}}s:2:"id";s:4:"1148";s:6:"groups";a:2:{i:0;a:3:{s:2:"id";s:1:"1";s:4:"name";s:9:"公司群";s:4:"role";s:1:"1";}i:1;a:3:{s:2:"id";s:3:"598";s:4:"name";s:3:"cq1";s:4:"role";s:1:"1";}}s:18:"current_company_id";s:1:"1";s:10:"app_access";i:1;}s:8:"eln_lang";s:5:"zh-CN";}}s:3:"log";a:1:{s:8:"messages";a:56:{i:0;a:5:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.488189;i:4;a:0:{}}i:1;a:5:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.4959469;i:4;a:0:{}}i:2;a:5:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.6339459;i:4;a:0:{}}i:3;a:5:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.6351421;i:4;a:0:{}}i:4;a:5:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.6363311;i:4;a:0:{}}i:5;a:5:{i:0;s:36:"Route requested: 'instrument/manage'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.6363499;i:4;a:0:{}}i:6;a:5:{i:0;s:103:"请求数据为:{"r":"instrument\/manage","needUpdateAllPage":"1","type":"my_instruments","limit":"15"}";i:1;i:4;i:2;s:7:"Request";i:3;d:**********.7250879;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:132;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}}i:7;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.886409;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:8;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.8948679;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:9;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.8954091;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:10;a:5:{i:0;s:28:"Executing Redis Command: GET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.89573;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:11;a:5:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.896137;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:340;s:8:"function";s:4:"open";s:5:"class";s:15:"yii\web\Session";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:137;s:8:"function";s:8:"_account";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:12;a:5:{i:0;s:29:"处理后的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.8961849;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\controllers\Common.php";s:4:"line";i:454;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:397;s:8:"function";s:12:"_setBaseLang";s:5:"class";s:25:"common\controllers\Common";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:13;a:5:{i:0;s:32:"系统设置的语言为：zh-CN";i:1;i:4;i:2;s:11:"application";i:3;d:**********.8964419;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:403;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:140;s:8:"function";s:8:"_setLang";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:14;a:5:{i:0;s:66:"Opening redis DB connection: redis.db.integle.com:6379, database=0";i:1;i:8;i:2;s:26:"yii\redis\Connection::open";i:3;d:**********.8972931;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:15;a:5:{i:0;s:29:"Executing Redis Command: AUTH";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9106729;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:16;a:5:{i:0;s:31:"Executing Redis Command: SELECT";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9110911;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:17;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9114399;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:257;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:67:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\MyController.php";s:4:"line";i:165;s:8:"function";s:9:"_initAuth";s:5:"class";s:33:"frontend\controllers\MyController";s:4:"type";s:2:"->";}}}i:18;a:5:{i:0;s:31:"Route to run: instrument/manage";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.912761;i:4;a:0:{}}i:19;a:5:{i:0;s:73:"Running action: frontend\controllers\InstrumentController::actionManage()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.9775431;i:4;a:0:{}}i:20;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9776011;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1480;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1480;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1457;s:8:"function";s:24:"getVisibleGroupsWithTags";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:21;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.983309;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1431;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1431;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:61;s:8:"function";s:21:"getVisibleDepartments";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:22;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9879749;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1386;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1386;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:66;s:8:"function";s:15:"getVisibleUsers";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:23;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.992866;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:70;s:8:"function";s:24:"getGroupsListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:24;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:**********.9986191;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1803;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1803;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:71;s:8:"function";s:28:"getDepartmentListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:25;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.0187819;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:28;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0415659;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:31;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.042572;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:34;a:5:{i:0;s:83:"SELECT `id` FROM `instruments` WHERE (`check_situation`=1) OR (`check_situation`=2)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.043426;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:35;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.0434439;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:40;a:5:{i:0;s:1691:"SELECT COUNT(*) FROM (SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id`) `c`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.060113;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1101;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:43;a:5:{i:0;s:1707:"SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id` ORDER BY `Ins`.`create_time` DESC LIMIT 15";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.089313;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:46;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1116409;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:49;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1164269;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:52;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.285306;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1129;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:53;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2901471;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1247;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:56;a:5:{i:0;s:97:"SELECT * FROM `company_dict_value` WHERE (`company_id`='1') AND (`parent_id`=52) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2937329;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\integle2025\eln_5.3.11_dev\frontend\services\CollaborationServer.php";s:4:"line";i:414;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1250;s:8:"function";s:11:"getTypeList";s:5:"class";s:37:"frontend\services\CollaborationServer";s:4:"type";s:2:"->";}}}i:59;a:5:{i:0;s:87:"SELECT `field_config` FROM `instrument_define_fields` WHERE (`type`=2) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2947869;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5341;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:62;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2953861;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5344;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:65;a:5:{i:0;s:96:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views\setting/instruments_manage.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3001449;i:4;a:1:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:66;a:5:{i:0;s:105:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/visible_user_select_tree.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.301681;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:340;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:67;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.302053;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:84:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\visible_user_select_tree.php";s:4:"line";i:39;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:68;a:5:{i:0;s:105:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/visible_user_select_tree.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.305743;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:372;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:69;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.306675;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:84:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\visible_user_select_tree.php";s:4:"line";i:39;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:70;a:5:{i:0;s:99:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/multi_group_select.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3104539;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:387;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:71;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.312613;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1289;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:78:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\multi_group_select.php";s:4:"line";i:22;s:8:"function";s:24:"getGroupsListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:72;a:5:{i:0;s:105:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/visible_user_select_tree.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3187871;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:402;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:73;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.3191791;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:84:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\visible_user_select_tree.php";s:4:"line";i:39;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:74;a:5:{i:0;s:105:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/visible_user_select_tree.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3226521;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:425;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:75;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.323065;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1216;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:84:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\visible_user_select_tree.php";s:4:"line";i:39;s:8:"function";s:22:"getUserListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:76;a:5:{i:0;s:105:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/components/visible_dept_select_tree.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3269069;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:443;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:77;a:5:{i:0;s:28:"Executing Redis Command: get";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.3273129;i:4;a:3:{i:0;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1803;s:8:"function";s:6:"__call";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"D:\integle2025\eln_5.3.11_dev\frontend\interfaces\CenterInterface.php";s:4:"line";i:1803;s:8:"function";s:3:"get";s:5:"class";s:20:"yii\redis\Connection";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:84:"D:\integle2025\eln_5.3.11_dev\frontend\views\components\visible_dept_select_tree.php";s:4:"line";i:27;s:8:"function";s:28:"getDepartmentListByCompanyId";s:5:"class";s:35:"frontend\interfaces\CenterInterface";s:4:"type";s:2:"->";}}}i:78;a:5:{i:0;s:101:"Rendering view file: D:\integle2025\eln_5.3.11_dev\frontend\views/setting/instruments_manage_page.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1749526130.3294981;i:4;a:2:{i:0;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:532;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:157;s:8:"function";s:10:"renderAjax";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}}i:79;a:5:{i:0;s:44:"Picture[common\components] Class Initialized";i:1;i:8;i:2;s:11:"application";i:3;d:1749526130.331079;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Picture.php";s:4:"line";i:58;s:8:"function";s:5:"trace";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage_page.php";s:4:"line";i:430;s:8:"function";s:11:"__construct";s:5:"class";s:25:"common\components\Picture";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:532;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}}i:80;a:5:{i:0;s:44:"Picture[common\components] Class Initialized";i:1;i:8;i:2;s:11:"application";i:3;d:1749526130.331351;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Picture.php";s:4:"line";i:58;s:8:"function";s:5:"trace";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage_page.php";s:4:"line";i:430;s:8:"function";s:11:"__construct";s:5:"class";s:25:"common\components\Picture";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\views\setting\instruments_manage.php";s:4:"line";i:532;s:8:"function";s:10:"renderFile";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}}i:81;a:5:{i:0;s:28:"Executing Redis Command: SET";i:1;i:8;i:2;s:36:"yii\redis\Connection::executeCommand";i:3;d:1749526130.333688;i:4;a:0:{}}}}s:9:"profiling";a:3:{s:6:"memory";i:28934088;s:4:"time";d:0.9234011173248291;s:8:"messages";a:26:{i:26;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.0188229;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:27;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.0415051;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:29;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0415959;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0424199;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.042594;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0433061;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:36;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.0434611;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:37;a:5:{i:0;s:85:"Opening DB connection: mysql:host=ineln.db.integle.com;port=3306;dbname=integle_ineln";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1749526130.057502;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:83:"SELECT `id` FROM `instruments` WHERE (`check_situation`=1) OR (`check_situation`=2)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.05755;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:83:"SELECT `id` FROM `instruments` WHERE (`check_situation`=1) OR (`check_situation`=2)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0594449;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:1691:"SELECT COUNT(*) FROM (SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id`) `c`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.060143;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1101;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:1691:"SELECT COUNT(*) FROM (SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id`) `c`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0883729;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1101;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:1707:"SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id` ORDER BY `Ins`.`create_time` DESC LIMIT 15";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0893781;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:1707:"SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id` ORDER BY `Ins`.`create_time` DESC LIMIT 15";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.109906;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.111753;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1138201;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:50;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.116477;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:51;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1171091;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2901771;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1247;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.291013;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1247;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:97:"SELECT * FROM `company_dict_value` WHERE (`company_id`='1') AND (`parent_id`=52) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.293756;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\integle2025\eln_5.3.11_dev\frontend\services\CollaborationServer.php";s:4:"line";i:414;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1250;s:8:"function";s:11:"getTypeList";s:5:"class";s:37:"frontend\services\CollaborationServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:97:"SELECT * FROM `company_dict_value` WHERE (`company_id`='1') AND (`parent_id`=52) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2945089;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\integle2025\eln_5.3.11_dev\frontend\services\CollaborationServer.php";s:4:"line";i:414;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1250;s:8:"function";s:11:"getTypeList";s:5:"class";s:37:"frontend\services\CollaborationServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:87:"SELECT `field_config` FROM `instrument_define_fields` WHERE (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.294805;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5341;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:61;a:5:{i:0;s:87:"SELECT `field_config` FROM `instrument_define_fields` WHERE (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.295321;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5341;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2954021;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5344;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.295996;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5344;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:2:"db";a:1:{s:8:"messages";a:22:{i:29;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0415959;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:30;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0424199;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1012;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:32;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.042594;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:33;a:5:{i:0;s:270:"SELECT `instrument_id` FROM `instrument_check_record` WHERE (((`if_end`=1) AND (`if_void`=0) AND (`status`=1)) AND ((`review_conclusion` IS NULL) OR (`review_conclusion`=1))) AND ((`start_expiry_time` <= '2025-06-10 11:28') AND (`end_expiry_time` >= '2025-06-10 11:28'))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0433061;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6867;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:38;a:5:{i:0;s:83:"SELECT `id` FROM `instruments` WHERE (`check_situation`=1) OR (`check_situation`=2)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.05755;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:39;a:5:{i:0;s:83:"SELECT `id` FROM `instruments` WHERE (`check_situation`=1) OR (`check_situation`=2)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0594449;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:6891;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1014;s:8:"function";s:14:"ifInExpiryTime";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:41;a:5:{i:0;s:1691:"SELECT COUNT(*) FROM (SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id`) `c`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.060143;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1101;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:42;a:5:{i:0;s:1691:"SELECT COUNT(*) FROM (SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id`) `c`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0883729;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1101;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:44;a:5:{i:0;s:1707:"SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id` ORDER BY `Ins`.`create_time` DESC LIMIT 15";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.0893781;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:45;a:5:{i:0;s:1707:"SELECT `Ins`.`id`, `Ins`.`name` AS `name`, `Ins`.`batch_number` AS `batch_number`, `Ins`.`specification` AS `specification`, `Ins`.`instrument_type`, `Ins`.`model` AS `model`, `Ins`.`manufacturer` AS `manufacturer`, `Ins`.`position` AS `position`, `Ins`.`responsible_person`, `Ins`.`in_charge_person`, `Ins`.`maintenance_person`, `Ins`.`supplier` AS `supplier`, `Ins`.`remark` AS `remark`, `Ins`.`files`, `Ins`.`pictures`, `Ins`.`check_situation`, `Ins`.`start_expiry_time`, `Ins`.`end_expiry_time`, `Ins`.`start_check_time`, `Ins`.`end_check_time`, `Ins`.`start_running_time`, `Ins`.`end_running_time`, `Ins`.`repair_start_time`, `Ins`.`repair_end_time`, `Ins`.`create_by`, `Ins`.`create_time`, `Ins`.`status`, `Ins`.`data_type`, `Ins`.`numerical_instrument_type`, `Ins`.`groupIds`, `Ins`.`departmentIds`, `Ins`.`available_slots`, `Ins`.`max_advance_day`, `Ins`.`min_advance`, `Ins`.`max_booking_duration`, `Ins`.`data1` AS `data1`, `Ins`.`data2` AS `data2`, `Ins`.`data3` AS `data3`, `Ins`.`data4` AS `data4`, `Ins`.`data5` AS `data5`, `Ins`.`data6` AS `data6`, `Ins`.`data7` AS `data7`, `Ins`.`data8` AS `data8`, `Ins`.`data9` AS `data9`, `Ins`.`data10` AS `data10`, `Ins`.`data11` AS `data11`, `Ins`.`data12` AS `data12`, `Ins`.`data13` AS `data13`, `Ins`.`data14` AS `data14`, `Ins`.`data15` AS `data15`, `Ins`.`data16` AS `data16`, `Ins`.`data17` AS `data17`, `Ins`.`data18` AS `data18`, `Ins`.`data19` AS `data19`, `Ins`.`data20` AS `data20`, `ib`.`status` AS `instrument_binding_status` FROM `instruments` `Ins` LEFT JOIN `instrument_binding` `ib` ON ib.batch_number=Ins.batch_number AND ib.status=1 WHERE `Ins`.`status` IN (1, 2, 3, 4) GROUP BY `Ins`.`id` ORDER BY `Ins`.`create_time` DESC LIMIT 15";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.109906;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:47;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.111753;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:48;a:5:{i:0;s:36:"SHOW FULL COLUMNS FROM `instruments`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1138201;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:50;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.116477;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:51;a:5:{i:0;s:609:"SELECT
    kcu.constraint_name,
    kcu.column_name,
    kcu.referenced_table_name,
    kcu.referenced_column_name
FROM information_schema.referential_constraints AS rc
JOIN information_schema.key_column_usage AS kcu ON
    (
        kcu.constraint_catalog = rc.constraint_catalog OR
        (kcu.constraint_catalog IS NULL AND rc.constraint_catalog IS NULL)
    ) AND
    kcu.constraint_schema = rc.constraint_schema AND
    kcu.constraint_name = rc.constraint_name
WHERE rc.constraint_schema = database() AND kcu.table_schema = database()
AND rc.table_name = 'instruments' AND kcu.table_name = 'instruments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.1171091;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1121;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:54;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2901771;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1247;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:55;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.291013;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1247;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:130;s:8:"function";s:19:"listInstrumentsView";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:57;a:5:{i:0;s:97:"SELECT * FROM `company_dict_value` WHERE (`company_id`='1') AND (`parent_id`=52) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.293756;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\integle2025\eln_5.3.11_dev\frontend\services\CollaborationServer.php";s:4:"line";i:414;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1250;s:8:"function";s:11:"getTypeList";s:5:"class";s:37:"frontend\services\CollaborationServer";s:4:"type";s:2:"->";}}}i:58;a:5:{i:0;s:97:"SELECT * FROM `company_dict_value` WHERE (`company_id`='1') AND (`parent_id`=52) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2945089;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\integle2025\eln_5.3.11_dev\frontend\services\CollaborationServer.php";s:4:"line";i:414;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:1250;s:8:"function";s:11:"getTypeList";s:5:"class";s:37:"frontend\services\CollaborationServer";s:4:"type";s:2:"->";}}}i:60;a:5:{i:0;s:87:"SELECT `field_config` FROM `instrument_define_fields` WHERE (`type`=2) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.294805;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5341;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:61;a:5:{i:0;s:87:"SELECT `field_config` FROM `instrument_define_fields` WHERE (`type`=2) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.295321;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5341;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:63;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.2954021;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5344;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}i:64;a:5:{i:0;s:74:"SELECT * FROM `instrument_define_fields` WHERE (`type`=1) AND (`status`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1749526130.295996;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"D:\integle2025\eln_5.3.11_dev\common\components\Command.php";s:4:"line";i:53;s:8:"function";s:13:"queryInternal";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:68:"D:\integle2025\eln_5.3.11_dev\frontend\services\InstrumentServer.php";s:4:"line";i:5344;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:75:"D:\integle2025\eln_5.3.11_dev\frontend\controllers\InstrumentController.php";s:4:"line";i:148;s:8:"function";s:25:"viewInstrumentFieldConfig";s:5:"class";s:34:"frontend\services\InstrumentServer";s:4:"type";s:2:"->";}}}}}s:6:"assets";a:0:{}s:4:"mail";a:0:{}s:7:"summary";a:9:{s:3:"tag";s:13:"6847a6719a9e3";s:3:"url";s:47:"http://dev.eln.integle.com/?r=instrument/manage";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";i:1749526130;s:10:"statusCode";i:200;s:8:"sqlCount";i:11;s:9:"mailCount";i:0;}}